// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = "1.8.20"
    ext.compileSdkVersion = 34
    ext.minSdkVersion = 29
    ext.targetSdkVersion = 29
    ext.objectboxVersion = "3.8.0"
    ext.koin_version= "3.2.2"
    ext.koin_android_version= "3.3.0"
    ext.koin_android_compose_version= "3.3.0"

    repositories {
        google()
        mavenCentral()
        jcenter()
    }

    dependencies {
        classpath "com.android.tools.build:gradle:8.2.2"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath("io.objectbox:objectbox-gradle-plugin:$objectboxVersion")

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        maven { url 'https://repo1.maven.org/maven2/' }
        maven { url "https://jitpack.io" }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}