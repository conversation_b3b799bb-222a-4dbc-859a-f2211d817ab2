plugins {
    id 'com.android.application'
    id 'kotlin-android'
//    id 'kotlin-android-extensions'
    id 'kotlin-kapt'
    id("io.objectbox") // Apply last.
}

android {
    compileSdk rootProject.compileSdkVersion
    namespace "com.google.chuangke"
    signingConfigs {
        debug {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            v1SigningEnabled true
            v2SigningEnabled true
        }

        release {
            storeFile file('debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    defaultConfig {
        applicationId "com.test.liveultra"
        minSdk rootProject.minSdkVersion
        targetSdk rootProject.targetSdkVersion
        versionCode 118
        versionName "1.1.8"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            zipAlignEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
            minifyEnabled true
            shrinkResources true
            zipAlignEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            ndk {
                abiFilters "armeabi-v7a"
            }
            signingConfig signingConfigs.debug
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    buildFeatures {
        viewBinding true
        buildConfig = true
        aidl true
    }
    android.applicationVariants.all { variant ->
        variant.outputs.all {
            //在这里修改apk文件名
            outputFileName = "${rootProject.name}-test-v${variant.versionName}.apk"
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar', '*.aar'])

    implementation 'androidx.appcompat:appcompat:1.5.1'
    implementation "androidx.leanback:leanback:1.0.0"
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "com.jakewharton.threetenabp:threetenabp:1.3.1"

    implementation 'androidx.core:core-ktx:1.8.0'
    implementation 'com.google.android.material:material:1.6.1'
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1"

    // Screen size
    implementation 'com.github.JessYanCoding:AndroidAutoSize:v1.2.1'
    //Logger
    implementation 'com.orhanobut:logger:2.2.0'
    // adapter
    implementation 'com.github.CymChad:BaseRecyclerViewAdapterHelper:3.0.6'

    def glideVersion = '4.15.1'

    // image load and display
    implementation "com.github.bumptech.glide:glide:$glideVersion"
    kapt "com.github.bumptech.glide:compiler:$glideVersion"
    implementation ("com.github.bumptech.glide:okhttp3-integration:$glideVersion"){
        exclude group: 'glide-parent'
    }

    implementation 'org.apache.commons:commons-lang3:3.7'

    // Koin for Kotlin
    implementation "io.insert-koin:koin-core:$koin_version"
    // Koin main features for Android
    implementation "io.insert-koin:koin-android:$koin_android_version"
    // Java Compatibility
    implementation "io.insert-koin:koin-android-compat:$koin_android_version"
    // Jetpack Compose
    implementation "io.insert-koin:koin-androidx-compose:$koin_android_compose_version"

    // https://github.com/greenrobot/EventBus
    implementation("org.greenrobot:eventbus:3.3.1")
    // https://github.com/alibaba/fastjson
    implementation 'com.alibaba:fastjson:1.1.72.android'
    // https://github.com/square/okhttp
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    implementation("androidx.viewpager2:viewpager2:1.0.0")

    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation 'io.reactivex.rxjava3:rxjava:3.0.6'

    //https://developer.android.com/reference/android/arch/lifecycle/ProcessLifecycleOwner
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'

    // 加载动画
//    implementation 'com.ldoublem.loadingview:loadingviewlib:1.0'
//    implementation 'com.wang.avi:library:2.1.3'

    // voice wave
    implementation('com.github.xfans:VoiceWaveView:1.0.2') {
        exclude group: 'com.android.support', module: 'appcompat-v7'
    }

    implementation("androidx.lifecycle:lifecycle-service:2.6.2")

    implementation 'com.google.guava:guava:33.0.0-android'

}