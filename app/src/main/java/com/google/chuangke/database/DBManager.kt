package com.google.chuangke.database

import android.annotation.SuppressLint
import android.database.sqlite.SQLiteDatabase
import com.google.chuangke.MyApplication
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.entity.*
import com.google.chuangke.util.FileUtil
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import java.io.File

class DBManager {

    private var mDirectvDB: SQLiteDatabase? = null
    private var mOtherDB: SQLiteDatabase? = null
    private var mEpgDB: SQLiteDatabase? = null
    private var mVodDB: SQLiteDatabase? = null

    private fun openDirectvDB(dbPath: String) {
        mDirectvDB = SQLiteDatabase.openDatabase(dbPath, null, SQLiteDatabase.OPEN_READONLY)
    }

    /**
     * 打开Other数据库
     */
    private fun openOtherDB() {
        mOtherDB = SQLiteDatabase.openDatabase(
            dbOtherPath, null, SQLiteDatabase.OPEN_READONLY
        )
    }

    /**
     * 打开Epg数据库
     */
    private fun openEpgDB(dbPath: String) {
        mEpgDB = SQLiteDatabase.openDatabase(dbPath, null, SQLiteDatabase.OPEN_READONLY)
    }

    fun deleteOtherDB() {
        SQLiteDatabase.deleteDatabase(File(dbOtherPath))
    }

    fun deleteEpgDB() {
        SQLiteDatabase.deleteDatabase(File(dbEpgPath))
    }

    fun deleteDirectvDB() {
        SQLiteDatabase.deleteDatabase(File(dbDirectvPath))
    }

    @SuppressLint("Range")
    fun queryOther(dbPath: String): Map<String, List<*>> {
        val result: MutableMap<String, List<*>> = HashMap()
        val tags = mutableListOf<TagBean>()
        val channels = mutableListOf<ChannelBean>()
        openDirectvDB(dbPath)
        try {
            var sql = " select `id`,`number`,name,tags,intOrder,image,dma from channel;"
            var cursor = mDirectvDB!!.rawQuery(sql, null)
            while (cursor.moveToNext()) {
                val id = cursor.getLong(cursor.getColumnIndex("id"))
                val name = cursor.getString(cursor.getColumnIndex("name"))
                val channelNumber = cursor.getInt(cursor.getColumnIndex("number"))
                val genre = cursor.getString(cursor.getColumnIndex("tags"))
                val intOrder = cursor.getInt(cursor.getColumnIndex("intOrder"))
                val image = cursor.getString(cursor.getColumnIndex("image"))
                val dma = cursor.getString(cursor.getColumnIndex("dma"))
                val channelBean = ChannelBean()
                channelBean.id = id
                channelBean.channelId = id
                channelBean.channelNumber = channelNumber
                channelBean.intOrder = intOrder
                channelBean.name = name
                channelBean.tags = genre
                channelBean.image = image
                channelBean.dma = dma
                channels.add(channelBean)
            }
            cursor.close()

            val sqlTag = "select id,name,intOrder from tag;"
            val cursorTag = mDirectvDB!!.rawQuery(sqlTag, null)
            while (cursorTag.moveToNext()) {
                val id = cursorTag.getLong(cursorTag.getColumnIndex("id"))
                val name = cursorTag.getString(cursorTag.getColumnIndex("name"))
                val intOrder = cursorTag.getInt(cursorTag.getColumnIndex("intOrder"))
                val tagBean = TagBean(id, name, intOrder, 0)
                tags.add(tagBean)
            }
            cursorTag.close()

            result["tags"] = tags
            result["channels"] = channels
            return result
        } catch (e: Exception) {
            SPUtils.putString(MyApplication.context, Constants.DB_NAME_OTHER.hashCode().toString(), "")
            Logger.e(e.message.toString())
        } finally {
            mDirectvDB?.close()
        }
        return result
    }

    @SuppressLint("Range")
    fun queryTag(): List<TagBean?>? {
        val tagList: MutableList<TagBean?> = mutableListOf()
        openOtherDB()
        try {
            val sql = " select a.id,a.name,a.intOrder,a.passwordAccess from tag a"
            val cursor = mOtherDB!!.rawQuery(sql, null)
            while (cursor.moveToNext()) {
                val id = cursor.getLong(cursor.getColumnIndex("id"))
                val name = cursor.getString(cursor.getColumnIndex("name"))
                val intOrder = cursor.getInt(cursor.getColumnIndex("intOrder"))
                val passwordAccess = cursor.getInt(cursor.getColumnIndex("passwordAccess"))
                tagList.add(TagBean(id, name, intOrder, passwordAccess))
            }
            cursor.close()
            return tagList
        } catch (e: Exception) {
            SPUtils.putString(MyApplication.context, "other.db.zip", "")
            e.printStackTrace()
        } finally {
            mOtherDB?.close()
        }
        return null
    }

    @SuppressLint("Range")
    fun queryChannel(): List<ChannelBean?>? {
        val channelList: MutableList<ChannelBean?> = mutableListOf()
        openOtherDB()
        try {
            val sql =
                " select a.unid,a.id,a.name,a.channelNumber,a.tags,a.intOrder,a.playback,a.countryCode,a.uid,a.wid from channel a"
            val cursor = mOtherDB!!.rawQuery(sql, null)
            while (cursor.moveToNext()) {
                val unid = cursor.getLong(cursor.getColumnIndex("unid"))
                val id = cursor.getLong(cursor.getColumnIndex("id"))
                val name = cursor.getString(cursor.getColumnIndex("name"))
                val channelNumber = cursor.getInt(cursor.getColumnIndex("channelNumber"))
                val tags = cursor.getString(cursor.getColumnIndex("tags"))
                val intOrder = cursor.getInt(cursor.getColumnIndex("intOrder"))
                val playback = cursor.getInt(cursor.getColumnIndex("playback"))
                val countryCode = cursor.getString(cursor.getColumnIndex("countryCode"))
                val uid = cursor.getString(cursor.getColumnIndex("uid"))
                val wid = cursor.getString(cursor.getColumnIndex("wid"))

                val channelBean = ChannelBean()
                channelBean.id = unid
                channelBean.channelId = id
                channelBean.name = name
                channelBean.channelNumber = channelNumber
                channelBean.tags = tags
                channelBean.intOrder = intOrder
                channelBean.playback = playback
                channelBean.countryCode = countryCode
                channelBean.uid = uid
                channelBean.wid = wid

                channelList.add(channelBean)
            }
            cursor.close()
            return channelList
        } catch (e: Exception) {
            SPUtils.putString(MyApplication.context, "other.db.zip", "")
            e.printStackTrace()
        } finally {
            mOtherDB?.close()
        }
        return null
    }

    @SuppressLint("Range")
    fun queryEpg(dbPath: String): List<EpgBean?>? {
        val channelList: MutableList<EpgBean?> = mutableListOf()
        openEpgDB(dbPath)
        try {
            val sql =
                " select a.id,a.channelId,a.name,a.description,a.beginTime,a.endTime from epg a"
            val cursor = mEpgDB!!.rawQuery(sql, null)
            while (cursor.moveToNext()) {
                val id = cursor.getLong(cursor.getColumnIndex("id"))
                val channelId = cursor.getInt(cursor.getColumnIndex("channelId"))
                val name = cursor.getString(cursor.getColumnIndex("name"))
                val description = cursor.getString(cursor.getColumnIndex("description"))
                val beginTime = cursor.getInt(cursor.getColumnIndex("beginTime"))
                val endTime = cursor.getInt(cursor.getColumnIndex("endTime"))

                val epgBean = EpgBean()
                epgBean.id = id
                epgBean.channelId = channelId
                epgBean.name = name
                epgBean.description = description
                epgBean.beginTime = beginTime
                epgBean.endTime = endTime

                channelList.add(epgBean)
            }
            cursor.close()
            return channelList
        } catch (e: Exception) {
            SPUtils.putString(MyApplication.context, Constants.DB_NAME_EPG.hashCode().toString(), "")
            Logger.e(e.message.toString())
        } finally {
            mEpgDB?.close()
        }
        return null
    }

    companion object {

        private val dbDirectvPath: String =
            MyApplication.context.getDatabasePath(Constants.DATABASE_DCHANNEL).path

        private val dbOtherPath: String =
            MyApplication.context.getDatabasePath(Constants.DATABASE_OTHER).path

        private val dbEpgPath: String =
            MyApplication.context.getDatabasePath(Constants.DATABASE_EPG).path

        private val dbVodPath: String =
            MyApplication.context.getDatabasePath(Constants.DATABASE_VOD).path

        var instance: DBManager? = null
            get() {
                if (field == null) {
                    field = DBManager()
                }
                return field
            }
            private set
    }
}
