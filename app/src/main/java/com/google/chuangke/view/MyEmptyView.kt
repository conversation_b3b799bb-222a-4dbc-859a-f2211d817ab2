package com.google.chuangke.view

import android.annotation.SuppressLint
import android.view.LayoutInflater
import android.view.View
import com.chad.library.adapter.base.BaseQuickAdapter
import com.google.chuangke.R

class MyEmptyView {

    private var emptyView: View? = null

    @SuppressLint("InflateParams", "NotifyDataSetChanged")
    fun setAdapterView(
        layoutInflater: LayoutInflater,
        adapter: BaseQuickAdapter<*, *>,
    ) {
        //声明全局变量View emptyView;
        if (emptyView == null) {
            emptyView = layoutInflater.inflate(R.layout.empty_data_layout, null)
        }
        //数据得清空才会显示空布局
        adapter.data.clear()
        adapter.setEmptyView(emptyView!!)
        adapter.notifyDataSetChanged()
    }
}