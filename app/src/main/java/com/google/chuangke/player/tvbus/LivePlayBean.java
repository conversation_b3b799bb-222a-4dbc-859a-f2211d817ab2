package com.google.chuangke.player.tvbus;

class LivePlayBean {
    private String playUrl;
    private int buffer;
    private int replayCount;
    private long checkTime;

    public void init() {
        buffer = 0;
        replayCount = 0;
        checkTime = Long.MAX_VALUE;
    }

    public int getBuffer() {
        return buffer;
    }

    public void setBuffer(int buffer) {
        this.buffer = buffer;
    }

    public int getReplayCount() {
        return replayCount;
    }

    public void setReplayCount(int replayCount) {
        this.replayCount = replayCount;
    }

    public long getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(long checkTime) {
        this.checkTime = checkTime;
    }

    public String getPlayUrl() {
        return playUrl;
    }

    public void setPlayUrl(String playUrl) {
        this.playUrl = playUrl;
    }
}
