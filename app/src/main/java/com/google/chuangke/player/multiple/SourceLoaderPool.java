package com.google.chuangke.player.multiple;

import android.annotation.SuppressLint;
import android.widget.ProgressBar;

import androidx.media3.ui.PlayerView;

import com.google.chuangke.entity.ChannelBean;

import java.util.ArrayDeque;
import java.util.Queue;

/**
 * SourceLoader池
 * 通过 acquire() 获取对象，并启动携程获取播放链接
 * 获取到链接后从播放器池取出播放器播放
 * 同时启动检查任务
 *
 */
@SuppressLint("UnsafeOptInUsageError")
public class SourceLoaderPool {

    private final Queue<SourceLoader> sourceLoaderQueue;
    private final int poolSize;

    private static final class InstanceHolder {
        public static final SourceLoaderPool instance = new SourceLoaderPool(10);
    }

    public static SourceLoaderPool getInstance() {
        return InstanceHolder.instance;
    }

    private SourceLoaderPool(int poolSize) {
        this.poolSize = poolSize;
        this.sourceLoaderQueue = new ArrayDeque<>(poolSize);
    }

    // 池子里面有就从池子拿，没有就创建一个，释放的时候归还到池子中
    public SourceLoader acquire(ChannelBean channelBean, PlayerView playerView, ProgressBar progressBar) {
//        Log.e("======== sourceLoaderQueue", ""+sourceLoaderQueue.size());

        SourceLoader sourceLoader;
        if (sourceLoaderQueue.isEmpty()) {
            sourceLoader =  SourceLoader.Companion.create();
        }else{
            sourceLoader =  sourceLoaderQueue.poll();
        }
        sourceLoader.start(channelBean, playerView, progressBar);

        return sourceLoader;
    }

    public void release(SourceLoader sourceLoader) {
        if (sourceLoader != null) {
            sourceLoader.reset();
            if (sourceLoaderQueue.size() < poolSize) {
                sourceLoaderQueue.offer(sourceLoader);
            }
        }
    }

    public void releaseAll() {
        while (!sourceLoaderQueue.isEmpty()) {
            SourceLoader sourceLoader = sourceLoaderQueue.poll();
            if (sourceLoader != null) {
                sourceLoader.release();
            }
        }
    }

}