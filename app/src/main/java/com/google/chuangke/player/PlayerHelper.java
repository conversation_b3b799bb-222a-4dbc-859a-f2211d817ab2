package com.google.chuangke.player;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.accessibility.CaptioningManager;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.media3.common.C;
import androidx.media3.common.Format;
import androidx.media3.common.MediaItem;
import androidx.media3.common.PlaybackException;
import androidx.media3.common.Player;
import androidx.media3.common.TrackGroup;
import androidx.media3.common.Tracks;
import androidx.media3.common.VideoSize;
import androidx.media3.datasource.DefaultHttpDataSource;
import androidx.media3.datasource.HttpDataSource;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.exoplayer.dash.DashMediaSource;
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory;
import androidx.media3.exoplayer.source.MediaSource;
import androidx.media3.exoplayer.source.TrackGroupArray;
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector;
import androidx.media3.exoplayer.trackselection.MappingTrackSelector;
import androidx.media3.ui.AspectRatioFrameLayout;
import androidx.media3.ui.CaptionStyleCompat;
import androidx.media3.ui.PlayerView;
import androidx.media3.ui.SubtitleView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.fastjson.JSONObject;
import com.bumptech.glide.Glide;
import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.viewholder.BaseViewHolder;
import com.google.chuangke.MyApplication;
import com.google.chuangke.R;
import com.google.chuangke.common.Config;
import com.google.chuangke.common.UserHelper;
import com.google.chuangke.common.event.PlayerStatusEvent;
import com.google.chuangke.common.event.TracksChangedEvent;
import com.google.chuangke.database.DBApi;
import com.google.chuangke.entity.ChannelBean;
import com.google.chuangke.entity.EpgBean;
import com.google.chuangke.page.dialog.ActionBarDialogHelper;
import com.google.chuangke.page.dialog.LoadingDialogHelper;
import com.google.chuangke.util.DateUtil;
import com.orhanobut.logger.Logger;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SuppressLint("UnsafeOptInUsageError")
public class PlayerHelper {
    private static PlayerHelper instance;
    private PlayerView playerView;
    private SubtitleView subtitleView;
    private ExoPlayer exoPlayer;
    private String playUrl; //播放的路径
    private DefaultTrackSelector trackSelector; //声道

    private View tvPause;
    private TextView tvSpeed;
    private TextView tvError;
    private ImageView ivError;
    private RecyclerView rvEpg;
    private TextView tvEpg;
    private BaseQuickAdapter<EpgBean, BaseViewHolder> mAdapter;

    private final List<String> audioTracks;
    private final List<String> textTracks;

    private final static int REPLAY_MSG = 0x130;
    private final static Long REPLAY_MSG_TIME = 10_000L;

    public final static String TEXT_TRACK_NONE = "None";

    private PlayerHelper() {
        audioTracks = new ArrayList<>();
        textTracks = new ArrayList<>();
        EventBus.getDefault().register(this);
    }

    public static PlayerHelper getInstance() {
        if (instance == null) {
            synchronized (PlayerHelper.class) {
                if (instance == null) {
                    instance = new PlayerHelper();
                }
            }
        }
        return instance;
    }

    public void init(PlayerView styledPlayerView) {
        this.playerView = styledPlayerView;
        initExoPlayer(); //初始化顺序不能变
        initView();
        initAdapter();
        initListener();
    }

    private void initExoPlayer() {
        trackSelector = new DefaultTrackSelector(MyApplication.context);
        trackSelector.setParameters(
                trackSelector.getParameters().buildUpon()
                        .setPreferredAudioMimeType("audio/mp4a-latm") // 默认使用aac音轨
                        .setPreferredAudioLanguage("en")
        );

        exoPlayer = new ExoPlayer.Builder(MyApplication.context, new FfmpegRenderersFactory(MyApplication.context)).setTrackSelector(trackSelector).build();
        exoPlayer.setVolume(1f);
    }

    private void initView() {
        View view = LayoutInflater.from(MyApplication.context).inflate(R.layout.view_player_pause, playerView, false);
        tvPause = view.findViewById(R.id.iv_player_pause);
        tvSpeed = view.findViewById(R.id.tv_player_speed);
        tvError = view.findViewById(R.id.tv_player_error);
        ivError = view.findViewById(R.id.iv_player_error);
        rvEpg = view.findViewById(R.id.rv_player_epg);
        tvEpg = view.findViewById(R.id.tv_player_epg);
        playerView.addView(view);

        playerView.requestFocus();
        playerView.setControllerAutoShow(false);
        playerView.setUseController(false);
        playerView.setKeepScreenOn(true);
        playerView.setKeepContentOnPlayerReset(true);

        subtitleView = playerView.getSubtitleView();
        setSystemCaptionStyle();
        playerView.setPlayer(exoPlayer);
    }

    private void initAdapter() {
        rvEpg.setLayoutManager(new LinearLayoutManager(MyApplication.context, LinearLayoutManager.VERTICAL, false));
        rvEpg.setAdapter(mAdapter = new BaseQuickAdapter<EpgBean, BaseViewHolder>(R.layout.item_play_epg) {
            @Override
            protected void convert(@NonNull BaseViewHolder baseViewHolder, EpgBean epgBean) {
                String sb = DateUtil.INSTANCE.getHourTime(epgBean.getBeginTime()) + "-" + DateUtil.INSTANCE.getHourTime(epgBean.getEndTime()) + " " + DateUtil.INSTANCE.getDMTime(epgBean.getBeginTime());
                baseViewHolder.setText(R.id.tv_item_epg_title, sb);
                baseViewHolder.setText(R.id.tv_item_epg_subtitle, epgBean.getName());
            }
        });
    }

    private Handler replayHandler = new Handler(Looper.getMainLooper(), new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            Logger.e("replayHandler-------->");
            if (msg.what == REPLAY_MSG) {
                replay();
            }
            return false;
        }
    });

    public void replay() {
        LivePlayHelper.Companion.getInstance().setInitPlay(true);
        LivePlayHelper.Companion.getInstance().channelPlay(Config.getInstance().getCurrentPlayChannel(), false, false, 0L, true);
    }

    private final static int MAX_RETRY = 10;
    private int retryCount = MAX_RETRY;

    private void initListener() {
        Player.Listener playerListener = new Player.Listener() {
            @Override
            public void onPlayerError(PlaybackException error) {

                // 重试方法移到到LivePlayHelper.startPlay()方法
                // 尝试在有限次数内的重播
//                if (retryCount > 0) {
//                    retryCount--;
//                    replayHandler.sendEmptyMessageDelayed(REPLAY_MSG, REPLAY_MSG_TIME);
//                }

//                long currentPosition = exoPlayer.getCurrentPosition();
//                if (error.errorCode == PlaybackException.ERROR_CODE_TIMEOUT || error.errorCode == PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT) {//超时,直接调用重播,需要从断网的节点开始播放
//                    startPlay(playUrl, currentPosition, mimeTypes);
//                    return;
//                }

                // 直播添加日志
//                if (Config.getInstance().getPlayType() == 0) {
//                    String message = error.getCause() != null ? error.getCause().getMessage() : error.getMessage();
//                    addErrorLog(-1, message, "ExoPlayer error");
//                }

            }

            @Override
            public void onIsPlayingChanged(boolean isPlaying) {
                Player.Listener.super.onIsPlayingChanged(isPlaying);
                if (isPlaying) {
                    // 播放成功后重置次数
//                    retryCount = MAX_RETRY;

                    LoadingDialogHelper.Companion.getInstance().dismiss();
                    onPlayerStatusEvent(new PlayerStatusEvent(PlayerStatusEvent.NORMAL));
                }
            }

            @Override
            public void onTracksChanged(@NonNull Tracks tracks) {
                getTracks();
            }

            @Override
            public void onPlaybackStateChanged(int playbackState) {
                Player.Listener.super.onPlaybackStateChanged(playbackState);
            }

            /*
            不监听，直接显示最高分辨率
            @Override
            public void onVideoSizeChanged(VideoSize videoSize) {
                currentWidth = videoSize.width;
                currentHeight = videoSize.height;
                Player.Listener.super.onVideoSizeChanged(videoSize);
            }*/
        };
        exoPlayer.addListener(playerListener);
    }

    /**
     * 切换屏幕播放比例
     *
     * @param ratioType 0:自适应,1:填充屏幕,2:16/9,3:4/3
     */
    public void setRatio(int ratioType) {
        int videoWidth = playerView.getWidth();
        int videoHeight;
        DisplayMetrics dm = MyApplication.context.getResources().getDisplayMetrics();
        int winWidth = dm.widthPixels;
        int winHeight = dm.heightPixels;

        videoHeight = winHeight;
        switch (ratioType) {
            case 0:
                playerView.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIT);
                videoWidth = winWidth;
                break;
            case 1:
                playerView.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FIXED_WIDTH);
                videoWidth = winWidth;
                break;
            case 2:
                playerView.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FILL);
                videoWidth = winHeight * 16 / 9;
                break;
            case 3:
                playerView.setResizeMode(AspectRatioFrameLayout.RESIZE_MODE_FILL);
                videoWidth = winHeight * 4 / 3;
                break;
            default:
        }

        RelativeLayout.LayoutParams rlp = new RelativeLayout.LayoutParams(videoWidth, videoHeight);
        rlp.addRule(RelativeLayout.CENTER_IN_PARENT);
        playerView.setLayoutParams(rlp);
    }

    /**
     * 设置音轨
     */
    public void setTrackAudio(String language) {
        trackSelector.setParameters(trackSelector.getParameters().buildUpon().setPreferredAudioLanguage(language));
    }

    /**
     * 设置字幕
     */
    public void setTrackText(String language) {
        if (subtitleView == null){
            return;
        }

        if (TEXT_TRACK_NONE.equals(language)){
            subtitleView.setVisibility(View.GONE);
            return;
        }

        subtitleView.setVisibility(View.VISIBLE);
        trackSelector.setParameters(trackSelector.getParameters().buildUpon().setPreferredTextLanguage(language));
    }


    private int currentWidth = 0;
    private int currentHeight = 0;
    private float currentFrameRate = 0f;

    private void getTracks() {
        audioTracks.clear();
        textTracks.clear();
        textTracks.add(TEXT_TRACK_NONE);
        MappingTrackSelector.MappedTrackInfo mappedTrackInfo = trackSelector.getCurrentMappedTrackInfo();
        if (mappedTrackInfo != null) {
            for (int i = 0; i < mappedTrackInfo.getRendererCount(); i++) {
                TrackGroupArray rendererTrackGroups = mappedTrackInfo.getTrackGroups(i);

                for (int groupIndex = 0; groupIndex < rendererTrackGroups.length; groupIndex++) {
                    TrackGroup trackGroup = rendererTrackGroups.get(groupIndex);
                    for (int j = 0; j < trackGroup.length; j++) {
                        Log.e("track", trackGroup.getFormat(j).toString());
                    }
                }

                if (C.TRACK_TYPE_AUDIO == mappedTrackInfo.getRendererType(i)) { //判断是否是音轨
                    for (int groupIndex = 0; groupIndex < rendererTrackGroups.length; groupIndex++) {
                        TrackGroup trackGroup = rendererTrackGroups.get(groupIndex);
                        for (int j = 0; j < trackGroup.length; j++) {
                            String language = trackGroup.getFormat(j).language;
                            if (!TextUtils.isEmpty(language)) {
                                audioTracks.add(language);
                            }
                        }
                    }
                } else if (C.TRACK_TYPE_TEXT == mappedTrackInfo.getRendererType(i)) { //判断是否是字幕
                    for (int groupIndex = 0; groupIndex < rendererTrackGroups.length; groupIndex++) {
                        TrackGroup trackGroup = rendererTrackGroups.get(groupIndex);
                        for (int j = 0; j < trackGroup.length; j++) {
                            String language = trackGroup.getFormat(j).language;
                            if (!TextUtils.isEmpty(language)) {
                                textTracks.add(language);
                            }
                        }
                    }
                } else if (C.TRACK_TYPE_VIDEO == mappedTrackInfo.getRendererType(i)) {
                    for (int groupIndex = 0; groupIndex < rendererTrackGroups.length; groupIndex++) {
                        TrackGroup trackGroup = rendererTrackGroups.get(groupIndex);
                        int maxHeight = 0;
                        for (int j = 0; j < trackGroup.length; j++) {
                            Format format = trackGroup.getFormat(j);

                            // 在这里获取分辨率和帧率
                            if (format.height > maxHeight) {
                                currentWidth = format.width;
                                currentHeight = format.height;
                                currentFrameRate = format.frameRate;
                                maxHeight = format.height;
                            }
                        }
                        Float aspectRatio = ((float) currentWidth) / currentHeight;
                        changeDisplayByAspectRatio(aspectRatio);
                    }
                }
            }
        }

        EventBus.getDefault().post(new TracksChangedEvent());
    }

    private final static Float RATIO_16_9 = 16f / 9f; // 16:9 宽高比
    private final static Float RATIO_4_3 = 4f / 3f; // 4:3 宽高比
    private final static Float epsilon = 0.01f; // 定义一个误差范围

    private void changeDisplayByAspectRatio(Float aspectRatio) {
        int aspectRatioResult;
        if (Math.abs(aspectRatio - RATIO_16_9) < epsilon) {
            // 如果实际宽高比接近16:9
            aspectRatioResult = 2;
            Log.d("AspectRatio", "The aspect ratio is 16:9.");
        } else if (Math.abs(aspectRatio - RATIO_4_3) < epsilon) {
            // 如果实际宽高比接近4:3
            aspectRatioResult = 3;
            Log.d("AspectRatio", "The aspect ratio is 4:3.");
        } else {
            // 如果都不是
            aspectRatioResult = 0;
            Log.d("AspectRatio", "The aspect ratio is neither 16:9 nor 4:3.");
        }
        setRatio(aspectRatioResult);
    }

    public List<String> getAudioTracks() {
        return audioTracks;
    }

    public List<String> getTextTracks() {
        return textTracks;
    }

    public void stopPlay() {
        if (exoPlayer != null) {
            exoPlayer.stop();
            exoPlayer.clearMediaItems();
        }
    }

    /**
     * exo播放方法，播放前先调用停止、清空，在设置到指定位置
     *
     * @param provider     000使用DashMediaSource，其他使用DefaultMediaSourceFactory2
     * @param playUrl     播放路径
     * @param timestampMs 开始播放的位置，null不设置
     * @param mimeTypes   播放类型 如，MimeTypes.APPLICATION_M3U8
     * @param retry       是否是重新尝试播放
     */
    public void startPlay(String provider, String playUrl, String headers, Long timestampMs, String mimeTypes, boolean retry) {
        if (TextUtils.isEmpty(playUrl)){
            return;
        }

        // 换台重置最大尝试次数
        if (!retry) {
            retryCount = MAX_RETRY;
        }

        Logger.e(playUrl);
        this.playUrl = playUrl;

        // 添加请求头
        HttpDataSource.Factory dataSourceFactory = new DefaultHttpDataSource.Factory().setReadTimeoutMs(60000).setConnectTimeoutMs(60000);
        Map<String, String> header = new HashMap<>();
        if (!TextUtils.isEmpty(headers)){
            try {
                JSONObject headerJO = JSONObject.parseObject(headers);
                for (String key : headerJO.keySet()) {
                    String value = headerJO.getString(key);
                    header.put(key, value);
                }
            } catch (Exception e) {
                Logger.e(e.getMessage());
            }
        }
        String selfHeader = getSessionByUrl(playUrl);
        if (!TextUtils.isEmpty(selfHeader)){
            header.put("session", selfHeader);
        }
        dataSourceFactory.setDefaultRequestProperties(header);

        MediaSource ms;
        if ("000".equals(provider)){
            ms = new DashMediaSource.Factory(dataSourceFactory, playUrl).createMediaSource(MediaItem.EMPTY);
        }else{
            MediaItem.Builder mediaBuilder = new MediaItem.Builder().setUri(Uri.parse(playUrl));
            if (mimeTypes != null) {
                mediaBuilder.setMimeType(mimeTypes);
            }
            MediaItem mediaItem = mediaBuilder.build();
            ms = new DefaultMediaSourceFactory(dataSourceFactory).createMediaSource(mediaItem);
        }

        exoPlayer.stop();
        exoPlayer.clearMediaItems();
        exoPlayer.setMediaSource(ms);
        if (timestampMs != null){
            exoPlayer.seekTo(timestampMs);
        }
        playerView.setKeepContentOnPlayerReset(true);
        exoPlayer.prepare();
        exoPlayer.setPlayWhenReady(true);

        tvPause.setVisibility(View.GONE);
    }

    private String getSessionByUrl(String url) {
        if (TextUtils.isEmpty(url)){
            return null;
        }

        Pattern pattern = Pattern.compile("/\\d+/\\w+/", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(url);
        if (!matcher.find()) {
            return null;
        }

        String session = matcher.group(0);
        if (session == null) {
            return null;
        }

        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            return null;
        }
        byte[] digest = md.digest(session.getBytes());
        return String.format("%032x", new BigInteger(1, digest));
    }

    public void toggle() {
        if (exoPlayer == null) {
            return;
        }
        if (exoPlayer.isPlaying()) {
            pause();
        } else {
            play();
        }
    }

    public void pause() {
        if (exoPlayer != null) {
            tvPause.setVisibility(View.VISIBLE);
            exoPlayer.pause();
        }
    }

    public void play() {
        if (exoPlayer != null) {
            tvPause.setVisibility(View.GONE);
            exoPlayer.play();
        }
    }

    public void release() {
        if (exoPlayer != null) {
            exoPlayer.release();
        }
        EventBus.getDefault().unregister(this);

        instance = null;
    }

    public long getDuration() {
        if (exoPlayer == null) {
            return 0;
        }
        if (exoPlayer.getDuration() == C.TIME_UNSET) {
            return 0;
        }
        return exoPlayer.getDuration();
    }

    public long getCurrentPosition() {
        if (exoPlayer == null) {
            return 0;
        }
        return exoPlayer.getCurrentPosition();
    }

    public void seekTo(long position) {
        if (exoPlayer == null) {
            return;
        }
        exoPlayer.seekTo(position);
        if (!exoPlayer.isPlaying()) {
            play();
        }
    }

    public void setSystemCaptionStyle(){
        if (subtitleView != null){
            subtitleView.setUserDefaultStyle();
            subtitleView.setUserDefaultTextSize();
        }
    }

    public int getPlaybackState() {
        if (exoPlayer == null) {
            return 0;
        }
        return exoPlayer.getPlaybackState();
    }

    public void clearContent() {
        playerView.setKeepContentOnPlayerReset(false);
    }

    private boolean channelChange;

    public void setChannelChange(boolean channelChange) {
        this.channelChange = channelChange;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPlayerStatusEvent(PlayerStatusEvent event) {
        if (playerView == null) {
            return;
        }
        tvSpeed.setVisibility(View.GONE);

        if (event.getCode() != PlayerStatusEvent.NORMAL) {
            LoadingDialogHelper.Companion.getInstance().dismiss();
            if (channelChange) {
//                addErrorLog(event.getCode(), "code:" + event.getCode(), "channel source error");
                tvError.setText(String.valueOf(event.getCode()));
                tvError.setVisibility(View.VISIBLE);
                errorData();
                ivError.setVisibility(View.VISIBLE);
                channelChange = false;
            }
        }else{
            tvError.setVisibility(View.GONE);
            ivError.setVisibility(View.GONE);
        }
    }

    /**
     * 不同频道展示不同的错误图片
     */
    private void errorData() {

        // 特殊频道的图片
        String tag = Config.getInstance().getCurrentTag().getName();
        int resId;
        switch (containsAnyOfIgnoreCase(tag)) {
            case "NFL":
                resId = R.drawable.img_channel_error_nfl;
                break;
            case "MLB":
                resId = R.drawable.img_channel_error_mlb;
                break;
            case "NBA":
                resId = R.drawable.img_channel_error_nba;
                break;
            case "NHL":
                resId = R.drawable.img_channel_error_nhl;
                break;
            case "PPV":
                resId = R.drawable.img_channel_error_ppv;
                break;
            default:
                resId = R.drawable.img_channel_error;
                break;
        }
        Glide.with(MyApplication.context).load(resId).into(ivError);

        // 展示特别频道的数据
        if (resId == R.drawable.img_channel_error || resId == R.drawable.img_channel_error_ppv) {
            tvEpg.setVisibility(View.GONE);
            rvEpg.setVisibility(View.GONE);
        } else {
            tvEpg.setText(Config.getInstance().getCurrentPlayChannel().getName());
            tvEpg.setVisibility(View.VISIBLE);
            rvEpg.setVisibility(View.VISIBLE);
            List<EpgBean> tempList = DBApi.Companion.getInstance().getEspecialEpgListByDay(Config.getInstance().getCurrentPlayChannel(), new Date());
            List<EpgBean> tempList2 = new ArrayList<>();
            if (tempList.size() > 7) {
                for (int i = 0; i < 7; i++) {
                    tempList2.add(tempList.get(i));
                }
            } else {
                tempList2.addAll(tempList);
            }
            mAdapter.setNewInstance(tempList2);
            rvEpg.setFocusable(false);
        }
    }

    private String containsAnyOfIgnoreCase(String tag) {
        if (TextUtils.isEmpty(tag)) return "";
        for (String keyword : Config.getInstance().getSportChannel()) {
            if (tag.toUpperCase().contains(keyword)) return keyword;
        }
        return "";
    }

    public void showLoadingDialog() {
        if (playerView != null) {
            Context context = playerView.getContext();
            if (context instanceof Activity) {
                LoadingDialogHelper.Companion.getInstance().show((Activity) context);
            }
        }
    }

    public void showChannelInfoDialog(ChannelBean channelBean) {
        if (playerView != null) {
            Context context = playerView.getContext();
            if (context instanceof Activity) {
                ActionBarDialogHelper.Companion.getInstance().show((Activity) context, channelBean);
            }
        }
    }

    public void addErrorLog(int code, String errorLog, String desc) {
        ChannelBean channel = Config.getInstance().getCurrentPlayChannel();
        String channelId = String.valueOf(channel.getChannelId());
        int channelNumber = channel.getChannelNumber() == null ? 0 : channel.getChannelNumber();
        String name = channel.getName();
        UserHelper.Companion.getInstance().addErrorLog(channelId, channelNumber, name, code, errorLog, desc);
    }

    public int getCurrentWidth() {
        return currentWidth;
    }

    public int getCurrentHeight() {
        return currentHeight;
    }

    public float getCurrentFrameRate() {
        return currentFrameRate;
    }
}

