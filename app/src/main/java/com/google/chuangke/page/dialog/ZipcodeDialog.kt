package com.google.chuangke.page.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import com.google.chuangke.R
import com.google.chuangke.base.DisableSpeechDialog
import com.google.chuangke.common.Constants
import com.google.chuangke.ext.toast
import com.google.chuangke.util.SPUtils
import com.google.chuangke.util.ZipCodeCache

class ZipcodeDialog(context: Context, themeId: Int, val callback: () -> Unit) :
    DisableSpeechDialog(context, themeId) {

    private lateinit var mEtZipcode: EditText
    private lateinit var mTvApply: TextView

    private lateinit var zipToDmaMap: Map<String, Pair<String, String>>

    fun getDmaDescription(zipCode: String): Pair<String, String>? {
        return zipToDmaMap[zipCode]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_zipcode_input)
        initView()
        val window: Window? = this.window
        if (window != null) {
            window.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
            )
            window.setDimAmount(0f)
        }
        initListener()
        zipToDmaMap = ZipCodeCache.loadData(context) as Map<String, Pair<String, String>>
    }

    private fun initView() {
        mEtZipcode = findViewById(R.id.et_zipcode)
        mTvApply = findViewById(R.id.tv_apply)
    }

    private fun initListener() {
        mTvApply.setOnClickListener {
            val zipcode = mEtZipcode.text.toString()
            if (zipcode.length < 5) {
                context.toast { "Invalid ZIP code" }
            } else {
                val pair = getDmaDescription(zipcode)
                if (pair == null)
                    context.toast { "Invalid ZIP code" }
                else {
                    context.toast { "DMA Location set success" }
                    val dma = pair.first
                    val displayName = pair.second
                    if (!dma.isNullOrEmpty()) {
                        SPUtils.putString(
                            context,
                            Constants.SP_KEY_DMA,
                            "$dma,$displayName"
                        )
                    }
                }

                callback.invoke()
                dismiss()
            }
        }
    }

}