package com.google.chuangke.page.center.presenter

import android.annotation.SuppressLint
import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.core.view.isVisible
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.page.center.ChannelFragment
import com.google.chuangke.util.DateUtil
import java.util.*

class EpgPresenter(var mChannelFragment: ChannelFragment) : BasePresenter<EpgBean>() {

    override fun layoutId(): Int {
        return R.layout.item_epgs
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {
        return false
    }

    @SuppressLint("SetTextI18n")
    override fun bindViewHolder(view: View, item: EpgBean) {

        // 时间段
        val startTime = item.beginTime?.let { DateUtil.getHourTime(it) }
        val endTime = item.endTime?.let { DateUtil.getHourTime(it) }
        val tempTimeRange = view.findViewById<TextView>(R.id.tv_item_epg_title)
        tempTimeRange.text =
            "$startTime-$endTime  ${if (mChannelFragment.especialChannel) DateUtil.getDMTime(item.beginTime) else ""}"


        // 节目
        val tempProgram = view.findViewById<TextView>(R.id.tv_item_epg_subtitle)
        tempProgram.text = item.name

        // 是否是当前节目，显示Playing
        val now = (Date().time / 1000).toInt()
        val playing = view.findViewById<TextView>(R.id.tv_item_epg_playing)
        if (item.beginTime!! < now && item.endTime!! > now && DateUtil.isToday(mChannelFragment.getCurrentDate())) {
            tempTimeRange.setTextColor(context.getColor(R.color.purpler_7B0B82))
            tempProgram.setTextColor(context.getColor(R.color.purpler_7B0B82))

            playing.visibility = View.VISIBLE
        } else {
            tempTimeRange.setTextColor(context.getColorStateList(R.color.selector_color_white_purple))
            tempProgram.setTextColor(context.getColorStateList(R.color.selector_color_white_purple))

            playing.visibility = View.INVISIBLE
        }

        val replay = view.findViewById<ImageView>(R.id.iv_item_channel_replay)
        val channelList = DBApi.getInstance().getChannelByEpg(item)
        if (channelList.isNotEmpty()) {
            channelList[0].let { channel ->
                replay.visibility = if (channel.playback == 1 && item.endTime!! < now) View.VISIBLE
                else View.INVISIBLE
            }
        } else {
            replay.visibility = View.INVISIBLE
        }

        // 如果右侧有标志就显示
        view.findViewById<View>(R.id.rl_item_epg_control).visibility =
            if (replay.isVisible || playing.isVisible) View.VISIBLE
            else View.GONE

    }

}