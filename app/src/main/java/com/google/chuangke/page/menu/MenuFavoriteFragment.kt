package com.google.chuangke.page.menu

import android.annotation.SuppressLint
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.core.view.get
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.VerticalGridView
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.event.*
import com.google.chuangke.data.menu.MenuFavoriteViewModel
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.page.menu.presenter.MenuFavoritePresenter
import org.koin.androidx.viewmodel.ext.android.viewModel

class MenuFavoriteFragment : BaseFragment() {

    private val mMenuFavoriteViewModel: MenuFavoriteViewModel by viewModel()

    private lateinit var mRecyclerView: VerticalGridView
    private lateinit var mTvCount: TextView
    private lateinit var mTvAllCount: TextView
    private lateinit var mBottomCatcher: View

    private lateinit var mMenuFavoriteAdapter: ArrayObjectAdapter

    override fun layoutId(): Int {
        return R.layout.fragment_menu_favorite
    }

    override fun initView(view: View) {
        mRecyclerView = view.findViewById(R.id.rv_fragment_menu_favorite)
        mTvCount = view.findViewById(R.id.tv_fragment_menu_favorite_count)
        mTvAllCount = view.findViewById(R.id.tv_fragment_menu_favorite_all_count)
        mBottomCatcher = view.findViewById(R.id.line_fragment_menu_favorite_result)

//        mRecyclerView.layoutManager =
//            LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
        mRecyclerView.adapter = ItemBridgeAdapter(ArrayObjectAdapter(MenuFavoritePresenter()).also {
            mMenuFavoriteAdapter = it
        }).also {
            it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                    super.onCreate(viewHolder)
                    viewHolder.itemView.setOnClickListener { itemView ->
                        val bean = itemView.tag as ChannelBean
                        bean.collection = if (bean.collection == 1) 0 else 1
                        //保存收藏的数据
                        mMenuFavoriteViewModel.saveChannelCollection(
                            bean.id!!, bean.collection == 1
                        )

                        viewHolder.itemView.tag?.let { value ->
//                            Log.e("mMenuFavoriteAdapter", "${mMenuFavoriteAdapter.indexOf(value)}")
                            it.notifyItemChanged(mMenuFavoriteAdapter.indexOf(value))
                        }

                    }
                }
            })
        }
    }


    companion object {
        private const val PAGE_CHANNEL_COUNT = 7
    }

    override fun initListener() {

        mRecyclerView.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mMenuFavoriteAdapter.indexOf(mRecyclerView.focusedChild.tag) == 0) {
                        return@setOnKeyInterceptListener true
                    }
                }
                KeyEvent.KEYCODE_PAGE_UP -> {
                    var tempPosition = mRecyclerView.selectedPosition - PAGE_CHANNEL_COUNT
                    if (tempPosition < 0) {
                        tempPosition = 0
                    }
                    mRecyclerView.selectedPosition = tempPosition
                }
                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    var tempPosition = mRecyclerView.selectedPosition + PAGE_CHANNEL_COUNT
                    if (tempPosition >= mMenuFavoriteAdapter.size()) {
                        tempPosition = mMenuFavoriteAdapter.size() - 1
                    }
                    mRecyclerView.selectedPosition = tempPosition
                }
            }

            return@setOnKeyInterceptListener false
        }

        mBottomCatcher.setOnKeyListener { v, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                when (keyCode) {
                    KeyEvent.KEYCODE_DPAD_DOWN -> {
                        val index = mRecyclerView.selectedPosition
                        if (index == -1) {
                            mRecyclerView.selectedPosition = 0
                            mRecyclerView[0].requestFocus()
                        } else {
                            mRecyclerView[mRecyclerView.selectedPosition].requestFocus()
                        }
                    }
                }
                return@setOnKeyListener false
            }

            when (keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    val index = mRecyclerView.selectedPosition
                    if (index == -1) {
                        mRecyclerView.selectedPosition = 0
                        mRecyclerView[0].requestFocus()
                    } else {
                        mRecyclerView[mRecyclerView.selectedPosition].requestFocus()
                    }
                    return@setOnKeyListener true
                }
            }

            false
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initObserve() {
        mMenuFavoriteViewModel.channelLiveData.observe(this) {
            mMenuFavoriteAdapter.setItems(it, null)
            mTvAllCount.text = "/${it.size}"
        }

        mMenuFavoriteViewModel.collectionCountLiveData.observe(this) {
            mTvCount.text = it.toString()
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            mMenuFavoriteViewModel.getAllChannel()
        }
    }

    fun requestFocus() {
        mRecyclerView.getChildAt(0).requestFocus()
    }
}