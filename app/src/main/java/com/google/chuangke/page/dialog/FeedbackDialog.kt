package com.google.chuangke.page.dialog

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import com.google.chuangke.page.dialog.feedback.FaqFragment
import com.google.chuangke.page.dialog.feedback.FeedbackFragment
import android.os.Bundle
import android.util.SparseArray
import android.view.*
import androidx.fragment.app.*
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.google.chuangke.R
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FeedbackDialogFragment : DialogFragment() {
    private lateinit var viewPageAdapter: ViewPageAdapter
    private lateinit var tabLayout: TabLayout
    private lateinit var viewPager: ViewPager2

    private lateinit var tabTitles: Array<String>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        //弹出框透明背景样式
        setStyle(STYLE_NO_TITLE, R.style.MyLoadingDialog);
    }

    override fun onStart() {
        super.onStart()
        //透明背景
        val window = dialog!!.window
        val params = window!!.attributes
        params.dimAmount = 0.0f
        params.flags = params.flags or WindowManager.LayoutParams.FLAG_DIM_BEHIND
        window.attributes = params
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.dialog_feedback, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initData()
        initView(view)
        EventBus.getDefault().register(this)
    }


    private fun initView(view: View) {
        viewPager = view.findViewById(R.id.vp_dialog_feedback)
        tabLayout = view.findViewById(R.id.tl_dialog_feedback)
        viewPager.isUserInputEnabled = false
        viewPager.offscreenPageLimit = 2

        viewPageAdapter = ViewPageAdapter(requireActivity()).apply {
            initData()
        }
        viewPager.adapter = viewPageAdapter

        TabLayoutMediator(tabLayout, viewPager) { tab, position ->
            tab.text = tabTitles[position]
            tab.view.background = ColorDrawable(Color.TRANSPARENT)
        }.attach()

        // tabLayout.getTabAt(1)?.select()

        viewPager.post {
            viewPager.requestFocus()
            onFeedbackSelectEvent(FeedbackTabEvent(0))
        }
    }

    private fun initData() {
        tabTitles = arrayOf(
            getString(R.string.dialog_feedback_tab_feedback),
            getString(R.string.dialog_feedback_tab_faq),
            getString(R.string.dialog_feedback_tab_contact_us)
        )
    }


    override fun onDestroy() {
        EventBus.getDefault().unregister(this)
        super.onDestroy()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFeedbackSelectEvent(event: FeedbackTabEvent) {
        viewPager.currentItem = event.index

        viewPager.post {
            EventBus.getDefault().postSticky(FeedbackItemFocusEvent(tabLayout.selectedTabPosition))
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFeedbackDismissEvent(event: FeedbackDismissEvent) {
        dismiss()
    }

}

class ViewPageAdapter(fragmentActivity: FragmentActivity) :
    FragmentStateAdapter(fragmentActivity) {
    private val fragments = SparseArray<Fragment>()

    fun initData() {
        fragments.put(0, FeedbackFragment())
        fragments.put(1, FaqFragment())
//        fragments.put(2, ContactsUsFragment())
    }

    fun getFragment(position: Int): Fragment {
        return fragments[position]
    }

    override fun getItemCount(): Int = fragments.size()

    override fun createFragment(position: Int): Fragment {
        return fragments.get(position)
    }

}

class FeedbackTabEvent(var index: Int)
class FeedbackItemFocusEvent(var index: Int)
class FeedbackDismissEvent



