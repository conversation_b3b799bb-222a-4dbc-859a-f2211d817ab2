package com.google.chuangke.page.center

import android.annotation.SuppressLint
import android.net.ConnectivityManager
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.fragment.app.FragmentTransaction
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.leanback.widget.VerticalGridView
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Constants
import com.google.chuangke.common.event.*
import com.google.chuangke.page.center.MenuItem.*
import com.google.chuangke.page.listener.OnProgramSelectListener
import com.google.chuangke.page.menu.*
import com.google.chuangke.page.menu.presenter.MenuPresenter
import com.google.chuangke.util.SPUtils
import com.wochuang.json.DeviceIdUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.*

class MenuFragment(
    private val mOnProgramSelectListener: OnProgramSelectListener,
    var mOnMenuRequestFocusListener: OnMenuRequestFocusListener
) : BaseFragment() {

    private lateinit var mRvOption: VerticalGridView
    private lateinit var mTvIp: TextView
    private lateinit var mTvMac: TextView
    private lateinit var mTvSn: TextView

    private var mMenuAccountFragment: MenuAccountFragment? = null
    private var mMenuInfoFragment: MenuInfoFragment? = null
    private var mMenuLockFragment: MenuLockFragment? = null
    private var mMenuAppFragment: MenuAppFragment? = null
    private var mMenuNotificationFragment: MenuNotificationFragment? = null

    private lateinit var mMenuAdapter: ArrayObjectAdapter

    private val menuList = mutableListOf(
        ACCOUNT,
        INFO,
        LOCK,
        NOTIFICATION,
    )


    override fun layoutId(): Int {
        return R.layout.fragment_menu
    }

    override fun initView(view: View) {
        mRvOption = view.findViewById(R.id.rv_fragment_menu_option)
        mTvIp = view.findViewById(R.id.tv_fragment_menu_ip)
        mTvMac = view.findViewById(R.id.tv_fragment_menu_mac)
        mTvSn = view.findViewById(R.id.tv_fragment_menu_sn)

        initAdapter()
    }

    private fun initAdapter() {
        mRvOption.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(MenuPresenter().also { it.needKeep = true }).also {
                mMenuAdapter = it
            })
    }

    @SuppressLint("SetTextI18n")
    private fun initData() {

        mMenuAdapter.setItems(menuList, null)

        val mac = DeviceIdUtil.getMac()
        if (!TextUtils.isEmpty(mac)) {
            mTvMac.text = "MAC:${mac.uppercase()}"
        } else {
            mTvMac.visibility = View.GONE
        }
        val sn = DeviceIdUtil.getSN()
        if (!TextUtils.isEmpty(sn)) {
            mTvSn.text = "SN:${sn.uppercase()}"
        } else {
            mTvSn.visibility = View.GONE
        }

        onNetworkChangeEvent(NetworkChangeEvent())
    }

    override fun initListener() {
        mRvOption.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            @SuppressLint("SetTextI18n")
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                child?.let {
                    showTab(it.itemView.tag as MenuItem)
                }
            }
        })

        mRvOption.setOnKeyInterceptListener { keyEvent ->
            if (keyEvent.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (keyEvent.keyCode) {
                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mMenuAdapter.indexOf(mRvOption.focusedChild.tag) == 0) {
                        mOnMenuRequestFocusListener.onMenuRequestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    when (mRvOption.focusedChild.tag as MenuItem) {
                        LOCK -> mMenuLockFragment!!.requestFocus()
                        ACCOUNT -> {}
                        INFO -> mMenuInfoFragment!!.requestFocus()
                        APP -> {}
                        NOTIFICATION -> {}
                    }
                }
            }

            return@setOnKeyInterceptListener false
        }
    }

    override fun initObserve() {
        initData()
    }

    private fun showTab(index: MenuItem) {
        val ft = requireActivity().supportFragmentManager.beginTransaction()
        hideFragment(ft)
        when (index) {
            ACCOUNT -> {
                if (mMenuAccountFragment == null) {
                    mMenuAccountFragment = MenuAccountFragment()
                    ft.add(
                        R.id.fl_fragment_menu_content,
                        mMenuAccountFragment!!,
                        "mMenuAccountFragment"
                    )
                } else {
                    ft.show(mMenuAccountFragment!!)
                }
            }

            INFO -> {
                if (mMenuInfoFragment == null) {
                    mMenuInfoFragment = MenuInfoFragment()
                    ft.add(
                        R.id.fl_fragment_menu_content, mMenuInfoFragment!!, "mMenuInfoFragment"
                    )
                } else {
                    ft.show(mMenuInfoFragment!!)
                }
            }

            LOCK -> {
                if (mMenuLockFragment == null) {
                    mMenuLockFragment = MenuLockFragment()
                    ft.add(
                        R.id.fl_fragment_menu_content, mMenuLockFragment!!, "mMenuLockFragment"
                    )
                } else {
                    ft.show(mMenuLockFragment!!)
                }
            }

            APP -> {
                if (mMenuAppFragment == null) {
                    mMenuAppFragment = MenuAppFragment()
                    ft.add(
                        R.id.fl_fragment_menu_content, mMenuAppFragment!!, "mMenuAppFragment"
                    )
                } else {
                    ft.show(mMenuAppFragment!!)
                }
            }

            NOTIFICATION -> {
                if (mMenuNotificationFragment == null) {
                    mMenuNotificationFragment = MenuNotificationFragment()
                    ft.add(
                        R.id.fl_fragment_menu_content,
                        mMenuNotificationFragment!!,
                        "mMenuNotificationFragment"
                    )
                } else {
                    ft.show(mMenuNotificationFragment!!)
                }
            }
        }
        ft.commit()
    }

    private fun hideFragment(ft: FragmentTransaction) {
        mMenuAccountFragment?.let {
            ft.hide(it)
        }
        mMenuInfoFragment?.let {
            ft.hide(it)
        }
        mMenuLockFragment?.let {
            ft.hide(it)
        }
        mMenuAppFragment?.let {
            ft.hide(it)
        }
        mMenuNotificationFragment?.let {
            ft.hide(it)
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onNetworkChangeEvent(event: NetworkChangeEvent) {
        val connectType = SPUtils.getObject(requireActivity(), Constants.SP_KEY_CONNECT_TYPE)
        val ipStr = SPUtils.getString(requireActivity(), Constants.SP_KEY_IP, null)
        if (!TextUtils.isEmpty(ipStr)) {
            val ipJson = JSONObject.parseObject(ipStr)
            val ip = ipJson.getString("query")
            when (connectType) {
                ConnectivityManager.TYPE_WIFI -> {
                    mTvIp.text = "IP:$ip W"
                }

                ConnectivityManager.TYPE_ETHERNET -> {
                    mTvIp.text = "IP:$ip L"
                }

                else -> {
                    mTvIp.text = "IP:$ip"
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    fun hide() {
        mRvOption.requestFocus()
        mRvOption.scrollToPosition(0)
    }
}

enum class MenuItem {
    ACCOUNT, INFO, LOCK, APP, NOTIFICATION
}