package com.google.chuangke.page.dialog.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.entity.ChannelBean
import java.util.*

class VoiceSearchPresenter : BasePresenter<ChannelBean>() {

    override fun layoutId(): Int {
        return R.layout.item_voice_search
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Bo<PERSON>an {

        // 不处理其他控件后续事件
        if (event.action == KeyEvent.ACTION_UP) {
            return false
        }

        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                needKeep = false
            }
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                needKeep = true
            }
        }

        return false
    }

    override fun bindViewHolder(view: View, item: ChannelBean) {

        val tempTvNo = view.findViewById<TextView>(R.id.tv_item_channel_no)
        tempTvNo.text = item.channelNumber.toString()

        val tempTvName = view.findViewById<TextView>(R.id.tv_item_channel_title)
        tempTvName.text = item.name
    }

}