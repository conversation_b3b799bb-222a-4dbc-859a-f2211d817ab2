package com.google.chuangke.page.center.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.common.Constants
import com.google.chuangke.page.center.SettingType
import com.google.chuangke.util.SPUtils

class SettingDialogPresenter : BasePresenter<SettingType>() {

    override fun layoutId(): Int {
        return R.layout.item_dialog_setting
    }

    override fun addFocusTextStyle(): List<Int> {
        return emptyList()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun bindViewHolder(view: View, item: SettingType) {
        val textView = view as TextView
        when (item) {
            SettingType.SPEED_VISIBLE -> {

                textView.text = context.getString(R.string.setting_speed_visible)

                val speedSP: String? =
                    SPUtils.getString(context, Constants.SETTING_SPEED_VISIBLE, "1")

                textView.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    if (speedSP != null && speedSP == "1") R.mipmap.ic_switch_open else R.mipmap.ic_switch_close,
                    0
                )

                textView.setOnClickListener {
                    val currentSpeedSP: String? =
                        SPUtils.getString(context, Constants.SETTING_SPEED_VISIBLE, "1")

                    val show = currentSpeedSP != null && currentSpeedSP == "1"

                    SPUtils.putString(
                        context,
                        Constants.SETTING_SPEED_VISIBLE,
                        if (show) "0" else "1"
                    )

                    textView.setCompoundDrawablesWithIntrinsicBounds(
                        0, 0, if (show) R.mipmap.ic_switch_close else R.mipmap.ic_switch_open, 0
                    )
                }
            }

            SettingType.TEXT_APPEARANCE -> {
                textView.text = context.getString(R.string.setting_text_appearance)

                textView.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    R.mipmap.ic_channel_arrow_right,
                    0
                )
            }

            SettingType.TEXT_CAPTION_STYLE -> {
                textView.text = context.getString(R.string.setting_captions_style)

                textView.setCompoundDrawablesWithIntrinsicBounds(
                    0,
                    0,
                    R.mipmap.ic_channel_arrow_right,
                    0
                )
            }
        }
    }

}