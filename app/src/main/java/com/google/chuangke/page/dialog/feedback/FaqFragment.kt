package com.google.chuangke.page.dialog.feedback

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.leanback.widget.VerticalGridView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Constants
import com.google.chuangke.common.event.SearchEvent
import com.google.chuangke.page.adapter.RecyclerExtras
import com.google.chuangke.page.dialog.FeedbackDismissEvent
import com.google.chuangke.page.dialog.FeedbackItemFocusEvent
import com.google.chuangke.page.dialog.FeedbackTabEvent
import com.google.chuangke.util.SPUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FaqFragment : BaseFragment() {
    private lateinit var recyclerView: VerticalGridView
    private lateinit var tvPosition: TextView
    private lateinit var faqAdapter: FaqAdapter
    private lateinit var list: ArrayList<JSONObject>

    override fun layoutId(): Int {
        return R.layout.fragment_feedback_faq
    }

    @SuppressLint("SetTextI18n")
    override fun initView(view: View) {
        tvPosition = view.findViewById(R.id.tv_fragment_feedback_faq_position)
        recyclerView = view.findViewById(R.id.rv_fragment_feedback_faq)

        val faq = SPUtils.getString(requireContext(), Constants.SP_KEY_FAQ, null)
        val faqJSONArray = JSONArray.parseArray(faq)
        list = ArrayList()
        if(faqJSONArray != null){
            for (item in faqJSONArray) {
                list.add(item as JSONObject)
            }
        }

        recyclerView.setOnKeyInterceptListener { event ->
            if (event.action != KeyEvent.ACTION_UP && event.keyCode == KeyEvent.KEYCODE_SEARCH) {
                EventBus.getDefault().post(SearchEvent())
                return@setOnKeyInterceptListener true
            }

            return@setOnKeyInterceptListener false
        }

        recyclerView.layoutManager = LinearLayoutManager(requireActivity())
        recyclerView.adapter = FaqAdapter(recyclerView).also { faqAdapter = it }
        faqAdapter.setList(list)

        tvPosition.text = ("")
    }

    override fun initListener() {

    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }


    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onFeedbackItemForceEvent(event: FeedbackItemFocusEvent) {
        if (event.index == 1) {
            recyclerView.getChildAt(0).requestFocus()
        }
    }
}

class FaqAdapter(recyclerView: RecyclerView) :
    BaseQuickAdapter<JSONObject, BaseViewHolder>(R.layout.item_fragment_feedback_faq) {

    var rv: RecyclerView = recyclerView
    private var onItemKeyListener: RecyclerExtras.OnItemKeyListener? = null

    fun setOnItemKeyListener(onItemKeyListener: RecyclerExtras.OnItemKeyListener?) {
        this.onItemKeyListener = onItemKeyListener
    }

    override fun convert(holder: BaseViewHolder, item: JSONObject) {
        holder.setText(R.id.tv_item_fragment_feedback_faq_id, (getItemPosition(item) + 1).toString())
        holder.setText(R.id.tv_item_fragment_feedback_faq_answer, item.getString("answer"))
        holder.setText(R.id.tv_item_fragment_feedback_faq_question, item.getString("question"))

        holder.itemView.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(view: View, i: Int, keyEvent: KeyEvent): Boolean {
                if (keyEvent.action == KeyEvent.ACTION_UP) {
                    return true
                }
                if(i == KeyEvent.KEYCODE_BACK){
                    EventBus.getDefault().post(FeedbackDismissEvent())
                    return true
                }

                if (i == KeyEvent.KEYCODE_DPAD_DOWN && holder.layoutPosition == data.size - 1
                    || i == KeyEvent.KEYCODE_DPAD_UP && holder.layoutPosition == 0
                    || i == KeyEvent.KEYCODE_DPAD_RIGHT
                ) {
                    return true
                }
                if (i == KeyEvent.KEYCODE_DPAD_LEFT) {
                    EventBus.getDefault().post(FeedbackTabEvent(0))
                    return true
                }
                return false
            }
        })

    }

}