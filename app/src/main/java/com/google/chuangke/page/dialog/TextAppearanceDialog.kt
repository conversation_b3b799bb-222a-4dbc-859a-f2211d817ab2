package com.google.chuangke.page.dialog

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.util.Log
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.RadioButton
import android.widget.RadioGroup
import android.widget.TextView
import androidx.core.view.get
import com.google.chuangke.R
import com.google.chuangke.base.DisableSpeechDialog
import com.google.chuangke.common.Constants
import com.google.chuangke.common.UserHelper
import com.google.chuangke.util.SPUtils

class TextAppearanceDialog(
    context: Context
) : DisableSpeechDialog(context) {

    private var usingScale = 1.0f

    private lateinit var mDisplay: TextView
    private lateinit var mHeadline: TextView
    private lateinit var mBody: TextView
    private lateinit var mCaption: TextView
    private lateinit var mGroup: RadioGroup
    private lateinit var mApply: Button
    private lateinit var mNextLaunch: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window!!.requestFeature(Window.FEATURE_NO_TITLE)
        val view: View =
            LayoutInflater.from(context).inflate(R.layout.dialog_text_appearance, null)
        setContentView(view)
        window!!.setBackgroundDrawable(ColorDrawable(0x00000000))
        window!!.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
        )

        mDisplay = findViewById(R.id.tv1)
        mHeadline = findViewById(R.id.tv2)
        mBody = findViewById(R.id.tv3)
        mCaption = findViewById(R.id.tv4)
        mGroup = findViewById(R.id.group)
        mApply = findViewById(R.id.apply)
        mNextLaunch = findViewById(R.id.nextLaunch)

        usingScale =
            SPUtils.getObject(context, Constants.SP_KEY_FONT_SCALE, 1.0f) as Float

        requestFocus(usingScale)

        Log.e("updateViewTreeFontSize", "usingScale:$usingScale")

        updateViewTreeFontSize(findViewById(R.id.setting), 1.0f)

        initListener()
    }

    private var animator: ValueAnimator? = null
    fun initListener() {
        mGroup.setOnCheckedChangeListener { group, checkedId ->
            when (checkedId) {
                R.id.tvSmall -> applyFontSize(1)
                R.id.tvMedium -> applyFontSize(2)
                R.id.tvLarge -> applyFontSize(3)
                R.id.tvExtra -> applyFontSize(4)
            }
        }

        mApply.setOnClickListener {
            UserHelper.getInstance().exit()
        }

        mNextLaunch.setOnClickListener {
            dismiss()
        }

        mApply.setOnFocusChangeListener { view, hasFocus ->
            if (hasFocus) {
                animator = ValueAnimator.ofFloat(0f, 1f).apply {
                    duration = 600
                    repeatMode = ValueAnimator.REVERSE
                    repeatCount = ValueAnimator.INFINITE
                    addUpdateListener {
                        val alpha = it.animatedValue as Float
                        view.background.alpha = (alpha * 255).toInt()
                    }
                }
                animator!!.start()
            } else {
                animator?.cancel()
                view.background.alpha = 255
            }
        }

        mNextLaunch.setOnFocusChangeListener { view, hasFocus ->
            if (hasFocus) {
                val animator = ValueAnimator.ofFloat(0f, 1f).apply {
                    duration = 600
                    addUpdateListener {
                        val alpha = it.animatedValue as Float
                        view.background.alpha = (alpha * 255).toInt()
                    }
                }
                animator.start()
            } else {
                view.background.alpha = 255
            }
        }

    }

    private fun applyFontSize(fontSize: Int) {
        val textAppearance = when (fontSize) {
            1 -> 0.8f
            2 -> 1.0f
            3 -> 1.2f
            4 -> 1.4f
            else -> 1.0f
        }

        SPUtils.put(context, Constants.SP_KEY_FONT_SCALE, textAppearance)

        updateViewTreeFontSize(findViewById(R.id.llSample), textAppearance)

        usingScale = textAppearance
    }

    private fun updateViewTreeFontSize(view: View, scale: Float) {
        if (view is ViewGroup) {
            for (i in 0 until view.childCount) {
                updateViewTreeFontSize(view.getChildAt(i), scale)
            }
        } else if (view is TextView) {
            val originalSize = view.textSize / usingScale
            val currentPx = originalSize * scale
            Log.e("updateViewTreeFontSize", "originalSize:${originalSize},currentPx:$currentPx")
            view.setTextSize(TypedValue.COMPLEX_UNIT_PX, currentPx)
        }
    }

    fun requestFocus(scale: Float) {
        val index = when (scale) {
            0.8f -> 0
            1.0f -> 1
            1.2f -> 2
            1.4f -> 3
            else -> 0
        }

        mGroup[index].requestFocus()
        (mGroup[index] as RadioButton).isChecked = true
    }

}