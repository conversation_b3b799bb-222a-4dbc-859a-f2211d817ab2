package com.google.chuangke.page.menu.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.page.center.MenuItem
import com.google.chuangke.page.center.MenuItem.*

class MenuPresenter : BasePresenter<MenuItem>() {

    override fun layoutId(): Int {
        return R.layout.item_fragment_menu
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf(R.id.tv_fragment_menu_account)
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): <PERSON><PERSON><PERSON> {

        // 不处理其他控件后续事件
        if (event.action == KeyEvent.ACTION_UP) {
            return false
        }

        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                needKeep = true
            }
        }

        return false
    }

    override fun bindViewHolder(view: View, item: MenuItem) {
        val text: Int
        val icon: Int
        when (item) {
            ACCOUNT -> {
                text = R.string.menu_my_account
                icon = R.drawable.selector_menu_account
            }
            INFO -> {
                text = R.string.menu_info
                icon = R.drawable.selector_menu_info
            }
            LOCK -> {
                text = R.string.menu_lock
                icon = R.drawable.selector_menu_lock
            }
            APP -> {
                text = R.string.menu_mobile_app
                icon = R.drawable.selector_menu_app
            }
            NOTIFICATION -> {
                text = R.string.menu_notification
                icon = R.drawable.selector_menu_notification
            }
        }

        (view as TextView).let {
            it.text = context.getString(text)
            it.setCompoundDrawablesWithIntrinsicBounds(
                icon, 0, 0, 0
            )
        }
    }
}