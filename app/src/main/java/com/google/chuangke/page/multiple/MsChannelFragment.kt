package com.google.chuangke.page.multiple

import android.annotation.SuppressLint
import android.view.KeyEvent
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.google.chuangke.R
import com.google.chuangke.base.BaseDataBindingFragment
import com.google.chuangke.common.Config
import com.google.chuangke.common.UnitCallback
import com.google.chuangke.databinding.FragmentChannelMsBinding
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.page.center.presenter.ChannelPresenter
import com.google.chuangke.page.center.presenter.TagPresenter
import com.google.chuangke.page.dialog.UnlockDialog
import com.google.chuangke.page.multiple.data.CategoryState
import com.google.chuangke.page.multiple.data.ChannelState
import com.google.chuangke.page.multiple.data.MsChannelViewModel
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel

@SuppressLint("SetTextI18n")
class MsChannelFragment :
    BaseDataBindingFragment<FragmentChannelMsBinding>(FragmentChannelMsBinding::inflate) {
    private val mMsChannelViewModel: MsChannelViewModel by viewModel()
    private lateinit var mTagAdapter: ArrayObjectAdapter
    private lateinit var mChannelAdapter: ArrayObjectAdapter

    override fun initView() {
        binding.rvFragmentChannelTag.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(TagPresenter().also {
                it.needKeep = true
            }).also {
                mTagAdapter = it
            })

        binding.rvFragmentChannelChannel.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(ChannelPresenter()).also {
                mChannelAdapter = it
            })
    }

    override fun initListener() {
        binding.rvFragmentChannelTag.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                val currentTag = mTagAdapter.get(position) as TagBean
                lifecycleScope.launch {
                    mMsChannelViewModel.cursorPositionFlow.emit(currentTag)
                }
            }
        })

        binding.rvFragmentChannelChannel.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                child?.let {
                    binding.tvFragmentChannelsChannelCount.text =
                        "${position + 1}/${mChannelAdapter.size()}"
                }
            }
        })

        binding.rvFragmentChannelTag.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    (requireActivity() as MultipleScreenActivity).disChannelFragment()
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    if (mChannelAdapter.size() == 0) {
                        return@setOnKeyInterceptListener true
                    } else {
                        binding.rvFragmentChannelChannel.requestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mTagAdapter.size() > 0 && binding.rvFragmentChannelTag.selectedPosition == 0) {
                        binding.rvFragmentChannelTag.selectedPosition = mTagAdapter.size() - 1
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    if (binding.rvFragmentChannelTag.childCount > 0 && binding.rvFragmentChannelTag.selectedPosition == mTagAdapter.size() - 1) {
                        binding.rvFragmentChannelTag.selectedPosition = 0
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_PAGE_UP -> {
                    var tempPosition =
                        binding.rvFragmentChannelTag.selectedPosition - PAGE_COUNT
                    if (tempPosition < 0) {
                        tempPosition = 0
                    }
                    binding.rvFragmentChannelTag.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    var tempPosition =
                        binding.rvFragmentChannelTag.selectedPosition + PAGE_COUNT
                    if (tempPosition >= mTagAdapter.size()) {
                        tempPosition = mTagAdapter.size() - 1
                    }
                    binding.rvFragmentChannelTag.selectedPosition = tempPosition
                }
            }
            return@setOnKeyInterceptListener false
        }

        binding.rvFragmentChannelChannel.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    binding.rvFragmentChannelTag.requestFocus()
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_BACK -> {
                    (requireActivity() as MultipleScreenActivity).disChannelFragment()
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mChannelAdapter.size() > 0 && binding.rvFragmentChannelChannel.selectedPosition == 0) {
                        binding.rvFragmentChannelChannel.selectedPosition =
                            mChannelAdapter.size() - 1
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    if (mChannelAdapter.size() > 0 && binding.rvFragmentChannelChannel.selectedPosition == mChannelAdapter.size() - 1) {
                        binding.rvFragmentChannelChannel.selectedPosition = 0
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_NUMPAD_ENTER -> {
                    val bean =
                        mChannelAdapter.get(binding.rvFragmentChannelChannel.selectedPosition) as ChannelBean

                    if (bean.id != Config.getInstance().currentPlayChannel.id && bean.locked == 1) {
                        showUnlockDialog {
                            (requireActivity() as MultipleScreenActivity).onProgramSelect(bean)
                        }
                    } else {
                        (requireActivity() as MultipleScreenActivity).onProgramSelect(bean)
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_PAGE_UP -> {
                    var tempPosition =
                        binding.rvFragmentChannelChannel.selectedPosition - PAGE_COUNT
                    if (tempPosition < 0) {
                        tempPosition = 0
                    }
                    binding.rvFragmentChannelChannel.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    var tempPosition =
                        binding.rvFragmentChannelChannel.selectedPosition + PAGE_COUNT
                    if (tempPosition >= mChannelAdapter.size()) {
                        tempPosition = mChannelAdapter.size() - 1
                    }
                    binding.rvFragmentChannelChannel.selectedPosition = tempPosition
                }
            }
            return@setOnKeyInterceptListener false
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initObserve() {
        lifecycleScope.launch {
            mMsChannelViewModel.tagState.collect {
                when (it) {
                    is CategoryState.Success -> {
                        mTagAdapter.setItems(it.list, null)
                        if (it.list.isNotEmpty()) binding.rvFragmentChannelTag.selectedPosition = 0
                        binding.rvFragmentChannelTag.requestFocus()
                    }
                }
            }
        }

        lifecycleScope.launch {
            mMsChannelViewModel.channelState.collect {
                when (it) {
                    is ChannelState.Success -> {
                        mChannelAdapter.setItems(it.list, null)
                        if (it.list.isNotEmpty()) {
                            binding.rvFragmentChannelChannel.selectedPosition = 0
                            binding.tvFragmentChannelsChannelCount.text =
                                "1/${mChannelAdapter.size()}"
                        } else {
                            binding.tvFragmentChannelsChannelCount.text =
                                "0/0"
                        }
                    }
                }
            }
        }
    }

    private var mUnlockDialog: UnlockDialog? = null

    private fun showUnlockDialog(callback: UnitCallback) {
        if (mUnlockDialog == null) {
            mUnlockDialog = UnlockDialog(requireActivity(), R.style.Dialog, callback = callback)
        } else {
            mUnlockDialog!!.callback = callback
        }

        if (!mUnlockDialog!!.isShowing) {
            mUnlockDialog!!.showDialog()
        }
    }

    companion object {
        private const val PAGE_COUNT = 9
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            binding.rvFragmentChannelChannel.requestFocus()
        }
    }

}