package com.google.chuangke.page.menu

import android.graphics.Paint
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.EventFilter
import com.google.chuangke.common.event.*
import com.google.chuangke.data.menu.MenuInfoViewModel
import com.google.chuangke.database.DBApi
import com.google.chuangke.ext.toast
import com.google.chuangke.common.UpgradeHelper
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.koin.androidx.viewmodel.ext.android.viewModel

class MenuInfoFragment : BaseFragment() {

    private val mMenuInfoViewModel: MenuInfoViewModel by viewModel()

    private lateinit var mTvVersion: TextView
    private lateinit var mTvUpdate: TextView
    private lateinit var mTvClearHistory: TextView
    private lateinit var mTvFeedback: TextView
    private lateinit var mRvDetail: RecyclerView

    private lateinit var mMenuInfoDetailAdapter: MenuInfoDetailAdapter

    private lateinit var eventFilter: EventFilter

    override fun layoutId(): Int {
        return R.layout.fragment_menu_info
    }

    override fun initView(view: View) {
        mTvVersion = view.findViewById(R.id.tv_fragment_menu_info_version)
        mTvUpdate = view.findViewById(R.id.tv_fragment_menu_info_update)
        mTvClearHistory = view.findViewById(R.id.tv_fragment_menu_info_clear_history)
        mTvFeedback = view.findViewById(R.id.tv_fragment_menu_info_feedback)
        mRvDetail = view.findViewById(R.id.rv_fragment_menu_info_detail)
        mTvFeedback.paintFlags = Paint.UNDERLINE_TEXT_FLAG or Paint.ANTI_ALIAS_FLAG

        mRvDetail.layoutManager =
            LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
        mRvDetail.adapter = MenuInfoDetailAdapter().also { mMenuInfoDetailAdapter = it }

        mTvVersion.text = try {
            requireActivity().packageManager.getPackageInfo(
                requireActivity().packageName, 0
            ).versionName
        } catch (e: Exception) {
            getString(R.string.menu_info_not_available)
        }
        eventFilter = EventFilter.Builder().setType(EventFilter.TYPE_OUT_TIME).setTime(2000).build()
    }

    override fun initListener() {
        mTvUpdate.setOnClickListener {
            if (eventFilter.filter()) {
                UpgradeHelper(requireActivity()).checkUpdate(true)
            }
        }

        mTvClearHistory.setOnClickListener {
            DBApi.getInstance().clearHistory()
            requireContext().toast { getString(R.string.menu_info_history_clear_done) }
        }

        mTvFeedback.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    return@setOnKeyListener true
                }
            }
            false
        }

        mTvFeedback.setOnClickListener {
            EventBus.getDefault().post(FeedbackDialogEvent())
        }
    }

    override fun initObserve() {
        mMenuInfoViewModel.detailLiveData.observe(this) {
            mMenuInfoDetailAdapter.setList(it)
        }
    }

    @Subscribe(sticky = true)
    fun onContactInfoEvent(event: ContactInfoEvent) {
        mMenuInfoViewModel.initDetail()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    fun requestFocus() {
        mTvUpdate.requestFocus()
    }
}

class MenuInfoDetailAdapter :
    BaseQuickAdapter<Pair<String?, String?>, BaseViewHolder>(R.layout.item_fragment_menu_info_detail) {

    override fun convert(holder: BaseViewHolder, item: Pair<String?, String?>) {
        val str = StringBuilder()
        if (!TextUtils.isEmpty(item.first)) {
            str.append(item.first)
            str.append("：")
        }
        if (!TextUtils.isEmpty(item.second)) {
            str.append(item.second)
        }

        holder.setText(R.id.tv_item_fragment_menu_info_detail, str)
    }

}