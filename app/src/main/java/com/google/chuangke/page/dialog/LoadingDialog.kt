package com.google.chuangke.page.dialog

import android.app.Activity
import android.app.Dialog
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import com.google.chuangke.R
import com.google.chuangke.page.MainActivity
import com.google.chuangke.view.MyLVCircularJump
import java.util.*

class LoadingDialog(activity: Activity, themeId: Int) :
    Dialog(activity, themeId) {
    private lateinit var mLVCircularSmile: MyLVCircularJump

    override fun onStart() {
        super.onStart()
        // 不获取焦点，让MainActivity去处理
        val window: Window = window!!
        window.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        )
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_loading)
        initView()
    }

    private fun initView() {
        mLVCircularSmile = findViewById(R.id.lv_circularring)
    }

    fun showDialog() {
        show()
        mLVCircularSmile.stopAnim()
        mLVCircularSmile.startAnim()
    }

    fun dismissDialog(){
        dismiss()
    }

}

class LoadingDialogHelper {
    private var loadingDialog: LoadingDialog? = null

    companion object {
        fun getInstance() = InstanceHelper.instance
    }

    object InstanceHelper {
        val instance = LoadingDialogHelper()
    }

    fun show(activity: Activity) {
        if (loadingDialog != null && loadingDialog!!.isShowing) {
            execute { dismiss() }
            return
        }
        loadingDialog = LoadingDialog(activity, R.style.Dialog)
        loadingDialog!!.setOwnerActivity(activity)
        loadingDialog!!.showDialog()

        execute { dismiss() }
    }

    fun dismiss() {
        loadingDialog?.let {
            if (it.isShowing)
                it.dismissDialog()
        }
    }

    fun dismiss(activity: MainActivity) {
        loadingDialog?.let {
            if (it.isShowing) {
                it.dismissDialog()
                activity.catchFocus()
            }
        }
    }

    private var timer = Timer()
    private var timerTask: TimerTask? = null
    private fun execute(runnable: () -> Unit) {
        timerTask?.cancel()
        timerTask = object : TimerTask() {
            override fun run() {
                runnable.invoke()
            }
        }
        timer.schedule(timerTask, 30000)
    }
}
