package com.google.chuangke.page.center.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.data.TAG_ADD
import com.google.chuangke.data.TAG_SEARCH
import com.google.chuangke.entity.CAN_EDIT
import com.google.chuangke.entity.TagBean

class TagPresenter : BasePresenter<TagBean>() {

    override fun layoutId(): Int {
        return R.layout.item_tags
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf(R.id.tv_item_tag)
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {

        // 不处理其他控件后续事件
        if (event.action == KeyEvent.ACTION_UP) {
            return false
        }

        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                needKeep = true
            }
        }

        return false
    }

    override fun bindViewHolder(view: View, item: TagBean) {
        var icon: Int = when (item.id) {
            TAG_SEARCH -> {
                R.drawable.selector_menu_search
            }

            TAG_ADD -> {
                R.drawable.selector_add
            }

            else -> {
                0
            }
        }

        if ((item.custom ?: 0) == CAN_EDIT) {
            icon = R.drawable.selector_edit
        }

        (view as TextView).let {
            it.text = item.name
            it.setCompoundDrawablesWithIntrinsicBounds(
                icon, 0, 0, 0
            )
        }
    }

    fun clear() {
        map.clear()
    }

}