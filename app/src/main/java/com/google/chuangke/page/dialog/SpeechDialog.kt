package com.google.chuangke.page.dialog

import android.animation.ObjectAnimator
import android.app.Dialog
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.*
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import android.widget.TextView
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.recyclerview.widget.RecyclerView
import com.google.chuangke.R
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.page.dialog.presenter.VoiceSearchPresenter
import com.google.chuangke.util.ScreenUtils

class SpeechDialog(
    val mContext: Context,
    val onSpeechResultClick: (ChannelBean) -> Unit,
    val startSpeech: () -> Unit
) : Dialog(mContext) {

    private lateinit var mIvCircle: ImageView
    private lateinit var mIvArrowLeft: ImageView
    private lateinit var mIvArrowRight: ImageView
    private lateinit var mTvResult: TextView
    private lateinit var mTvSpeech: TextView
    private lateinit var mTvVoiceContent: TextView
    private lateinit var mRvResult: HorizontalGridView

    private var searching = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window!!.requestFeature(Window.FEATURE_NO_TITLE)
        val view: View = LayoutInflater.from(mContext).inflate(R.layout.dialog_speech, null)
        setContentView(view)
        window!!.setBackgroundDrawable(ColorDrawable(0x00000000))
        window!!.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
        )

        initView(view)

        initListener()

    }

    private fun initListener() {
        mTvSpeech.setOnKeyListener { v, keyCode, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                KeyEvent.KEYCODE_SEARCH -> {
                    search()
                    return@setOnKeyListener true
                }
            }

            return@setOnKeyListener false
        }

        mRvResult.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_SEARCH -> {
                    search()
                    return@setOnKeyInterceptListener true
                }
            }

            return@setOnKeyInterceptListener false
        }

        mRvResult.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)

//                val = mTagAdapter.indexOf(child!!.itemView.tag) == 0

                if (position == 3 && mResultAdapter.size() > 5) {
                    mIvArrowLeft.visibility = View.VISIBLE
                }

                if (position == 2 && mResultAdapter.size() > 5) {
                    mIvArrowLeft.visibility = View.INVISIBLE
                }

                if (position == mResultAdapter.size() - 3 && mResultAdapter.size() > 5) {
                    mIvArrowRight.visibility = View.INVISIBLE
                }

                if (position == mResultAdapter.size() - 4 && mResultAdapter.size() > 5) {
                    mIvArrowRight.visibility = View.VISIBLE
                }
            }
        })
    }

    private fun search() {
        if (searching) {
            Log.e("Speech123", "searching $searching")
            return
        }
        mSpeechHandler.removeMessages(SPEECH_DIALOG_DISMISS)
        startSpeech()
    }

    private lateinit var mResultAdapter: ArrayObjectAdapter

    private fun initView(view: View) {
        mIvCircle = view.findViewById(R.id.iv_speech_circle)
        mIvArrowLeft = view.findViewById(R.id.iv_speech_left)
        mIvArrowRight = view.findViewById(R.id.iv_speech_right)
        mTvSpeech = view.findViewById(R.id.tv_dialog_speech)
        mTvResult = view.findViewById(R.id.tv_dialog_result)
        mRvResult = view.findViewById(R.id.vg_speech_search)
        mTvVoiceContent = view.findViewById(R.id.tv_voice_content)

        mRvResult.adapter = ItemBridgeAdapter(ArrayObjectAdapter(VoiceSearchPresenter()).also {
            mResultAdapter = it
        }).also {
            it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                    super.onCreate(viewHolder)
                    viewHolder.itemView.setOnClickListener { itemView ->
                        // 频道切换
                        onSpeechResultClick(itemView.tag as ChannelBean)
                    }
                }
            })
        }
    }

    private var speechReady = false

    fun changeState(state: Int) {
        Log.e("Speech123", "state $state")
        if (state == 0) {
            speechReady = true
        }
        if (state == 1 && !speechReady) {
            return
        }

        if (state == 5 || state == 6) {
            speechReady = false
            searching = false
        } else {
            searching = true
        }

    }

    /**
     * 显示状态
     */
    fun showState(text: String) {
        if (!::mTvSpeech.isInitialized) {
            mSpeechHandler.postDelayed({
                mTvSpeech.text = text
            }, 500)
        } else {
            mTvSpeech.text = text
        }
    }

    /**
     * 显示文字结果
     */
    fun showResult(text: String) {
        mTvResult.text = text
        mTvResult.translationX = 0f

        // Tips
        mTvVoiceContent.startAnimation(alphaAnimation)

        mTvResult.postDelayed({
            val width = mTvResult.left
            val animator = ObjectAnimator.ofFloat(
                mTvResult, "translationX", -(width - marginLest).toFloat()
            )
            animator.duration = 2000
            animator.start()
        }, 500L)
    }

    fun dismissDialog() {
        dismiss()
    }

    fun setResultList(list: MutableList<ChannelBean>) {
        // 重置左右箭头
        mIvArrowRight.visibility = View.INVISIBLE
        mIvArrowLeft.visibility = View.INVISIBLE

        val tempList = mutableListOf<ChannelBean>()
        for (i in list.indices) {
            if (i == 10) {
                break
            }
            tempList.add(list[i])
        }
        mResultAdapter.setItems(tempList, null)
        if (list.size > 0) {
            mRvResult.requestFocus()
            mRvResult.selectedPosition = 0
        }

        // 大于5个战士右滑箭头
        if (list.size > 5) {
            mIvArrowRight.visibility = View.VISIBLE
        } else {
            mIvArrowRight.visibility = View.INVISIBLE
        }

        if (list.size < 2) {
            // Do something with spokenText
            val msg = mSpeechHandler.obtainMessage()
            msg.what = SPEECH_DIALOG_DISMISS
            mSpeechHandler.sendMessageDelayed(msg, SPEECH_DIALOG_DISMISS_TIME)
        }
    }

    fun focusScale(scale: Boolean) {
        if (!::mIvCircle.isInitialized) {
            mSpeechHandler.postDelayed({
                executeScale(scale)
            }, 500)
        } else {
            executeScale(scale)
        }

    }

    private fun executeScale(scale: Boolean) {
        if (scale) {
            mIvCircle.visibility = View.VISIBLE
            mIvCircle.startAnimation(circleAnimationSet)
        } else {
            mIvCircle.visibility = View.INVISIBLE
            mIvCircle.clearAnimation()
        }
    }

    /**
     * 动画相关
     */
    private var circleAnimationSet: AnimationSet
    private val alphaAnimation: AlphaAnimation

    private val marginLest: Int = ScreenUtils.dp2px(mContext, 50)

    init {

        //缩放动画
        val animation = ScaleAnimation(
            1.0f,
            1.5f,
            1.0f,
            1.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f,
            Animation.RELATIVE_TO_SELF,
            0.5f
        )
        animation.duration = 1000
        animation.fillAfter = true
        animation.repeatMode = Animation.REVERSE
        animation.repeatCount = Animation.INFINITE

        //透明度动画
        val animation1 = AlphaAnimation(1.0f, 0.8f)
        animation1.duration = 1000
        animation1.repeatCount = Animation.INFINITE
        animation1.repeatMode = Animation.REVERSE
        animation1.fillAfter = true

        //装入AnimationSet中
        circleAnimationSet = AnimationSet(true)
        circleAnimationSet.addAnimation(animation)
        circleAnimationSet.addAnimation(animation1)


        alphaAnimation = AlphaAnimation(1.0f, 0.0f)
        alphaAnimation.repeatCount = 1
        alphaAnimation.repeatMode = Animation.REVERSE
        alphaAnimation.duration = 1000

    }

    @Suppress("UNCHECKED_CAST")
    private val mSpeechHandler = Handler(Looper.myLooper()!!) {
        when (it.what) {
            SPEECH_DIALOG_DISMISS -> {
                dismissDialog()
                if (mResultAdapter.size() > 0) mResultAdapter.get(0)?.let { channel ->
                    onSpeechResultClick(channel as ChannelBean)
                }
            }
        }
        return@Handler false
    }

    companion object {
        const val SPEECH_DIALOG_DISMISS = 0x120
        const val SPEECH_DIALOG_DISMISS_TIME = 3000L
    }

    override fun dismiss() {
        mSpeechHandler.removeCallbacksAndMessages(null)
        super.dismiss()
    }

    override fun onStart() {
        super.onStart()

        mTvResult.text = "..."
        mResultAdapter.setItems(mutableListOf<ChannelBean>(), null)
    }
}