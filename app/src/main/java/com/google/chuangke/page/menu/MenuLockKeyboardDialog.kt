package com.google.chuangke.page.menu

import android.app.Activity
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.base.DisableSpeechDialog
import com.google.chuangke.page.adapter.RecyclerExtras

interface OnMenuLockKeyboardClickListener {
    fun onClick(word: String)
}

class MenuLockKeyboardDialog(
    activity: Activity,
    themeId: Int
) : DisableSpeechDialog(activity, themeId) {

    private lateinit var mRecyclerView: RecyclerView
    private lateinit var mMenuLockKeyboardAdapter: MenuLockKeyboardAdapter

    var mOnMenuLockKeyboardClickListener: OnMenuLockKeyboardClickListener? = null

    private val mKeyDEL: String
    private val mKeyDONE: String

    private val keywordList = mutableListOf<String>()

    init {
        for (i in 0..9) {
            keywordList.add("$i")
        }
        mKeyDEL = context.getString(R.string.key_board_del)
        mKeyDONE = context.getString(R.string.key_board_done)
        keywordList.add(mKeyDEL)
        keywordList.add(mKeyDONE)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_menu_lock_keyboard)
        initView()
        initListener()
    }

    private fun initView() {
        mRecyclerView = findViewById(R.id.rv_dialog_menu_lock_keyboard)
        mRecyclerView.layoutManager = GridLayoutManager(context, 3)
        mRecyclerView.adapter = MenuLockKeyboardAdapter().also { mMenuLockKeyboardAdapter = it }
        mMenuLockKeyboardAdapter.setList(keywordList)
    }

    private fun initListener() {

        mMenuLockKeyboardAdapter.setOnItemClickListener { adapter, _, position ->
            if (position < adapter.data.size) {
                mOnMenuLockKeyboardClickListener?.onClick(adapter.data[position] as String)
            }
        }

        mMenuLockKeyboardAdapter.mOnItemKeyListener = object : RecyclerExtras.OnItemKeyListener {
            override fun onItemKey(view: View, position: Int, keyEvent: KeyEvent, keyCode: Int) {
                when (keyCode) {
                    in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                        mOnMenuLockKeyboardClickListener?.onClick((keyCode - 7).toString())
                    }

                    KeyEvent.KEYCODE_DEL -> {
                        mOnMenuLockKeyboardClickListener?.onClick(mKeyDEL)
                    }
                }
            }
        }
    }
}

class MenuLockKeyboardAdapter :
    BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_menu_lock_keyboard) {

    var mOnItemKeyListener: RecyclerExtras.OnItemKeyListener? = null

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tv_item_menu_lock_keyboard, item)

        holder.getView<TextView>(R.id.tv_item_menu_lock_keyboard)
            .setOnKeyListener { v, keyCode, event ->

                if (event.action == KeyEvent.ACTION_UP) {
                    return@setOnKeyListener false
                }

                when (keyCode) {
                    in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                        mOnItemKeyListener?.onItemKey(v, holder.layoutPosition, event, keyCode)
                        return@setOnKeyListener true
                    }
                    KeyEvent.KEYCODE_DEL -> {
                        mOnItemKeyListener?.onItemKey(v, holder.layoutPosition, event, keyCode)
                        return@setOnKeyListener true
                    }
                }
                return@setOnKeyListener false
            }
    }

}