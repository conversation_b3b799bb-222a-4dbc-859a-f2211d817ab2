package com.google.chuangke.page.menu

import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.ext.toast
import com.google.chuangke.http.HttpCallback
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.page.adapter.RecyclerExtras
import com.google.chuangke.util.DateUtil
import com.google.chuangke.view.MyEmptyView

class MenuNotificationFragment : BaseFragment() {

    private lateinit var mRecyclerView: RecyclerView
    private val mMyEmptyView = MyEmptyView()

    private lateinit var mMenuNotificationAdapter: MenuNotificationAdapter

    override fun layoutId(): Int {
        return R.layout.fragment_menu_notification
    }

    override fun initView(view: View) {
        mRecyclerView = view.findViewById(R.id.rv_fragment_menu_notification)

        mRecyclerView.layoutManager =
            LinearLayoutManager(requireActivity(), RecyclerView.VERTICAL, false)
        mRecyclerView.adapter = MenuNotificationAdapter().also { mMenuNotificationAdapter = it }
        mMyEmptyView.setAdapterView(layoutInflater, mMenuNotificationAdapter)

        initData()
    }

    override fun initListener() {
    }

    private fun initData() {
        val params = JSONObject()
        HttpHelper.getInstance().postApi("getNotificationList", params, object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    Handler(Looper.getMainLooper()).post {
                        requireActivity().toast(msg)
                    }
                    return
                }

                val reData = jsonObject.getString("reData")
                val respJson = JSONArray.parseArray(reData)
                val list = mutableListOf<MenuNotificationBean>()
                for (i in respJson) {
                    list.add(
                        MenuNotificationBean(
                            (i as JSONObject).getString("content"),
                            (i as JSONObject).getLong("createTime")
                        )
                    )
                }

                requireActivity().runOnUiThread { mMenuNotificationAdapter.setList(list) }
            }

            override fun onError(err: String) {
                requireActivity().runOnUiThread { requireActivity().toast(err) }
            }

        })
    }

}

class MenuNotificationBean(var content: String?, var date: Long)

class MenuNotificationAdapter :
    BaseQuickAdapter<MenuNotificationBean, BaseViewHolder>(R.layout.item_fragment_menu_notification) {

    private var onItemKeyListener: RecyclerExtras.OnItemKeyListener? = null

    fun setOnItemKeyListener(onItemKeyListener: RecyclerExtras.OnItemKeyListener?) {
        this.onItemKeyListener = onItemKeyListener
    }

    override fun convert(holder: BaseViewHolder, item: MenuNotificationBean) {
        holder.setText(R.id.tv_item_fragment_menu_notification_content, item.content)
        holder.setText(R.id.tv_item_fragment_menu_notification_date,
            item.date.let { DateUtil.getYMDTime((it / 1000).toInt()) })


        holder.itemView.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(view: View, i: Int, keyEvent: KeyEvent): Boolean {

                if (keyEvent.action == KeyEvent.ACTION_UP) {
                    return false
                }

                onItemKeyListener?.onItemKey(
                    view,
                    holder.layoutPosition,
                    keyEvent,
                    i
                )

                if (i == KeyEvent.KEYCODE_DPAD_UP && holder.layoutPosition == 0
                    || i == KeyEvent.KEYCODE_DPAD_RIGHT
                    || i == KeyEvent.KEYCODE_DPAD_DOWN && holder.layoutPosition == data.size - 1
                ) {
                    return true
                }

                return false
            }
        })
    }
}

