package com.google.chuangke.page.center

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.KeyEvent
import androidx.core.view.get
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import com.google.chuangke.R
import com.google.chuangke.base.BaseActivity
import com.google.chuangke.common.Config
import com.google.chuangke.common.event.ChannelPlayEvent
import com.google.chuangke.data.ChannelSearchViewModel
import com.google.chuangke.data.TAG_ALL
import com.google.chuangke.databinding.ActivityChannelSearchBinding
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.page.menu.presenter.ChannelSearchPresenter
import com.google.chuangke.page.presenter.SearchKeyPresenter
import org.greenrobot.eventbus.EventBus
import org.koin.androidx.viewmodel.ext.android.viewModel

class ChannelSearchActivity :
    BaseActivity<ActivityChannelSearchBinding>(ActivityChannelSearchBinding::inflate) {

    private val mChannelSearchViewModel: ChannelSearchViewModel by viewModel()
    private lateinit var mKeyAdapter: ArrayObjectAdapter
    private lateinit var mMovieAdapter: ArrayObjectAdapter

    private val letterList = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    private var inputText: StringBuilder = StringBuilder("")

    private val period = '.'
    private val slash = '/'
    private val minus = '-'
    private val apostrophe = '\''
    private val comma = ','

    private val keyList = mutableListOf<Char>()

    init {

        for (i in 0..25) {
            keyList.add(Char(65 + i))
        }
        for (j in 0..9) {
            keyList.add(Char(48 + j))
        }
        keyList.add('/')
        keyList.add(':')
        keyList.add('&')
        keyList.add('-')
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (event.action != KeyEvent.ACTION_UP && event.keyCode == KeyEvent.KEYCODE_SEARCH) {
            return true
        }
        return super.onKeyDown(keyCode, event)
    }

    override fun initView() {
        binding.rvActivityChannelSearchKey.setNumColumns(5)
        binding.rvActivityChannelSearchKey.setGravity(Gravity.CENTER_HORIZONTAL)
        binding.rvActivityChannelSearchKey.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(SearchKeyPresenter()).also {
                mKeyAdapter = it
            }).also {
                it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                    override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                        super.onCreate(viewHolder)
                        viewHolder.itemView.setOnClickListener { itemView ->
                            if (inputText.length < 30) {
                                inputText.append(itemView.tag)
                                search()
                            }
                        }
                    }
                })
            }

        binding.rvActivityChannelSearchResult.setNumColumns(2)
        binding.rvActivityChannelSearchResult.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(ChannelSearchPresenter()).also {
                mMovieAdapter = it
            }).also {
                it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                    override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                        super.onCreate(viewHolder)
                        viewHolder.itemView.setOnClickListener { itemView ->
                            val channelBean = itemView.tag as ChannelBean
                            if (channelBean.id != Config.getInstance().currentPlayChannel.id) {
                                EventBus.getDefault().post(
                                    ChannelPlayEvent(TagBean(id = TAG_ALL), channelBean, null)
                                )
                            }
                            finish()
                        }
                    }
                })
            }
        initObserve()
        initData()
    }

    private fun initData() {
        mKeyAdapter.setItems(keyList, null)
        binding.rvActivityChannelSearchKey.postDelayed(
            { binding.rvActivityChannelSearchKey[0].requestFocus() },
            100L
        )
    }

    private fun initObserve() {
        mChannelSearchViewModel.searchChannelLiveData.observe(this) {
            binding.tvActivityChannelSearchResultCount.text =
                "${getString(R.string.menu_search_result)}${it.size}"
            mMovieAdapter.setItems(it, null)
        }

    }

    override fun initListener() {

        binding.tvActivityChannelSearchBack.setOnClickListener {
            finish()
        }

        binding.tvActivityChannelSearchBack.setOnKeyListener { v, keyCode, keyEvent ->

            if (keyEvent.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyEvent.keyCode) {
                KeyEvent.KEYCODE_DPAD_RIGHT, KeyEvent.KEYCODE_DPAD_LEFT, KeyEvent.KEYCODE_DPAD_UP -> {
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    binding.rvActivityChannelSearchKey.requestFocus()
                    return@setOnKeyListener true
                }
            }
            return@setOnKeyListener false
        }

        binding.rvActivityChannelSearchKey.setOnKeyInterceptListener { keyEvent ->

            if (keyEvent.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (keyEvent.keyCode) {
                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    if (mMovieAdapter.size() == 0 && mKeyAdapter.indexOf(binding.rvActivityChannelSearchKey.focusedChild.tag) % 5 == 4) {
                        return@setOnKeyInterceptListener true
                    }
                }

                in KeyEvent.KEYCODE_A..KeyEvent.KEYCODE_Z -> {
                    if (inputText.length < 30) {
                        inputText.append(letterList[keyEvent.keyCode - 29])
                        search()
                    }
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    if (inputText.length < 30) {
                        inputText.append((keyEvent.keyCode - 7).toString())
                        search()
                    }
                }

                KeyEvent.KEYCODE_SPACE -> {
                    if (inputText.length < 30) {
                        inputText.append(" ")
                        search()
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_PERIOD -> {
                    if (inputText.length < 30) {
                        inputText.append(period)
                        search()
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_SLASH -> {
                    if (inputText.length < 30) {
                        inputText.append(slash)
                        search()
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_MINUS -> {
                    if (inputText.length < 30) {
                        inputText.append(minus)
                        search()
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_APOSTROPHE -> {
                    if (inputText.length < 30) {
                        inputText.append(apostrophe)
                        search()
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_COMMA -> {
                    if (inputText.length < 30) {
                        inputText.append(comma)
                        search()
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DEL -> {
                    if (inputText.isNotEmpty()) {
                        inputText.deleteCharAt(inputText.lastIndex)
                        search()
                    }
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    val index =
                        mKeyAdapter.indexOf(binding.rvActivityChannelSearchKey.focusedChild.tag)
                    if (index in mKeyAdapter.size() - 5 until mKeyAdapter.size()) {
                        when (index % 5) {
                            0 -> binding.ivActivityChannelSearchDelete.requestFocus()
                            1 -> binding.tvActivityChannelSearch0.requestFocus()
                            2 -> binding.tvActivityChannelSearchDot.requestFocus()
                            3 -> binding.tvActivityChannelSearchSpace.requestFocus()
                            else -> binding.ivActivityChannelSearchClear.requestFocus()
                        }

                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mKeyAdapter.indexOf(binding.rvActivityChannelSearchKey.focusedChild.tag) < 5) {
                        binding.tvActivityChannelSearchBack.requestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    if (mKeyAdapter.indexOf(binding.rvActivityChannelSearchKey.focusedChild.tag) % 5 == 0) {
                        return@setOnKeyInterceptListener true
                    }
                }
            }
            return@setOnKeyInterceptListener false
        }

        binding.ivActivityChannelSearchDelete.setOnKeyListener { v, keyCode, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                in KeyEvent.KEYCODE_A..KeyEvent.KEYCODE_Z -> {
                    if (inputText.length < 30) {
                        inputText.append(letterList[event.keyCode - 29])
                        search()
                    }
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    if (inputText.length < 30) {
                        inputText.append((event.keyCode - 7).toString())
                        search()
                    }
                }

                KeyEvent.KEYCODE_DEL -> {
                    if (inputText.isNotEmpty()) {
                        inputText.deleteCharAt(inputText.lastIndex)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SPACE -> {
                    if (inputText.length < 30) {
                        inputText.append(" ")
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_PERIOD -> {
                    if (inputText.length < 30) {
                        inputText.append(period)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SLASH -> {
                    if (inputText.length < 30) {
                        inputText.append(slash)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_MINUS -> {
                    if (inputText.length < 30) {
                        inputText.append(minus)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_APOSTROPHE -> {
                    if (inputText.length < 30) {
                        inputText.append(apostrophe)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_COMMA -> {
                    if (inputText.length < 30) {
                        inputText.append(comma)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_DOWN, KeyEvent.KEYCODE_DPAD_LEFT -> return@setOnKeyListener true
                KeyEvent.KEYCODE_DPAD_UP -> {
                    binding.rvActivityChannelSearchKey[binding.rvActivityChannelSearchKey.childCount - 5].requestFocus()
                    return@setOnKeyListener true
                }
            }
            false
        }

        binding.ivActivityChannelSearchClear.setOnKeyListener { v, keyCode, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                in KeyEvent.KEYCODE_A..KeyEvent.KEYCODE_Z -> {
                    if (inputText.length < 30) {
                        inputText.append(letterList[event.keyCode - 29])
                        search()
                    }
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    if (inputText.length < 30) {
                        inputText.append((event.keyCode - 7).toString())
                        search()
                    }
                }

                KeyEvent.KEYCODE_DEL -> {
                    if (inputText.isNotEmpty()) {
                        inputText.deleteCharAt(inputText.lastIndex)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SPACE -> {
                    if (inputText.length < 30) {
                        inputText.append(" ")
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_PERIOD -> {
                    if (inputText.length < 30) {
                        inputText.append(period)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SLASH -> {
                    if (inputText.length < 30) {
                        inputText.append(slash)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_MINUS -> {
                    if (inputText.length < 30) {
                        inputText.append(minus)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_APOSTROPHE -> {
                    if (inputText.length < 30) {
                        inputText.append(apostrophe)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_COMMA -> {
                    if (inputText.length < 30) {
                        inputText.append(comma)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> return@setOnKeyListener true
                KeyEvent.KEYCODE_DPAD_RIGHT -> if (mMovieAdapter.size() == 0) return@setOnKeyListener true
                KeyEvent.KEYCODE_DPAD_UP -> {
                    binding.rvActivityChannelSearchKey[binding.rvActivityChannelSearchKey.childCount - 1].requestFocus()
                    return@setOnKeyListener true
                }
            }
            false
        }

        binding.tvActivityChannelSearch0.setOnKeyListener { _, keyCode, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                in KeyEvent.KEYCODE_A..KeyEvent.KEYCODE_Z -> {
                    if (inputText.length < 30) {
                        inputText.append(letterList[event.keyCode - 29])
                        search()
                    }
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    if (inputText.length < 30) {
                        inputText.append((event.keyCode - 7).toString())
                        search()
                    }
                }

                KeyEvent.KEYCODE_DEL -> {
                    if (inputText.isNotEmpty()) {
                        inputText.deleteCharAt(inputText.lastIndex)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SPACE -> {
                    if (inputText.length < 30) {
                        inputText.append(" ")
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_PERIOD -> {
                    if (inputText.length < 30) {
                        inputText.append(period)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SLASH -> {
                    if (inputText.length < 30) {
                        inputText.append(slash)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_MINUS -> {
                    if (inputText.length < 30) {
                        inputText.append(minus)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_APOSTROPHE -> {
                    if (inputText.length < 30) {
                        inputText.append(apostrophe)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_COMMA -> {
                    if (inputText.length < 30) {
                        inputText.append(comma)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> return@setOnKeyListener true
                KeyEvent.KEYCODE_DPAD_UP -> {
                    binding.rvActivityChannelSearchKey[binding.rvActivityChannelSearchKey.childCount - 4].requestFocus()
                    return@setOnKeyListener true
                }
            }
            false
        }

        binding.tvActivityChannelSearchDot.setOnKeyListener { _, keyCode, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                in KeyEvent.KEYCODE_A..KeyEvent.KEYCODE_Z -> {
                    if (inputText.length < 30) {
                        inputText.append(letterList[event.keyCode - 29])
                        search()
                    }
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    if (inputText.length < 30) {
                        inputText.append((event.keyCode - 7).toString())
                        search()
                    }
                }

                KeyEvent.KEYCODE_DEL -> {
                    if (inputText.isNotEmpty()) {
                        inputText.deleteCharAt(inputText.lastIndex)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SPACE -> {
                    if (inputText.length < 30) {
                        inputText.append(" ")
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_PERIOD -> {
                    if (inputText.length < 30) {
                        inputText.append(period)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SLASH -> {
                    if (inputText.length < 30) {
                        inputText.append(slash)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_MINUS -> {
                    if (inputText.length < 30) {
                        inputText.append(minus)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_APOSTROPHE -> {
                    if (inputText.length < 30) {
                        inputText.append(apostrophe)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_COMMA -> {
                    if (inputText.length < 30) {
                        inputText.append(comma)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> return@setOnKeyListener true
                KeyEvent.KEYCODE_DPAD_UP -> {
                    binding.rvActivityChannelSearchKey[binding.rvActivityChannelSearchKey.childCount - 3].requestFocus()
                    return@setOnKeyListener true
                }
            }
            return@setOnKeyListener false
        }

        binding.tvActivityChannelSearchSpace.setOnKeyListener { _, keyCode, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                in KeyEvent.KEYCODE_A..KeyEvent.KEYCODE_Z -> {
                    if (inputText.length < 30) {
                        inputText.append(letterList[event.keyCode - 29])
                        search()
                    }
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    if (inputText.length < 30) {
                        inputText.append((event.keyCode - 7).toString())
                        search()
                    }
                }

                KeyEvent.KEYCODE_DEL -> {
                    if (inputText.isNotEmpty()) {
                        inputText.deleteCharAt(inputText.lastIndex)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SPACE -> {
                    if (inputText.length < 30) {
                        inputText.append(" ")
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_PERIOD -> {
                    if (inputText.length < 30) {
                        inputText.append(period)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SLASH -> {
                    if (inputText.length < 30) {
                        inputText.append(slash)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_MINUS -> {
                    if (inputText.length < 30) {
                        inputText.append(minus)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_APOSTROPHE -> {
                    if (inputText.length < 30) {
                        inputText.append(apostrophe)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_COMMA -> {
                    if (inputText.length < 30) {
                        inputText.append(comma)
                        search()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> return@setOnKeyListener true
                KeyEvent.KEYCODE_DPAD_UP -> {
                    binding.rvActivityChannelSearchKey[binding.rvActivityChannelSearchKey.childCount - 2].requestFocus()
                    return@setOnKeyListener true
                }
            }
            return@setOnKeyListener false
        }

        binding.tvActivityChannelSearch0.setOnClickListener {
            if (inputText.length < 30) {
                inputText.append("'")
                search()
            }
        }

        binding.tvActivityChannelSearchDot.setOnClickListener {
            if (inputText.length < 30) {
                inputText.append(".")
                search()
            }
        }

        binding.tvActivityChannelSearchSpace.setOnClickListener {
            if (inputText.length < 30) {
                inputText.append(" ")
                search()
            }
        }

        binding.ivActivityChannelSearchDelete.setOnClickListener {
            if (inputText.isNotEmpty()) {
                inputText.deleteCharAt(inputText.lastIndex)
                search()
            }
        }

        binding.ivActivityChannelSearchClear.setOnClickListener {
            inputText.delete(0, inputText.length)
            search()
        }

        binding.rvActivityChannelSearchResult.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mMovieAdapter.indexOf(binding.rvActivityChannelSearchResult.focusedChild.tag) == 0) {
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    if (mMovieAdapter.indexOf(binding.rvActivityChannelSearchResult.focusedChild.tag) % 2 == 0) {
                        binding.rvActivityChannelSearchKey.requestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }
            }

            return@setOnKeyInterceptListener false
        }
    }

    @SuppressLint("SetTextI18n")
    private fun search() {
        binding.tvActivityChannelSearchInput.text = inputText
        binding.tvActivityChannelSearchCount.text = "${inputText.length}/30"
        mChannelSearchViewModel.getChannelListByKeyword(inputText.toString())
    }

}