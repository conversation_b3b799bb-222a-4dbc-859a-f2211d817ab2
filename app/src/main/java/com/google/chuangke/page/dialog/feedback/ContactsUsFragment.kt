package com.google.chuangke.page.dialog.feedback

import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.page.dialog.FeedbackDismissEvent
import com.google.chuangke.page.dialog.FeedbackItemFocusEvent
import com.google.chuangke.page.dialog.FeedbackTabEvent
import com.google.chuangke.util.SPUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class ContactsUsFragment : BaseFragment() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var tvTip: TextView
    private lateinit var faqAdapter: ContactsUsAdapter

    override fun layoutId(): Int {
        return R.layout.fragment_feedback_contact_us
    }

    override fun initView(view: View) {
        recyclerView = view.findViewById(R.id.rv_fragment_feedback_contact)
        tvTip = view.findViewById(R.id.tv_fragment_feedback_contact_tip2)

        val contactStr = SPUtils.getString(requireContext(), Constants.SP_KEY_CONTACT_US, null)
        val jsonArray = JSONArray.parseArray(contactStr)
        val list = ArrayList<JSONObject>()
        for (item in jsonArray) {
            list.add(item as JSONObject)
        }

        val layoutManager = LinearLayoutManager(requireActivity())
        layoutManager.orientation = LinearLayoutManager.HORIZONTAL
        recyclerView.layoutManager = layoutManager
        recyclerView.adapter = ContactsUsAdapter(recyclerView).also { faqAdapter = it }
        faqAdapter.setList(list)

    }

    override fun initListener() {

    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFeedbackItemForceEvent(event: FeedbackItemFocusEvent) {
        if (event.index == 2) {
            recyclerView.getChildAt(0).requestFocus()
        }
    }
}

class ContactsUsAdapter(recyclerView: RecyclerView) :
    BaseQuickAdapter<JSONObject, BaseViewHolder>(R.layout.item_fragment_feedback_contact) {
    var rv: RecyclerView = recyclerView

    override fun convert(holder: BaseViewHolder, item: JSONObject) {
        val apiUrl = Config.getInstance().getApiUrl("qrcodeimage")
        Glide.with(context)
            .load("${apiUrl}?key=${item.getString("qrcode")}")
            .error(R.mipmap.dif_ic_logo_default)
            .diskCacheStrategy(DiskCacheStrategy.ALL)//缓存所有台标
            .into(holder.getView(R.id.iv_item_fragment_feedback_contact))

        holder.setText(R.id.tv_item_fragment_feedback_contact_title, item.getString("type"))
        holder.setText(R.id.tv_item_fragment_feedback_contact_content, item.getString("content"))

        holder.itemView.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(view: View, i: Int, keyEvent: KeyEvent): Boolean {
                if (keyEvent.action == KeyEvent.ACTION_UP) {
                    return true
                }
                if(i == KeyEvent.KEYCODE_BACK){
                    EventBus.getDefault().post(FeedbackDismissEvent())
                    return true
                }

                if (i == KeyEvent.KEYCODE_DPAD_RIGHT
                    || i == KeyEvent.KEYCODE_DPAD_DOWN && holder.layoutPosition == data.size - 1
                    || i == KeyEvent.KEYCODE_DPAD_UP && holder.layoutPosition == 0
                ) {
                    return true
                }
                if (i == KeyEvent.KEYCODE_DPAD_LEFT) {
                    EventBus.getDefault().post(FeedbackTabEvent(1))
                    return true
                }
                return false
            }
        })
    }

}