package com.google.chuangke.page.adapter

import android.view.KeyEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.util.DateUtil
import java.util.*

class DialogEpgDayAdapter : BaseQuickAdapter<Date, BaseViewHolder>(R.layout.item_dialog_epg_date) {

    private var onItemKeyListener: RecyclerExtras.OnItemKeyListener? = null
    var onItemFocusChangeListener: RecyclerExtras.OnItemFocusChangeListener? = null
    var selIndex: Int = 0

    fun setOnItemKeyListener(onItemKeyListener: RecyclerExtras.OnItemKeyListener?) {
        this.onItemKeyListener = onItemKeyListener
    }

    fun marketFocusV() {
        if (selIndex == 7) return // 最中间那天的颜色已经有了背景色，重叠了不好看
        val selMVHolder: BaseViewHolder? =
            recyclerView.findViewHolderForAdapterPosition(selIndex) as BaseViewHolder?
        selMVHolder?.let {
            selectHolder(it, true)
        }
    }

    fun resumeFocusV() {
        val selMVHolder: BaseViewHolder? =
            recyclerView.findViewHolderForAdapterPosition(selIndex) as BaseViewHolder?
        selMVHolder?.let {
            selectHolder(it, false)
        }
    }

    private fun selectHolder(it: BaseViewHolder, select: Boolean) {
        it.getView<TextView>(R.id.tv_item_dialog_epg_date_no).isSelected = select
        it.getView<TextView>(R.id.tv_item_dialog_epg_date_week).isSelected = select
    }

    override fun convert(holder: BaseViewHolder, item: Date) {
        holder.setText(R.id.tv_item_dialog_epg_date_no, DateUtil.getFormatString(item, "dd"))
        holder.setText(
            R.id.tv_item_dialog_epg_date_week,
            DateUtil.getFormatString(item, "E").uppercase()
        )

        val date = DateUtil.getFormatString(item, "yyyy-MM-dd")
        val current = DateUtil.getFormatString(Date(), "yyyy-MM-dd")
        holder.setVisible(R.id.iv_ll_item_dialog_epg_date, date == current)

        holder.getView<LinearLayout>(R.id.ll_item_dialog_epg_date)
            .setOnFocusChangeListener { v, hasFocus ->
                if (hasFocus) {
                    onItemFocusChangeListener?.onItemFocusChange(v, holder.layoutPosition, hasFocus)
                }
            }

        holder.getView<LinearLayout>(R.id.ll_item_dialog_epg_date)
            .setOnKeyListener(object : View.OnKeyListener {
                override fun onKey(view: View, i: Int, keyEvent: KeyEvent): Boolean {
                    onItemKeyListener?.onItemKey(view, holder.layoutPosition, keyEvent, i)

                    if (keyEvent.action == KeyEvent.ACTION_UP) {
                        return false
                    }

                    if (i == KeyEvent.KEYCODE_DPAD_DOWN
                    ) {
                        return true
                    }

                    return false
                }
            })
    }

}