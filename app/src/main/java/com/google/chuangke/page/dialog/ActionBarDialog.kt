package com.google.chuangke.page.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.*
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.chuangke.R
import com.google.chuangke.base.BaseSpeechDialog
import com.google.chuangke.common.Config
import com.google.chuangke.common.event.SourceChangedEvent
import com.google.chuangke.common.event.TracksChangedEvent
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.page.MainActivity
import com.google.chuangke.player.PlayerHelper
import com.google.chuangke.player.SourceHelper
import com.orhanobut.logger.Logger
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.text.SimpleDateFormat
import java.util.*

class ActionBarDialog(
    private val activity: Activity, themeId: Int
) : BaseSpeechDialog(activity, themeId) {

    private lateinit var mIvLogo: ImageView
    private lateinit var mIvReplay: ImageView
    private lateinit var mTvChannelNo: TextView
    private lateinit var mTvChannelName: TextView
    private lateinit var mTvStartTime: TextView
    private lateinit var mTvEndTime: TextView
    private lateinit var mTvCurrent: TextView
    private lateinit var mTvNext: TextView
    private lateinit var mTvResolution: TextView
    private lateinit var mTvFrameRate: TextView
    private lateinit var mTvFrameSource: TextView
    private lateinit var mSb: SeekBar
    private lateinit var mTc: TextClock
    private lateinit var mLlBuff: LinearLayout
    private lateinit var mLlReplay: LinearLayout
    private lateinit var mLlCurrent: LinearLayout
    private lateinit var mLlProcess: LinearLayout

    private var mChannelBean: ChannelBean? = null
    private var mEpgBean: EpgBean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_action_bar)
        initView()

        val window: Window? = this.window
        if (window != null) {
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
            )
            window.setDimAmount(0f)
        }
    }

    private fun initView() {
        mIvLogo = findViewById(R.id.iv_dialog_action_bar_logo)
        mIvReplay = findViewById(R.id.iv_dialog_action_bar_replay)
        mTvChannelNo = findViewById(R.id.tv_dialog_action_bar_channel_no)
        mTvChannelName = findViewById(R.id.tv_dialog_action_bar_channel_name)
        mTvStartTime = findViewById(R.id.tv_dialog_action_bar_start_time)
        mTvEndTime = findViewById(R.id.tv_dialog_action_bar_end_time)
        mTvCurrent = findViewById(R.id.tv_dialog_action_bar_current)
        mLlCurrent = findViewById(R.id.ll_dialog_action_bar_current)
        mLlProcess = findViewById(R.id.ll_dialog_action_bar_process)
        mTvNext = findViewById(R.id.tv_dialog_action_bar_next)
        mSb = findViewById(R.id.sb_dialog_action_bar_process)
        mLlBuff = findViewById(R.id.ll_dialog_action_bar_buff)
        mLlReplay = findViewById(R.id.ll_dialog_action_bar_replay)
        mTc = findViewById(R.id.tc_dialog_action_bar_clock)
        mTvResolution = findViewById(R.id.tv_fragment_live_info_current_resolution)
        mTvFrameRate = findViewById(R.id.tv_fragment_live_info_current_rate)
        mTvFrameSource = findViewById(R.id.tv_fragment_live_info_current_source)

        initData()
    }


    fun setChannel(channelBean: ChannelBean?) {
        mChannelBean = channelBean
        initData()
    }

    fun setEpg(epgBean: EpgBean) {
        mEpgBean = epgBean
        initData()
    }

    fun setProgress(max: Long, position: Long) {
        mSb.max = max.toInt()
        mSb.progress = position.toInt()

        val sdf = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        sdf.timeZone = TimeZone.getTimeZone("GMT+00:00")
        mTvEndTime.text = sdf.format(max)
        mTvStartTime.text = sdf.format(position)
    }

    @SuppressLint("SetTextI18n")
    private fun initData() {
        if (Config.getInstance().playType == 1) {
            initPlayback()
        } else {
            initLive()
        }
    }

    private fun initPlayback() {
        if (mEpgBean == null) {
            return
        }

        val channels = DBApi.getInstance().getChannelByEpg(mEpgBean!!)
        if (channels.isEmpty()) {
            return
        }
        mChannelBean = channels[0]

        Glide.with(activity).load( DBApi.getInstance().getChannelIcon(null, mEpgBean, null))
            .error(R.mipmap.dif_icon_default).diskCacheStrategy(DiskCacheStrategy.ALL)
            .into(mIvLogo)

        mTvChannelNo.text = mChannelBean!!.channelNumber.toString()
        mTvChannelName.text = mChannelBean!!.name

        if (Config.getInstance().playType == 0) {
            // 日期
            mLlBuff.visibility = View.VISIBLE
            mLlReplay.visibility = View.GONE
            mTc.visibility = View.VISIBLE
            mIvReplay.visibility = View.GONE
        } else {
            mLlReplay.visibility = View.VISIBLE
            mLlBuff.visibility = View.GONE
            mTc.visibility = View.GONE
            mIvReplay.visibility = View.VISIBLE
        }

        // 当前节目
        mTvCurrent.text = mEpgBean!!.name
        // 下一节目
        val nextEpg =
            DBApi.getInstance().getNextEpg(mChannelBean!!.channelId!!, mEpgBean!!.endTime!!)
        if (nextEpg.isNotEmpty()) {
            mTvNext.text = nextEpg[0].name
        }
    }

    private fun initLive() {
        if (mChannelBean == null) {
            return
        }

        Glide.with(activity).load(DBApi.getInstance().getChannelIcon(mChannelBean, null, null))
            .error(R.mipmap.dif_icon_default).diskCacheStrategy(DiskCacheStrategy.ALL)
            .into(mIvLogo)

        mTvChannelNo.text = mChannelBean!!.channelNumber.toString()
        mTvChannelName.text = mChannelBean!!.name

        if (Config.getInstance().playType != 1) {
            // 日期
            mLlBuff.visibility = View.VISIBLE
            mLlReplay.visibility = View.GONE
        } else {
            mLlReplay.visibility = View.VISIBLE
            mLlBuff.visibility = View.GONE
        }
        val curEpg = DBApi.getInstance().getCurrentEpgList(mChannelBean!!, Date())

        if (curEpg.isNotEmpty()) {
            // 当前节目
            mTvCurrent.text = curEpg[0].name

            val startTime = curEpg[0].beginTime
            val endTime = curEpg[0].endTime
            val sdf = SimpleDateFormat("hh:mm a", Locale.getDefault())
            if (startTime != null) {
                mTvStartTime.text = sdf.format(startTime.toLong() * 1000)
            }
            if (endTime != null) {
                mTvEndTime.text = sdf.format(endTime.toLong() * 1000)
            }

            //进度
            if (startTime != null && endTime != null) {
                mSb.max = endTime - startTime
                val currentTime = Date().time / 1000
                mSb.progress = (currentTime - startTime).toInt()
            }

            // 下一节目
            val nextEpg = curEpg[0].endTime?.let {
                DBApi.getInstance().getNextEpg(mChannelBean!!.channelId!!, it)
            }
            if (!nextEpg.isNullOrEmpty()) {
                mTvNext.text = nextEpg[0].name
            }
        } else {
            mTvCurrent.text = "--"
            mTvStartTime.text = context.getString(R.string.action_bar_null_time)
            mTvEndTime.text = context.getString(R.string.action_bar_null_time)
            mSb.progress = 0
            mTvNext.text = "--"
        }

        updateVideoInfoDisplay(
            PlayerHelper.getInstance().currentWidth,
            PlayerHelper.getInstance().currentHeight,
            PlayerHelper.getInstance().currentFrameRate
        )
        updateSource()
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (Config.getInstance().playType == 1 && (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_LEFT)) {
            ActionBarDialogHelper.getInstance().reCount()
            seekEvent(keyCode, event)
            return true
        }
        return (activity as MainActivity).onKeyDown(keyCode, event)
    }

    override fun onKeyUp(keyCode: Int, event: KeyEvent): Boolean {
        if (Config.getInstance().playType == 1 && (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_LEFT)) {
            seekEvent(keyCode, event)
            return true
        }
        return (activity as MainActivity).onKeyDown(keyCode, event)
    }

    private var offset = 0.001f //移动速度基数
    var position = 0L
    private var seeking = false
    private fun seekEvent(keyCode: Int, keyEvent: KeyEvent) {
        val duration = PlayerHelper.getInstance().duration
        if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && keyEvent.action == KeyEvent.ACTION_DOWN) {
            if (!seeking) {
                position = PlayerHelper.getInstance().currentPosition
            }

            position += kotlin.math.max((offset * duration).toInt(), 5000)
            if (position >= duration) {
                position = duration - 10000
            }
            ActionBarDialogHelper.getInstance().setProgress(duration, position)
            offset += 0.001.toFloat()
            seeking = true
        } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && keyEvent.action == KeyEvent.ACTION_DOWN) {
            if (!seeking) {
                position = PlayerHelper.getInstance().currentPosition
            }

            position -= kotlin.math.max((duration * offset).toInt(), 5000)
            if (position <= 0) {
                position = 10
            }
            ActionBarDialogHelper.getInstance().setProgress(duration, position)
            offset += 0.001.toFloat()
            seeking = true
        } else if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT && keyEvent.action == KeyEvent.ACTION_UP || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT && keyEvent.action == KeyEvent.ACTION_UP) {
            if (!seeking && keyCode == KeyEvent.KEYCODE_DPAD_RIGHT) {
                position = PlayerHelper.getInstance().currentPosition + 5000
            }
            if (!seeking && keyCode == KeyEvent.KEYCODE_DPAD_LEFT) {
                position = PlayerHelper.getInstance().currentPosition - 5000
            }
            ActionBarDialogHelper.getInstance().setProgress(duration, position)
            offset = 0.001f
            seeking = false
            PlayerHelper.getInstance().seekTo(position)
        }
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

    override fun onStop() {
        EventBus.getDefault().unregister(this)
        super.onStop()
    }


    fun dismissDialog() {
        dismiss()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onTracksChangedEvent(event: TracksChangedEvent) {
        updateVideoInfoDisplay(
            PlayerHelper.getInstance().currentWidth,
            PlayerHelper.getInstance().currentHeight,
            PlayerHelper.getInstance().currentFrameRate
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSourceChangedEvent(event: SourceChangedEvent) {
        updateSource()
    }

    private fun updateSource(){
        try {
            if (SourceHelper.INSTANCE.sourceArray.isNullOrEmpty()){
                mTvFrameSource.visibility = View.GONE
                return
            }

            var source = SourceHelper.INSTANCE.sourceArray.getString(SourceHelper.INSTANCE.currentIndex)
            val arr = source.split("_")
            source = "${arr[1]}.${arr[0].reversed()}.${arr[2]}"
//            source = source.replace("_", ".").reversed()
            mTvFrameSource.visibility =
                if (source.isNotEmpty()) View.VISIBLE else View.GONE
            mTvFrameSource.text = source
        }catch (e: Exception){
            Logger.e(e.message.toString())
        }
    }

    private fun updateVideoInfoDisplay(width: Int, height: Int, frameRate: Float) {

        mTvResolution.visibility =
            if (width > 0) View.VISIBLE else View.GONE
        mTvFrameRate.visibility =
            if (frameRate > 0) View.VISIBLE else View.GONE

        // 更新UI的代码，例如使用TextView显示分辨率和帧率
        mTvResolution.text = if (width > 0) "$width x $height" else ""

        mTvFrameRate.text = if (frameRate > 0) "$frameRate FPS" else ""
    }
}


class ActionBarDialogHelper {
    private var dialog: ActionBarDialog? = null

    companion object {
        fun getInstance() = InstanceHelper.instance
    }

    object InstanceHelper {
        val instance = ActionBarDialogHelper()
    }

    fun show(activity: Activity, channelBean: ChannelBean?) {
        if (dialog != null && dialog!!.isShowing) {
            dialog!!.setChannel(channelBean)
            execute { dismiss() }
            return
        }
        dialog = ActionBarDialog(activity, R.style.Dialog)
        dialog!!.setOwnerActivity(activity)
        dialog!!.show()
        dialog!!.setChannel(channelBean)

        execute { dismiss() }
    }

    fun show(activity: Activity, epgBean: EpgBean) {
        if (dialog != null && dialog!!.isShowing) {
            dialog!!.setEpg(epgBean)
            execute { dismiss() }
            executePeriod {
                activity.runOnUiThread {
                    setProgress(
                        PlayerHelper.getInstance().duration,
                        PlayerHelper.getInstance().currentPosition
                    )
                }
            }
            return
        }
        dialog = ActionBarDialog(activity, R.style.Dialog)
        dialog!!.setOwnerActivity(activity)
        dialog!!.show()
        dialog!!.setEpg(epgBean)
        dialog!!.setProgress(
            PlayerHelper.getInstance().duration, PlayerHelper.getInstance().currentPosition
        )

        execute { dismiss() }
        executePeriod {
            activity.runOnUiThread {
                setProgress(
                    PlayerHelper.getInstance().duration, PlayerHelper.getInstance().currentPosition
                )
            }
        }
    }

    fun show(activity: Activity) {
        show(activity, Config.getInstance().currentPlayChannel)
    }

    fun setProgress(max: Long, position: Long) {
        dialog?.setProgress(max, position)
    }

    fun dismiss() {
        dialog?.let {
            if (it.isShowing) it.dismissDialog()
        }
        timerTaskPeriod?.cancel()
    }

    fun dismiss(activity: MainActivity) {
        dialog?.let {
            if (it.isShowing) {
                it.dismissDialog()
                activity.catchFocus()
            }
        }
        timerTaskPeriod?.cancel()
    }

    fun reCount() {
        execute { dismiss() }
    }

    private var timer = Timer()
    private var timerTask: TimerTask? = null
    private var timerTaskPeriod: TimerTask? = null
    private fun execute(runnable: () -> Unit) {
        timerTask?.cancel()
        timerTask = object : TimerTask() {
            override fun run() {
                runnable.invoke()
            }
        }
        timer.schedule(timerTask, 5000)
    }

    private fun executePeriod(runnable: () -> Unit) {
        timerTaskPeriod?.cancel()
        timerTaskPeriod = object : TimerTask() {
            override fun run() {
                runnable.invoke()
            }
        }
        timer.schedule(timerTaskPeriod, 0, 1000)
    }
}
