package com.google.chuangke.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

@Entity
class TagBean(
    @Id(assignable = true)
    var id: Long,
    var name: String? = null,
    var intOrder: Int? = null,
    var passwordAccess: Int? = null,
    var naLiteral: String? = null,
    var isSpecial: Int? = null,
    var custom: Int? = 0
) : Serializable {

    override fun equals(other: Any?): <PERSON><PERSON>an {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as TagBean

        if (id != other.id) return false
        if (intOrder != other.intOrder) return false
        if (passwordAccess != other.passwordAccess) return false
        if (isSpecial != other.isSpecial) return false
        if (custom != other.custom) return false
        if (name != other.name) return false
        if (naLiteral != other.naLiteral) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + (intOrder ?: 0)
        result = 31 * result + (passwordAccess ?: 0)
        result = 31 * result + (isSpecial ?: 0)
        result = 31 * result + (custom ?: 0)
        result = 31 * result + (name?.hashCode() ?: 0)
        result = 31 * result + (naLiteral?.hashCode() ?: 0)
        return result
    }

}