package com.google.chuangke.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import io.objectbox.annotation.Transient
import java.io.Serializable

const val CAN_EDIT = 2
const val CAN_NOT_EDIT = 1

@Entity
class CustomTagBean: Serializable {

    @Id(assignable = true)
    var id: Long? = null

    var name: String? = null

    // 是否可编辑 1否，2可
    var edit: Int? = 0

    var createTime: Int? = null

    @Transient
    var isSelect = false

}