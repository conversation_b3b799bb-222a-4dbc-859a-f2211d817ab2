package com.google.chuangke.util;

import android.content.Context;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import kotlin.Pair;

public class ZipCodeCache {
    public static Map<String, Pair<String, String>> loadData(Context context) throws IOException {
        Map<String, Pair<String, String>> zipToDmaMap = new HashMap<>();
        InputStream is = context.getAssets().open("dma.txt");
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));

        // 跳过表头
        reader.readLine();

        String line;
        while ((line = reader.readLine()) != null) {
            String[] parts = line.split("\t");
            if (parts.length >= 3) {
                zipToDmaMap.put(parts[0], new Pair<>(parts[1], parts[2])); // zip_code -> dma_description
            }
        }
        reader.close();

        return zipToDmaMap;
    }
}
