package com.google.chuangke.common;


import android.text.TextUtils;

import com.google.chuangke.MyApplication;
import com.google.chuangke.database.DBApi;
import com.google.chuangke.entity.ChannelBean;
import com.google.chuangke.entity.EpgBean;
import com.google.chuangke.entity.TagBean;
import com.google.chuangke.util.SPUtils;

public class Config {
    private static Config instance;
    public static Platform platform = Platform.USA;

    private String[] sportChannel;  //体育频道NFL、MLB、NBA、NHL、PPV
    private String baseUrl;
    private String deviceAuthSN; //设备鉴权sn
    private TagBean currentTag; //当前播放的频道的标签
    private ChannelBean currentPlayChannel; //当前播放的频道
    private EpgBean currentPlayEpg; //当前播放的epg
    private boolean unLock; //是否已解锁，true已解锁，false未解锁
    private int playType; //播放类型，0直播，1回播

    private boolean isFavoriteClick = false;//是否是收藏标签点击

    private boolean auth = true;

    private String resourceVersion;

    private boolean isRequestPermission = false;

    private int dmaViewFlag = 0;

    private Config() {
    }

    public boolean isFavoriteClick() {
        return isFavoriteClick;
    }

    public void setFavoriteClick(boolean favoriteClick) {
        isFavoriteClick = favoriteClick;
    }

    public boolean isRequestPermission() {
        return isRequestPermission;
    }

    public void setRequestPermission(boolean requestPermission) {
        isRequestPermission = requestPermission;
    }

    public int getDmaViewFlag() {
        return dmaViewFlag;
    }

    public void setDmaViewFlag(int dmaViewFlag) {
        this.dmaViewFlag = dmaViewFlag;
    }

    // 添加平台请追加在最后，以前的顺序不要打乱
    public enum Platform {
        USA, NEWTV, TEST, USA_PRE, PI, ROCK, ZSHARE
    }

    public static Config getInstance() {
        if (instance == null) {
            synchronized (Config.class) {
                if (instance == null) {
                    instance = new Config();
                }
            }
        }
        return instance;
    }

    public String getApiUrl(String action) {
        if (TextUtils.isEmpty(baseUrl)) {
            return null;
        }
//        baseUrl = "http://*************:8080/myiptv/";
        return baseUrl + "api/" + action + ".htm";
    }


    public String getStaticUrl() {
        if (TextUtils.isEmpty(baseUrl)) {
            return null;
        }
        String host = baseUrl.substring(baseUrl.indexOf("."));
        return "http://static" + host;
    }

    public String[] getSportChannel() {

        if (sportChannel == null) {
            synchronized (this) {
                sportChannel = DBApi.Companion.getInstance().getAllSpecialTag().toArray(new String[0]);
            }
        }

        return sportChannel;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        String host = baseUrl.substring(baseUrl.indexOf("."));

        this.baseUrl = baseUrl;
    }

    public String getDeviceAuthSN() {
        return deviceAuthSN;
    }

    public void setDeviceAuthSN(String deviceAuthSN) {
        this.deviceAuthSN = deviceAuthSN;
    }

    public ChannelBean getCurrentPlayChannel() {
        if (currentPlayChannel == null) {
            currentPlayChannel = (ChannelBean) SPUtils.Companion.getObject(MyApplication.context, Constants.SP_LAST_TIME_CHANNEL);
        }
        return currentPlayChannel;
    }

    public void setCurrentPlayChannel(ChannelBean currentPlayChannel) {
        this.currentPlayChannel = currentPlayChannel;
        if (currentTag.getPasswordAccess() == null || currentTag.getPasswordAccess() == 1) {
            return;
        }
        SPUtils.Companion.putObject(MyApplication.context, Constants.SP_LAST_TIME_CHANNEL, currentPlayChannel);
    }

    public boolean isUnLock() {
        return unLock;
    }

    public void setUnLock(boolean unLock) {
        this.unLock = unLock;
    }

    public int getPlayType() {
        return playType;
    }

    public void setPlayType(int playType) {
        this.playType = playType;
    }

    public EpgBean getCurrentPlayEpg() {
        return currentPlayEpg;
    }

    public void setCurrentPlayEpg(EpgBean currentPlayEpg) {
        this.currentPlayEpg = currentPlayEpg;
    }

    public TagBean getCurrentTag() {
        if (currentTag == null) {
            currentTag = (TagBean) SPUtils.Companion.getObject(MyApplication.context, Constants.SP_LAST_TIME_TAG);
        }
        return currentTag;
    }

    // 保存标签
    public void setCurrentTag(TagBean currentTag) {
        this.currentTag = currentTag;
        if (currentTag.getPasswordAccess() == null || currentTag.getPasswordAccess() == 1) {
            return;
        }
        SPUtils.Companion.putObject(MyApplication.context, Constants.SP_LAST_TIME_TAG, currentTag);
    }

    public boolean isAuth() {
        return auth;
    }

    public void setAuth(boolean auth) {
        this.auth = auth;
    }

    public String getResourceVersion() {
        return resourceVersion;
    }

    public void setResourceVersion(String resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    public void release() {
        instance = null;
    }

}
