package com.google.chuangke.common;

import android.os.SystemClock;

public class EventFilter {
    /**
     * 在一个时间内发生多少次返回true
     * eg：1秒内点击了两次
     */
    public static int TYPE_IN_TIME = 0;
    /**
     * 距离上次事件的时间超过了返回true
     * eg：500毫秒内发生的重复事件不处理
     */
    public static int TYPE_OUT_TIME = 1;
    private final long[] eventArr;
    private final long time;
    private final int type;

    private EventFilter(Builder builder) {
        this.time = builder.time;
        this.type = builder.type;
        if(type == TYPE_OUT_TIME){
            this.eventArr = new long[2];
        }else{
            this.eventArr = new long[builder.numOfTime];
        }
    }

    public static class Builder {
        private int type;
        private int numOfTime;
        private int time;

        public Builder setType(int type) {
            this.type = type;
            return this;
        }

        public Builder setNumOfTime(int numOfTime) {
            this.numOfTime = numOfTime;
            return this;
        }

        public Builder setTime(int time) {
            this.time = time;
            return this;
        }

        public EventFilter build() {
            return new EventFilter(this);
        }
    }

    public boolean filter() {
        switch (type) {
            case 0:
                return inTime();
            case 1:
                return outTime();
            default:
        }
        return true;
    }

    private boolean inTime() {
        System.arraycopy(eventArr, 1, eventArr, 0, eventArr.length - 1);
        eventArr[eventArr.length - 1] = SystemClock.uptimeMillis();
        return eventArr[eventArr.length - 1] - eventArr[0] < time;
    }

    private boolean outTime() {
        System.arraycopy(eventArr, 1, eventArr, 0, eventArr.length - 1);
        eventArr[eventArr.length - 1] = SystemClock.uptimeMillis();
        return eventArr[eventArr.length - 1] - eventArr[eventArr.length - 2] > time;
    }


}
