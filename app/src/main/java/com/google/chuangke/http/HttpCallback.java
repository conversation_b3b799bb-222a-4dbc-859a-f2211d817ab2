package com.google.chuangke.http;

import androidx.annotation.NonNull;

import com.alibaba.fastjson.JSONObject;
import com.google.chuangke.MyApplication;
import com.orhanobut.logger.Logger;
import com.wochuang.json.NativeLib;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;

public class HttpCallback implements Callback {

    public void onSuccess(JSONObject jsonObject) {

    }

    public void onError(String err){

    }

    @Override
    public void onFailure(@NonNull Call call, @NonNull IOException e) {
        onError("API Exception");
    }

    @Override
    public void onResponse(@NonNull Call call, @NonNull Response response) {
        JSONObject jsonObject;
        try{
            String result = response.body().string();
            response.close();
            String responseData = NativeLib.getResponseData(MyApplication.context, result);
            jsonObject = JSONObject.parseObject(responseData);
        }catch (Exception e){
            Logger.e("api接口返回异常:"+call.request().url());
            onError("API Exception");
            return;
        }
        if(jsonObject == null){
            Logger.e("api接口返回异常:"+call.request().url());
            onError("API Exception");
            return;
        }

        onSuccess(jsonObject);
    }


}