package com.google.chuangke.http;

import androidx.annotation.NonNull;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;

import gojson.gojson.Gojson;
import okhttp3.Dns;

public class HttpDoh implements Dns {

    @NonNull
    @Override
    public List<InetAddress> lookup(@NonNull String hostname) throws UnknownHostException {
        try {
            String ip = Gojson.getDohAnswer(hostname);
            List<InetAddress> result = new ArrayList<>();
            result.add(InetAddress.getByName(ip));
            return result;
        } catch (Exception err) {
//            err.printStackTrace();
        }

        //当有异常发生时，使用默认解析
        return Dns.SYSTEM.lookup(hostname);
    }
}