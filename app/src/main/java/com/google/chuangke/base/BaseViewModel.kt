package com.google.chuangke.base

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.chuangke.MyApplication
import com.google.chuangke.ext.toast
import com.orhanobut.logger.Logger
import kotlinx.coroutines.launch

/**
 *  BaseViewModel
 */
open class BaseViewModel : ViewModel() {

    /**
     *  展示Toast
     */
    fun showToast(msg: String) {
        MyApplication.context.toast(msg)
    }

    /**
     * 统一处理异常
     */
    fun launch(block: suspend () -> Unit) = viewModelScope.launch {
        try {
            block()
        } catch (e: Throwable) {
//            showToast(e.message.toString())
//            e.printStackTrace()
            Logger.e(e.message.toString())
        }
    }

    /**
     * 统一处理异常
     */
    fun launch(block: suspend () -> Unit, error: (() -> Unit)? = null) = viewModelScope.launch {
        try {
            block()
        } catch (e: Throwable) {
            error?.invoke()
//            showToast(e.message.toString())
            Logger.e(e.message.toString())
        }
    }
}