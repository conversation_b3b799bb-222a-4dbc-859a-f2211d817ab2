package com.google.chuangke.data

import android.text.TextUtils
import androidx.core.text.isDigitsOnly
import androidx.lifecycle.MutableLiveData
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.common.Config
import com.google.chuangke.common.EventFilter
import com.google.chuangke.common.UnitCallback
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.page.MainActivity
import com.google.chuangke.player.LivePlayHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn

class MainViewModel(private val mChannelRepository: ChannelRepository) : BaseViewModel() {

    // 搜索匹配到的频道
    val searchChannelList: MutableLiveData<MutableList<ChannelBean>> = MutableLiveData()

    // 需要解锁的播放
    val unlockLiveData: MutableLiveData<UnitCallback> = MutableLiveData()

    // 当前播放的分类下的频道
    private val currentChannelList: MutableList<ChannelBean> = mutableListOf()

    // 当前频道的序号
    private var currentChannelIndex = 0

    // 防误触过滤器
    private var eventFilter =
        EventFilter.Builder().setType(EventFilter.TYPE_OUT_TIME).setTime(500).build()

    /**
     * 自定义收藏标签
     */
    private fun getCustomTag(): Flow<List<TagBean>> = flow {
        DBApi.getInstance().getCustomTags().let {
            val customTags = it.map { customBean ->
                TagBean(
                    customBean.id!!, customBean.name, 0, -1, custom = customBean.edit
                )
            }
            emit(customTags)
        }
    }.flowOn(Dispatchers.IO)

    fun initData() {
        launch {
            val oldTag = Config.getInstance().currentTag
            val allTags = mChannelRepository.getTagList().toMutableList()
            allTags.add(
                TagBean(
                    TAG_ALL, MyApplication.context.getString(R.string.txt_tag_all), 0, -1
                )
            )
            // 自定义收藏
            getCustomTag().collect {
                allTags.addAll(it)
            }
            // 如果没有找到本地存放的数据，从数据库按顺序去拿出第一项存在频道的列表
            if (allTags.isNotEmpty()) {
                if (oldTag == null) {
                    // 本地未存放
                    for (i in allTags.indices) {
                        val channelList = mChannelRepository.getChannelList(allTags[i])
                        if (channelList.isNotEmpty()) {
                            // 取有频道数据的标签
                            Config.getInstance().currentTag = allTags[i]
                            Config.getInstance().currentPlayChannel = channelList[0]
                            currentChannelList.addAll(channelList)
                            break
                        }
                    }
                } else {
                    // 存在，则核对目前的数据库里是否有它
                    var tagPosition = -1
                    for (i in allTags.indices) {
                        if (allTags[i].id == oldTag.id) {
                            tagPosition = i
                            break
                        }
                    }

                    if (tagPosition == -1) {
                        // 没有，取有频道数据的标签
                        for (i in allTags.indices) {
                            val channelList = mChannelRepository.getChannelList(allTags[i])
                            if (channelList.isNotEmpty()) {
                                Config.getInstance().currentTag = allTags[i]
                                Config.getInstance().currentPlayChannel = channelList[0]
                                currentChannelList.addAll(channelList)
                                break
                            }
                        }
                    } else {
                        // 有，进一步核对存放的频道
                        val oldChannel = Config.getInstance().currentPlayChannel
                        val channelList = mChannelRepository.getChannelList(oldTag)

                        var channelPosition = -1
                        if (channelList.isNotEmpty() && oldChannel != null) {
                            // 标签下的列表不为空的时候进行对比
                            for (j in channelList.indices) {
                                if (channelList[j].id == oldChannel.id) {
                                    channelPosition = j
                                    break
                                }
                            }

                            if (channelPosition == -1) {
                                // 不存在，就去频道列表的第一项
                                Config.getInstance().currentPlayChannel = channelList[0]
                            } else {
                                currentChannelIndex = channelPosition
                            }
                            currentChannelList.addAll(channelList)
                        } else {
                            // 标签下的列表为空，取有频道数据的标签
                            for (i in allTags.indices) {
                                val channelList2 = mChannelRepository.getChannelList(allTags[i])
                                if (channelList2.isNotEmpty()) {
                                    Config.getInstance().currentTag = allTags[i]
                                    Config.getInstance().currentPlayChannel = channelList2[0]
                                    currentChannelList.addAll(channelList2)
                                    break
                                }
                            }
                        }
                    }
                }

                // 播放
                LivePlayHelper.getInstance().channelPlay(Config.getInstance().currentPlayChannel!!)
            }
        }
    }

    /**
     * 上按换台
     */
    fun upChangeChannel() {
        currentChannelList.let {
            if (it.isNotEmpty()) {
                if (currentChannelIndex > 0) {
                    if (DBApi.getInstance().isChannelLocked(it[currentChannelIndex - 1].id!!)) {
                        unlockLiveData.postValue {
                            currentChannelIndex--
                            // 播放
                            LivePlayHelper.getInstance().channelPlay(it[currentChannelIndex])
                        }
                    } else {
                        currentChannelIndex--
                        // 播放
                        LivePlayHelper.getInstance().channelPlay(it[currentChannelIndex])
                    }
                } else {
                    if (eventFilter.filter()) showToast(MyApplication.context.getString(R.string.no_more_channel))
                }
            }
        }
    }

    /**
     * 下按换台
     */
    fun downChangeChannel() {
        currentChannelList.let {
            if (it.isNotEmpty()) {
                if (currentChannelIndex < it.size - 1) {
                    if (DBApi.getInstance().isChannelLocked(it[currentChannelIndex + 1].id!!)) {
                        unlockLiveData.postValue {
                            currentChannelIndex++

                            // 播放
                            LivePlayHelper.getInstance().channelPlay(it[currentChannelIndex])
                        }
                    } else {
                        currentChannelIndex++

                        // 播放
                        LivePlayHelper.getInstance().channelPlay(it[currentChannelIndex])
                    }
                } else {
                    if (eventFilter.filter()) showToast(MyApplication.context.getString(R.string.no_more_channel))
                }
            }
        }
    }

    /**
     * 数字键换台
     */
    fun changeChannelByNumber(channelNumber: Int) {
        launch {
            mChannelRepository.getChannelByChannelNumber(channelNumber).also {
                if (it.isNotEmpty()) {
                    searchPlay(it[0])
                } else {
                    showToast(MyApplication.context.getString(R.string.channel_not_exist))
                }
            }
        }
    }

    /**
     * 搜索频道进入播放
     */
    fun searchPlay(channelBean: ChannelBean, isWatchHistory: Boolean = false) {
        if (DBApi.getInstance().isChannelLocked(channelBean.id!!)) {
            unlockLiveData.postValue {
                // 播放
                LivePlayHelper.getInstance().channelPlay(channelBean, isWatchHistory)
                // 改变当前的配置
                changeCurrentConfig(channelBean)
            }
        } else {
            // 播放
            LivePlayHelper.getInstance().channelPlay(channelBean, isWatchHistory)
            // 改变当前的配置
            changeCurrentConfig(channelBean)
        }
    }

    fun play(channelBean: ChannelBean, isWatchHistory: Boolean) {
        // 播放
        LivePlayHelper.getInstance().channelPlay(channelBean, isWatchHistory)
    }

    /**
     * 灌入当前的频道
     */
    private fun changeCurrentConfig(channelBean: ChannelBean) {

        Config.getInstance().currentPlayChannel = channelBean

        // 取出当前频道的频道列表
        launch {
            // 取出标签反推
            val tagObject = JSONObject.parseObject(channelBean.tags)
            val iterator: Iterator<String> = tagObject.keys.iterator()
            if (iterator.hasNext()) {
                val tagId = iterator.next().toLong()
                mChannelRepository.getTagList().let { tagList ->
                    // 找标签
                    for (i in tagList.indices) {
                        if (tagId == tagList[i].id) {
                            Config.getInstance().currentTag = tagList[i]
                            break
                        }
                    }

                    // 取当前标签下的列表
                    mChannelRepository.getChannelList(Config.getInstance().currentTag).let { channelBeans ->
                        // 替换当前的频道列表
                        currentChannelList.clear()
                        currentChannelList.addAll(channelBeans)

                        // 查询channel的index
                        for (i in channelBeans.indices) {
                            if (channelBeans[i].id == channelBean.id) {
                                currentChannelIndex = i
                                break
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * channel换台
     */
    fun onChannelPlay(tagBean: TagBean, channelBean: ChannelBean) {
        Config.getInstance().currentTag = tagBean
        launch {
            if (tagBean.id != TAG_HISTORY && tagBean.id != TAG_ALL) {
                mChannelRepository.getChannelList(tagBean).let { channelBeans ->
                    // 替换当前的频道列表
                    currentChannelList.clear()
                    currentChannelList.addAll(channelBeans)

                    // 查询channel的index
                    for (i in channelBeans.indices) {
                        if (channelBeans[i].id == channelBean.id) {
                            currentChannelIndex = i
                            break
                        }
                    }
                }
                // 播放
                LivePlayHelper.getInstance().channelPlay(channelBean)
            } else {
                // ALL标签
                searchPlay(channelBean)
            }
        }
    }

    /**
     * 变更当前的tag和channel
     */
    fun onPlaybackPlay(
        tagBean: TagBean, channelBean: ChannelBean, epgBean: EpgBean, activity: MainActivity
    ) {
        // 变更正确的频道标签
        if (tagBean.id > 0) {
            Config.getInstance().currentTag = tagBean
            Config.getInstance().currentPlayChannel = channelBean
        } else {
            // ALL标签
            changeCurrentConfig(channelBean)
        }

    }

    /**
     * 语音搜索
     */
    fun searchKeywords(keywords: MutableList<String>) {
        launch {
            val channelList = mutableListOf<ChannelBean>()

            // 查台名
            keywords.forEach {
                channelList.addAll(mChannelRepository.getChannelLisByKeywords(it))
            }

            // 纯数字查台号
            keywords.forEach { word ->
                if (!TextUtils.isEmpty(word) && word.isDigitsOnly()) {
                    channelList.addAll(mChannelRepository.getChannelByChannelNumber(word.toInt()))
                }
            }

            val tempMap = mutableMapOf<Long, ChannelBean>()

            // 去重
            channelList.forEach {
                tempMap[it.id!!] = it
            }

            searchChannelList.postValue(tempMap.map { it.value }.toMutableList())
        }
    }

    /**
     * 上一个频道
     */
    fun watchHistoryPrevious() {
        launch {
            mChannelRepository.getHistoryChannelList().let { channels ->
                if (channels.isNotEmpty()) {
                    var tempIndex = -1
                    for (i in channels.indices) {
                        val tempChannelBean = channels[i]
                        if (Config.getInstance().currentPlayChannel.id == tempChannelBean.id) {
                            tempIndex = i
                            break
                        }
                    }

                    if (tempIndex != -1) {
                        tempIndex += 1
                    }

                    if (tempIndex == channels.size || tempIndex == -1) {
                        showToast(MyApplication.context.getString(R.string.no_more_channel))
                    } else {
                        searchPlay(channels[tempIndex], true)
                    }
                }
            }
        }

    }

    /**
     * 后面的频道
     */
    fun watchHistoryAfter() {
        launch {
            mChannelRepository.getHistoryChannelList().let { channels ->
                if (channels.isNotEmpty()) {
                    var tempIndex = -1
                    for (i in channels.indices) {
                        val tempChannelBean = channels[i]
                        if (Config.getInstance().currentPlayChannel.id == tempChannelBean.id) {
                            tempIndex = i
                            break
                        }
                    }

                    if (tempIndex != -1) {
                        tempIndex -= 1
                    }

                    if (tempIndex < 0) {
                        showToast(MyApplication.context.getString(R.string.no_more_channel))
                    } else {
                        searchPlay(channels[tempIndex], true)
                    }
                }
            }
        }
    }

}