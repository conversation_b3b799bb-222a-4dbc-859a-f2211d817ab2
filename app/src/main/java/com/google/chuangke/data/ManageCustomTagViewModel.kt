package com.google.chuangke.data

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.entity.CustomTagBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn

class ManageCustomTagViewModel(private val manageCustomTagRepository: ManageCustomTagRepository) :
    BaseViewModel() {

    val customTagLiveData: MutableLiveData<List<CustomTagBean>> = MutableLiveData()

    init {
        getCustomTags()
    }

    private fun getCustomTags() {
        launch {
            flow {
                val list = manageCustomTagRepository.getCustomTags()
                emit(list)
            }.flowOn(Dispatchers.IO)
                .collect {
                    customTagLiveData.postValue(it)
                }
        }
    }
}