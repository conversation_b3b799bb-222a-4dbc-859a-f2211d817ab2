package com.google.chuangke.data

import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.TagBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ClassicEpgRepository(private val mDBApi: DBApi) {

    suspend fun getChannelList(
        tagBean: TagBean
    ): MutableList<ChannelBean> {

        if ((tagBean.custom ?: 0) > 0) {
            return getChannelListByCustomTag(tagBean.id)
        }

        return withContext(Dispatchers.IO) {
            when (tagBean.id) {
                TAG_ALL -> {
                    return@withContext mDBApi.allChannel().toMutableList()
                }

                else -> {
                    return@withContext mDBApi.getChannelByTag(tagBean.id)
                        .toMutableList()
                }
            }
        }
    }

    private suspend fun getChannelListByCustomTag(customId: Long): MutableList<ChannelBean> {
        return withContext(Dispatchers.IO) {
            val tempList = mutableListOf<ChannelBean>()
            val channelCollectionBeans = mDBApi.customTagChannelsId(customId)
            val ids = LongArray(channelCollectionBeans.size)
            for (i in ids.indices) {
                ids[i] = channelCollectionBeans[i].unid ?: -1
            }
            tempList.addAll(mDBApi.getChannelByChannelIds(ids))
            tempList
        }
    }

    suspend fun getTagList(): List<TagBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.allTag
        }
    }
}