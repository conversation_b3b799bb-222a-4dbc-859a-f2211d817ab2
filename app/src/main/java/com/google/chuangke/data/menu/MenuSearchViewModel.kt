package com.google.chuangke.data.menu

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.entity.ChannelBean

class MenuSearchViewModel(
    private val mMenuSearchRepository: MenuSearchRepository
) :
    BaseViewModel() {

    val searchChannelLiveData: MutableLiveData<MutableList<ChannelBean>> = MutableLiveData()

    val keyListLiveData: MutableLiveData<MutableList<Char>> = MutableLiveData()

    init {
        val keyList = mutableListOf<Char>()

        for (i in 0..25) {
            keyList.add(Char(65 + i))
        }
        for (j in 1..9) {
            keyList.add(Char(48 + j))
        }

        keyListLiveData.value = keyList
    }

    /**
     * 搜索频道
     */
    fun getChannelListByKeyword(keyword: String) {
        launch {
            mMenuSearchRepository.getChannelListByKeyword(keyword).let {
                searchChannelLiveData.postValue(it.toMutableList())
            }
        }
    }
}