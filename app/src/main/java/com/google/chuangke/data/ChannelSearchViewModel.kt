package com.google.chuangke.data

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.data.menu.MenuSearchRepository
import com.google.chuangke.entity.ChannelBean

class ChannelSearchViewModel(
    private val mMenuSearchRepository: MenuSearchRepository
) :
    BaseViewModel() {

    val searchChannelLiveData: MutableLiveData<List<ChannelBean>> = MutableLiveData()

    /**
     * 搜索频道
     */
    fun getChannelListByKeyword(keyword: String) {
        launch {
            mMenuSearchRepository.getChannelListByKeyword(keyword).let {
                searchChannelLiveData.postValue(it)
            }
        }
    }
}