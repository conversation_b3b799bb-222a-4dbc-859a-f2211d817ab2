package com.google.chuangke.data

import androidx.lifecycle.MutableLiveData
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.common.Constants
import com.google.chuangke.util.SPUtils

class CenterViewModel : BaseViewModel() {

    val optionLiveData: MutableLiveData<MutableList<String>> = MutableLiveData()
    val infoLiveData: MutableLiveData<String> = MutableLiveData()

    init {
        val optionList: MutableList<String> = mutableListOf("MENU", "CHANNELS")
        optionLiveData.postValue(optionList)

        val notification = SPUtils.getString(MyApplication.context, Constants.SP_KEY_NOTIFICATION, null)
        if(notification != null){
            val jsonArray = JSONArray.parseArray(notification)
            val sb = StringBuffer()
            for (i in 0..10) {
                for (value in jsonArray) {
                    val jsonObject = value as JSONObject
                    val now = System.currentTimeMillis()
                    val startTime: Long? = jsonObject.getLong("startTime")
                    val endTime: Long? = jsonObject.getLong("endTime")
                    if (jsonObject["isImportant"] == true && startTime != null && endTime != null && startTime < now && endTime > now) {
                        sb.append(jsonObject["content"]).append("\t\t\t\t\t")
                    }
                }
            }
            infoLiveData.postValue(sb.toString())
        }

    }
}