<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="afterDescendants"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="365dp"
        android:layout_height="match_parent"
        android:background="@color/bg_content"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <TextView
            android:id="@+id/tv_fragment_menu_account_account_name"
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="110dp"
            android:focusable="false"
            android:text="@string/menu_account_free_trial"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv_fragment_menu_account_expiry_date_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:focusable="false"
            android:text="@string/menu_account_expiry_date"
            android:textColor="#7E7E7E"
            android:textSize="13sp" />

        <TextView
            android:id="@+id/tv_fragment_menu_account_expiry_date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:textColor="@color/white"
            android:textSize="26sp" />

        <TextView
            android:id="@+id/tv_fragment_menu_account_login"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="40dp"
            android:layout_marginTop="110dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/menu_account_login_now"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="12sp"
            tools:visibility="visible" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:focusable="false" />

        <TextView
            android:id="@+id/tv_fragment_menu_account_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="39dp"
            android:focusable="false"
            android:text="@string/menu_account_login_tips1"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:visibility="gone"
            tools:ignore="SmallSp"
            tools:visibility="visible" />
    </LinearLayout>

</LinearLayout>