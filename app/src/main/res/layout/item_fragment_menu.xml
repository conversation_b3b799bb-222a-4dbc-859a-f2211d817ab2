<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tv_fragment_menu_account"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:layout_marginTop="17.5dp"
    android:background="@drawable/selector_menu_btn"
    android:drawablePadding="8.5dp"
    android:focusable="true"
    android:gravity="center_vertical"
    android:paddingStart="25dp"
    android:paddingEnd="1dp"
    android:text="@string/menu_my_account"
    android:textColor="@color/selector_color_menu"
    android:textSize="14sp"
    tools:drawableStartCompat="@drawable/selector_menu_account" />
