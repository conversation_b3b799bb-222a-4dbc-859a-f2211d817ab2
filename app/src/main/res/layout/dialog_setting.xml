<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="800dp"
        android:layout_height="400dp"
        android:background="@drawable/shape_dialog_epg"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/shape_bg_gradient"
            android:gravity="center"
            android:text="@string/txt_setting"
            android:textColor="@color/white"
            android:textSize="18sp" />

        <androidx.leanback.widget.VerticalGridView
            android:id="@+id/rv_dialog_setting"
            android:layout_width="800dp"
            android:layout_height="match_parent" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>