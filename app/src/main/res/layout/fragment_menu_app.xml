<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="365dp"
        android:layout_height="match_parent"
        android:background="@color/bg_content">

        <TextView
            android:id="@+id/tv_fragment_menu_app_tips1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="26dp"
            android:layout_marginEnd="30dp"
            android:focusable="false"
            android:textColor="#CFCFCF"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_fragment_menu_app_tips2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="30dp"
            android:focusable="false"
            android:textColor="#CFCFCF"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_fragment_menu_app_tips1" />

        <ImageView
            android:id="@+id/iv_fragment_menu_app_qr_code"
            android:layout_width="125dp"
            android:layout_height="125dp"
            android:layout_gravity="center_horizontal"
            android:focusable="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_fragment_menu_app_refresh"
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="38dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/selector_menu_sub_btn"
            android:drawablePadding="5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="37.5dp"
            android:paddingEnd="37.5dp"
            android:text="@string/menu_app_refresh_or_code"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="11sp"
            android:visibility="gone"
            app:drawableStartCompat="@mipmap/ic_menu_app_refresh"
            app:layout_constraintBottom_toTopOf="@+id/tv_fragment_menu_app_tips3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv_fragment_menu_app_qr_code" />

        <TextView
            android:id="@+id/tv_fragment_menu_app_tips3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:layout_marginBottom="24dp"
            android:focusable="false"
            android:textColor="#CFCFCF"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@+id/tv_fragment_menu_app_tips4"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/tv_fragment_menu_app_tips4"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginEnd="30dp"
            android:layout_marginBottom="30dp"
            android:focusable="false"
            android:textColor="#CFCFCF"
            android:textSize="12sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>