<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <androidx.media3.ui.PlayerView
        android:id="@+id/spv_activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="false" />

    <TextView
        android:id="@+id/tv_activity_main_no"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="35dp"
        android:layout_marginEnd="68dp"
        android:focusable="false"
        android:textColor="#ffffff"
        android:textSize="52sp"
        tools:ignore="RelativeOverlap"
        tools:text="999"
        tools:textColor="#000000" />

    <View
        android:id="@+id/focus_catcher"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_activity_live_playback"
        style="@style/big"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:drawablePadding="20dp"
        android:focusable="false"
        tools:drawableRight="@drawable/ic_movie_info_forward"
        tools:text="1min" />

    <ImageView
        android:id="@+id/iv_activity_live_playback"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentEnd="true"
        android:layout_margin="10dp"
        android:src="@drawable/ic_replaying"
        android:visibility="gone"
        tools:visibility="visible" />

</RelativeLayout>