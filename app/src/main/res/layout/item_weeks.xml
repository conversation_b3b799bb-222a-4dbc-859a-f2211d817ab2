<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    tools:background="@color/black">

    <LinearLayout
        android:id="@+id/ll_item_week"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_item_white"
        android:duplicateParentState="true"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_week_day"
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:textColor="@color/selector_color_white_purple"
            android:textSize="14sp"
            tools:text="14:00-17:00" />

        <TextView
            android:id="@+id/tv_item_week_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:maxLines="1"
            android:textColor="@color/selector_color_white_purple"
            android:textSize="11sp"
            tools:text="THU" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_item_week_day"
        android:layout_width="0.5dp"
        android:layout_height="23dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:contentDescription="@null"
        android:src="@mipmap/ic_channel_detail_line"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>