<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:paddingStart="30dp"
    android:duplicateParentState="true"
    android:focusable="false"
    android:gravity="center_vertical"
    android:orientation="vertical"
    tools:background="@color/black">

    <TextView
        android:id="@+id/tv_item_epg_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:focusable="false"
        android:textColor="@color/white"
        android:textSize="12sp"
        tools:text="14:00-17:00" />

    <TextView
        android:id="@+id/tv_item_epg_subtitle"
        android:layout_width="wrap_content"
        android:ellipsize="end"
        android:layout_height="wrap_content"
        android:duplicateParentState="true"
        android:focusable="false"
        android:maxLines="2"
        android:textColor="@color/white"
        android:textSize="14sp"
        tools:text="i ne oite com vanilovanilovanilovanilovanilovanilovanilo" />
</LinearLayout>
