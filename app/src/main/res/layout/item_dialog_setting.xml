<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/tv_item_dialog_setting"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_feedback"
    android:focusable="true"
    android:gravity="center_vertical"
    android:paddingStart="25dp"
    android:paddingTop="16dp"
    android:paddingEnd="25dp"
    android:paddingBottom="16dp"
    android:textColor="@color/white_ccc"
    android:textSize="13sp"
    tools:drawableEnd="@mipmap/ic_switch_open" />