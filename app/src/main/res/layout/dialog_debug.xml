<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal">

    <View
        android:id="@+id/view"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0"
        android:background="@android:color/transparent" />

    <TextView
        android:id="@+id/tv_dialog_debug_fixed"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:background="@color/black_80per"
        android:gravity="bottom"
        android:textSize="12sp"
        android:padding="16dp"
        android:scrollbars="vertical"
        android:textColor="@color/white_eee" />

    <TextView
        android:id="@+id/tv_dialog_debug_flow"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="3"
        android:background="#c6000000"
        android:gravity="bottom"
        android:textSize="8sp"
        android:padding="16dp"
        android:scrollbars="vertical"
        android:textColor="@color/white_eee" />

</LinearLayout>