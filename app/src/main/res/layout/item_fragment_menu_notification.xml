<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_notification_item"
    android:focusable="true"
    android:orientation="vertical"
    android:paddingStart="30dp"
    android:paddingEnd="30dp">

    <TextView
        android:id="@+id/tv_item_fragment_menu_notification_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:focusable="false"
        android:textColor="@color/selector_color_main"
        android:textSize="13sp" />

    <TextView
        android:id="@+id/tv_item_fragment_menu_notification_date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:focusable="false"
        android:gravity="end"
        android:textColor="@color/gray_999"
        android:textSize="11sp"
        tools:text="2022-06-23" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="15dp"
        android:background="#4CFFFFFF"
        android:focusable="false" />
</LinearLayout>