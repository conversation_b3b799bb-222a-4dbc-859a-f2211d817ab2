<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="afterDescendants"
    tools:ignore="SmallSp">

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_menu_option"
        android:layout_width="190dp"
        android:layout_height="0dp"
        android:background="@drawable/shape_bg_gradient"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:layout_width="190dp"
        android:layout_height="0dp"
        android:gravity="bottom"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_fragment_menu_ip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:textColor="@color/white_ccc"
            android:textSize="10sp"
            tools:text="IP: 127.0.0.1" />

        <TextView
            android:id="@+id/tv_fragment_menu_mac"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:layout_marginTop="2dp"
            android:textColor="@color/white_ccc"
            android:textSize="10sp"
            tools:text="01010101010101" />

        <TextView
            android:id="@+id/tv_fragment_menu_sn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:layout_marginTop="2dp"
            android:textColor="@color/white_ccc"
            android:textSize="10sp"
            tools:text="01010101010101" />

        <View
            android:layout_width="match_parent"
            android:layout_height="25dp" />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/fl_fragment_menu_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/bg_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_menu_option"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>