<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_channel_root"
    android:layout_width="150dp"
    android:layout_height="wrap_content"
    android:layout_marginStart="29dp"
    android:layout_marginBottom="7dp"
    android:background="@drawable/bg_item_white_15dp"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center_vertical"
    android:orientation="vertical"
    tools:background="@color/black">

    <TextView
        android:id="@+id/tv_item_channel_no"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:duplicateParentState="true"
        android:focusable="false"
        android:gravity="center"
        android:singleLine="true"
        android:textColor="@color/selector_color_white_purple"
        android:textSize="12sp"
        tools:text="8888" />

    <TextView
        android:id="@+id/tv_item_channel_title"
        style="@style/font_bold"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:duplicateParentState="true"
        android:ellipsize="marquee"
        android:focusable="false"
        android:marqueeRepeatLimit="marquee_forever"
        android:maxLines="1"
        android:singleLine="true"
        android:textColor="@color/selector_color_white_purple"
        android:textSize="15sp"
        tools:text="BABY TV" />

</LinearLayout>