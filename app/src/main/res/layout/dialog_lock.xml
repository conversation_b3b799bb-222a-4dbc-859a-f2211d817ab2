<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog"
        android:orientation="vertical"
        android:padding="36dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:focusable="false"
            android:text="Parental Password"
            android:textColor="@color/white_eee"
            android:textSize="19sp" />

        <TextView
            android:id="@+id/et_dialog_lock_password"
            style="@style/font_bold"
            android:layout_width="320dp"
            android:layout_height="45dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:background="@drawable/selector_input2"
            android:focusable="false"
            android:gravity="center_vertical"
            android:hint="Enter a 4-digit password"
            android:maxLength="4"
            android:paddingStart="26.5dp"
            android:paddingEnd="26.5dp"
            android:singleLine="true"
            android:textColor="@color/selector_color_hint"
            android:textColorHint="@color/selector_color_hint"
            android:textSize="14sp" />

        <androidx.leanback.widget.VerticalGridView
            android:id="@+id/vg_key"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            android:layout_marginTop="20dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>