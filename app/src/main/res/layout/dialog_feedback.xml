<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/shape_dialog_epg"
        android:orientation="vertical">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tl_dialog_feedback"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:background="@drawable/shape_bg_gradient"
            app:tabIndicatorFullWidth="false"
            app:tabInlineLabel="false"
            app:tabMaxWidth="0dp"
            app:tabMode="fixed"
            app:tabGravity="fill"
            app:tabSelectedTextColor="@color/purpler_7B0B82"
            app:tabTextAppearance="@style/TabLayoutTextStyle"
            app:tabIndicatorColor="@color/purpler_7B0B82"
            app:tabIndicator="@drawable/shape_tab_indicator"
            app:tabTextColor="@color/white_ccc" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vp_dialog_feedback"
            android:layout_width="800dp"
            android:layout_height="400dp"
            android:orientation="horizontal" />

    </LinearLayout>

</LinearLayout>
