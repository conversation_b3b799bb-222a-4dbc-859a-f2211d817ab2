<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_bg_gradient"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_activity_login_back"
        android:layout_width="wrap_content"
        android:layout_height="15.5dp"
        android:layout_marginStart="28dp"
        android:layout_marginTop="22dp"
        android:contentDescription="@null"
        android:focusable="true"
        android:src="@mipmap/ic_back" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="57dp"
        android:focusable="false"
        style="@style/font_bold"
        android:text="@string/login_title"
        android:textColor="@color/white_eee"
        android:textSize="19sp"/>

    <EditText
        android:id="@+id/et_activity_login_account_number"
        android:layout_width="420dp"
        android:layout_height="45dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="70dp"
        android:background="@drawable/selector_input"
        android:focusable="true"
        android:singleLine="true"
        style="@style/font_bold"
        android:gravity="center_vertical"
        android:hint="@string/login_enter_code"
        android:paddingStart="26.5dp"
        android:paddingEnd="26.5dp"
        android:textColor="@color/selector_color_hint"
        android:textColorHint="@color/selector_color_hint"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_activity_login_failed"
        android:layout_width="420dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="5dp"
        android:drawablePadding="4dp"
        android:focusable="false"
        android:text="@string/login_failed"
        android:textColor="#ED2121"
        android:textSize="14sp"
        android:visibility="invisible"
        app:drawableStartCompat="@mipmap/ic_menu_login_failed"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_activity_login_login"
        android:layout_width="420dp"
        android:layout_height="45dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="10.5dp"
        android:background="@drawable/selector_menu_sub_btn"
        android:focusable="true"
        style="@style/font_bold"
        android:gravity="center"
        android:text="@string/login_login"
        android:textColor="@color/selector_color_menu_sub_btn"
        android:textSize="14sp" />

    <TextView
        android:id="@+id/tv_activity_login_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="35dp"
        android:drawablePadding="6.5dp"
        android:focusable="false"
        android:text="@string/login_tips"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:drawableStartCompat="@mipmap/icon_menu_tip_selected" />
</LinearLayout>