<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_dialog_action_bar_setting"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="34dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="34dp"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="25dp"
        android:descendantFocusability="afterDescendants"
        android:focusableInTouchMode="true"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_epg"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/action_bar_setting_epg"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@mipmap/ic_action_bar_infor" />

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_vocal_tract"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/action_bar_setting_vocal_tract"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@mipmap/ic_action_bar_vocal_tract" />

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_captions"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/action_bar_setting_captions"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@mipmap/ic_action_bar_setting" />

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_resolution_ratio"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/action_bar_setting_resolution_ratio"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@mipmap/ic_action_bar_resolution_ratio" />

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_feedback"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/action_bar_setting_feedback"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@mipmap/ic_action_bar_feedback" />

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_setting"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/txt_setting"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@mipmap/ic_action_bar_setting" />

        <TextView
            android:id="@+id/tv_dialog_action_bar_setting_multiple"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginStart="10dp"
            android:background="@drawable/selector_action_bar_btn"
            android:drawablePadding="2.5dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingEnd="10dp"
            android:text="@string/txt_multiple_screen"
            android:textColor="@color/white"
            android:textSize="11sp"
            app:drawableStartCompat="@drawable/ic_action_bar_grid" />
    </LinearLayout>
</LinearLayout>