<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_classic_epg_root"
    android:layout_width="match_parent"
    android:layout_height="44dp"
    android:descendantFocusability="afterDescendants"
    android:orientation="horizontal"
    tools:background="@color/black">

    <LinearLayout
        android:id="@+id/ll_item_classic_epg_channel"
        android:layout_width="125dp"
        android:layout_height="44dp"
        android:background="@drawable/programguide_channel_item_background"
        tools:background="#000000">

        <TextView
            android:id="@+id/tv_item_classic_epg_no"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="4dp"
            android:duplicateParentState="true"
            android:fontFamily="@font/open_sans_regular"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="8888" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="2dp"
            android:layout_weight="1"
            android:duplicateParentState="true"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_item_classic_epg_logo"
                android:layout_width="wrap_content"
                android:layout_height="28dp"
                android:contentDescription="@null"
                tools:src="@mipmap/dif_ic_logo_default" />

            <TextView
                android:id="@+id/tv_item_classic_epg_channel_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="3.5dp"
                android:duplicateParentState="true"
                android:ellipsize="marquee"
                android:fontFamily="@font/open_sans_regular"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="8sp"
                tools:ignore="SmallSp"
                tools:text="shshhshs" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="12dp"
            android:layout_height="match_parent"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/iv_item_classic_epg_replay"
                android:layout_width="11dp"
                android:layout_height="11dp"
                android:layout_gravity="center_vertical"
                android:layout_marginTop="2dp"
                android:contentDescription="@null"
                android:src="@mipmap/ic_channel_replay" />

            <Space
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_item_classic_epg_lock"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="center_vertical"
                android:layout_marginBottom="2dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_channel_lock" />

            <Space
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <ImageView
                android:id="@+id/iv_item_classic_epg_favorite"
                android:layout_width="12dp"
                android:layout_height="12dp"
                android:layout_gravity="center_vertical"
                android:contentDescription="@null"
                android:src="@mipmap/ic_favorite_love_unselect" />
        </LinearLayout>
    </LinearLayout>

    <com.google.chuangke.view.RowView
        android:id="@+id/rowview_item_classic_epg"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:descendantFocusability="afterDescendants" />

</LinearLayout>