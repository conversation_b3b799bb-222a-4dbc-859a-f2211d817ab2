<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="640dp"
        android:layout_height="240dp"
        android:background="@drawable/shape_round_primary2"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="20dp"
            android:text="DMA LOCATION SETTING"
            android:textColor="@color/white"
            android:textSize="20sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="20dp"
            android:text="Current Location: New York"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="48dp"
            android:layout_marginEnd="24dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tv_disable"
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:focusable="true"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:text="DISABLE DMA"
                android:textColor="@color/selector_color_menu_sub_btn"
                android:textSize="14sp" />

            <Space
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <TextView
                android:id="@+id/tv_refresh"
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:focusable="true"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:text="REFRESH LOCATION"
                android:textColor="@color/selector_color_menu_sub_btn"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_zipcode"
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginStart="12dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:focusable="true"
                android:gravity="center"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:text="ENTER ZIP CODE"
                android:textColor="@color/selector_color_menu_sub_btn"
                android:textSize="14sp" />
        </LinearLayout>

    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>