<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="365dp"
        android:layout_height="match_parent"
        android:background="@color/bg_content"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="55dp"
            android:orientation="horizontal">

            <TextView
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:text="@string/menu_info_version"
                android:textColor="@color/white"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tv_fragment_menu_info_version"
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:textColor="@color/white"
                android:textSize="16sp"
                tools:text="3.0.0" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_fragment_menu_info_update"
            style="@style/font_bold"
            android:layout_width="190dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="60dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/menu_info_check_update"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_fragment_menu_info_clear_history"
            style="@style/font_bold"
            android:layout_width="190dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/menu_info_clear_history"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_fragment_menu_info_feedback"
            style="@style/font_bold"
            android:layout_width="190dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dp"
            android:focusable="true"
            android:gravity="end"
            android:paddingStart="20dp"
            android:paddingEnd="15dp"
            android:text="@string/menu_info_feedback"
            android:textColor="@color/selector_color_menu_feedback"
            android:textSize="12sp" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_fragment_menu_info_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="25.5dp"
            android:layout_marginEnd="25.5dp"
            android:focusable="false"
            android:paddingBottom="30dp" />
    </LinearLayout>
</LinearLayout>