<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="afterDescendants">

    <View
        android:layout_width="375dp"
        android:layout_height="0dp"
        android:background="@color/bg_content"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_fragment_menu_favorite_count"
        android:layout_width="375dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="17.5dp"
        android:focusable="false"
        android:gravity="end"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_fragment_menu_favorite_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="1" />

        <TextView
            android:id="@+id/tv_fragment_menu_favorite_all_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="25dp"
            android:focusable="false"
            android:textColor="#A99AAD"
            android:textSize="12sp"
            tools:text="/365" />
    </LinearLayout>

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_menu_favorite"
        android:layout_width="375dp"
        android:layout_height="0dp"
        android:layout_marginTop="15dp"
        app:layout_constraintBottom_toTopOf="@+id/line_fragment_menu_favorite_result"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_fragment_menu_favorite_count" />

    <View
        android:id="@+id/line_fragment_menu_favorite_result"
        android:layout_width="345dp"
        android:layout_height="1dp"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_fragment_menu_favorite" />

</androidx.constraintlayout.widget.ConstraintLayout>