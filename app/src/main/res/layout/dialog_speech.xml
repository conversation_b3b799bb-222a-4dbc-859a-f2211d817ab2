<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#30000000"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/iv_speech_circle"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginTop="30dp"
        android:src="@drawable/shape_purple_per30"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_speech_icon"
        android:layout_width="wrap_content"
        android:layout_height="30dp"
        android:layout_marginTop="30dp"
        android:focusable="false"
        android:src="@drawable/ic_record_on"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_dialog_speech"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/speech_dialog_hint"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_speech_icon" />

    <ImageView
        android:layout_marginTop="30dp"
        android:id="@+id/iv_speech_left"
        android:layout_width="16dp"
        android:layout_height="20dp"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="14dp"
        android:src="@drawable/ic_arrow_left_vs"
        android:visibility="invisible"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_dialog_speech"
        tools:visibility="visible" />

    <androidx.leanback.widget.HorizontalGridView
        android:id="@+id/vg_speech_search"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="10dp"
        app:layout_constraintBottom_toTopOf="@+id/tv_voice_content"
        app:layout_constraintEnd_toStartOf="@+id/iv_speech_right"
        app:layout_constraintStart_toEndOf="@id/iv_speech_left"
        app:layout_constraintTop_toBottomOf="@+id/tv_dialog_speech" />

    <ImageView
        android:layout_marginTop="30dp"
        android:id="@+id/iv_speech_right"
        android:layout_width="16dp"
        android:layout_height="20dp"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="10dp"
        android:src="@drawable/ic_arrow_right_vs"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/vg_speech_search"
        app:layout_constraintTop_toBottomOf="@+id/tv_dialog_speech"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_dialog_result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:focusable="false"
        android:text="..."
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tv_voice_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="50dp"
        android:layout_marginBottom="10dp"
        android:focusable="false"
        android:text="VOICE CONTENT"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@+id/tv_dialog_result"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>