<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="16dp"
    android:background="@android:color/transparent"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <ImageView
        android:id="@+id/iv_item_fragment_feedback_contact"
        android:layout_width="96dp"
        android:layout_height="96dp"
        android:layout_marginTop="4dp"
        android:scaleType="centerCrop"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_item_fragment_feedback_contact_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:textColor="@color/white_ccc"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_item_fragment_feedback_contact" />

    <TextView
        android:id="@+id/tv_item_fragment_feedback_contact_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:paddingBottom="10dp"
        android:textColor="@color/white"
        android:textSize="13sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_item_fragment_feedback_contact_title" />
</androidx.constraintlayout.widget.ConstraintLayout>
