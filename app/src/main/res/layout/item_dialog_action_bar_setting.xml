<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="35dp"
    android:layout_marginEnd="10dp"
    android:background="@drawable/selector_action_bar_btn"
    android:focusable="true"
    android:gravity="center"
    android:orientation="horizontal"
    android:paddingStart="22dp"
    android:paddingEnd="22dp">

    <TextView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="5dp"
        android:background="@color/white"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tv_item_dialog_epg_setting"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:textSize="11sp"
        tools:text="EN" />
</LinearLayout>