<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="40dp"
    android:layout_height="64dp"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:focusable="true"
    android:orientation="vertical"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/iv_ll_item_dialog_epg_date"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:contentDescription="@null"
        android:focusable="false"
        android:src="@drawable/shape_today"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_item_dialog_epg_date"
        android:layout_width="40dp"
        android:layout_height="64dp"
        android:duplicateParentState="true"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_dialog_epg_date_no"
            style="@style/font_bold"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/selector_dialog_epg_date"
            android:duplicateParentState="true"
            android:focusable="false"
            android:gravity="center"
            android:textColor="@color/programguide_selector_item_dialog_epg_date_text"
            android:textSize="16sp"
            tools:text="28" />

        <TextView
            android:id="@+id/tv_item_dialog_epg_date_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:duplicateParentState="true"
            android:focusable="false"
            android:textColor="@color/selector_item_dialog_epg_date_subtext"
            android:textSize="11sp"
            tools:text="SUN" />
    </LinearLayout>
</FrameLayout>