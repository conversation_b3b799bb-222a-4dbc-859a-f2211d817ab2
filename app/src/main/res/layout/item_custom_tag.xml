<?xml version="1.0" encoding="utf-8"?>
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="35dp"
    android:drawablePadding="7dp"
    android:layout_marginTop="5dp"
    android:background="@drawable/bg_item_white"
    android:focusable="true"
    android:singleLine="true"
    android:ellipsize="marquee"
    android:marqueeRepeatLimit="marquee_forever"
    android:gravity="center_vertical"
    android:paddingStart="25dp"
    android:paddingEnd="25dp"
    android:textColor="@color/selector_color_white_purple"
    android:textSize="15sp"
    tools:background="@drawable/shape_test_selected"
    tools:text="ALL" />
