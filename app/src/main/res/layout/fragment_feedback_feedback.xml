<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tv_fragment_feedback_tip1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        android:focusable="false"
        android:text="@string/fragment_feedback_tip"
        android:textColor="@color/white_ccc"
        android:textSize="15sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_feedback"
        android:layout_width="480dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:descendantFocusability="afterDescendants"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_fragment_feedback_tip1" />

    <TextView
        android:id="@+id/tv_fragment_feedback_tip2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:paddingStart="64dp"
        android:paddingEnd="64dp"
        android:paddingBottom="18dp"
        android:text="@string/fragment_feedback_tip2"
        android:textColor="@color/white_ccc"
        android:textSize="11sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
