<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="5.28dp" />
            <solid android:color="@color/white_10per" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape>
            <corners android:radius="5.28dp" />
            <solid android:color="@color/white" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape>
            <corners android:radius="5.28dp" />
            <solid android:color="@color/white" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape>
            <corners android:radius="5.28dp" />
            <solid android:color="@color/white_10per" />
        </shape>
    </item>
    <item android:state_selected="false">
        <shape>
            <corners android:radius="5.28dp" />
            <solid android:color="@color/white_10per" />
        </shape>
    </item>
</selector>

