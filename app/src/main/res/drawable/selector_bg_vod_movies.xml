<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_activated="true">
        <shape>
            <solid android:color="@color/white" />
            <corners android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp" />
        </shape>
    </item>

    <item android:state_focused="true">
        <shape>
            <solid android:color="@color/white" />
            <corners android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp" />
        </shape>
    </item>

    <item>
        <shape>
            <solid android:color="@color/white_20per" />
            <corners android:bottomLeftRadius="4dp"
                android:bottomRightRadius="4dp" />
        </shape>
    </item>

</selector>