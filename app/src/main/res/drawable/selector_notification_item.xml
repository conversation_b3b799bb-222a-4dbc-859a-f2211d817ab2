<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/white_20per" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape>
            <solid android:color="@color/white_20per" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape>
            <solid android:color="@color/white_20per" />
        </shape>
    </item>
    <item android:drawable="@drawable/shape_transparent_unselect" android:state_focused="false" />
    <item android:drawable="@drawable/shape_transparent_unselect" android:state_selected="false" />
</selector>

