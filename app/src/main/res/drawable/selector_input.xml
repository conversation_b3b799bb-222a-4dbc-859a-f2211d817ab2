<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <solid android:color="@color/white_90per" />
            <corners android:radius="30dp" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape>
            <solid android:color="@color/white_90per" />
            <corners android:radius="30dp" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape>
            <solid android:color="@color/white_90per" />
            <corners android:radius="30dp" />
        </shape>
    </item>
    <item android:state_focused="false">
        <shape>
            <solid android:color="@color/white_40per" />
            <corners android:radius="30dp" />
        </shape>
    </item>
    <item android:state_selected="false">
        <shape>
            <solid android:color="@color/white_40per" />
            <corners android:radius="30dp" />
        </shape>
    </item>
</selector>