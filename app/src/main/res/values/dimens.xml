<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2020, Egeniq
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<resources>
    <dimen name="programguide_table_width_per_hour">300dp</dimen>
    <dimen name="programguide_program_row_height">44.5dp</dimen>
    <dimen name="programguide_program_row_height_with_empty_space">44.5dp</dimen>
    <dimen name="programguide_minimum_item_width_sticking_out_behind_channel_column">74dp</dimen>
    <dimen name="programguide_item_padding">6dp</dimen>
    <dimen name="programguide_item_spacing">0dp</dimen>
    <dimen name="programguide_gap_item_corner_radius">2dp</dimen>
    <dimen name="programguide_time_row_negative_margin">0dp</dimen>
    <dimen name="programguide_time_row_height">25dp</dimen>
    <dimen name="programguide_channel_column_width">122dp</dimen>
    <dimen name="programguide_page_left_padding">32dp</dimen>
    <dimen name="programguide_current_time_indicator_top_height">4dp</dimen>
    <dimen name="programguide_filter_spacing">8dp</dimen>
    <dimen name="programguide_page_top_margin_menu_visible">8dp</dimen>
    <dimen name="programguide_button_corner_radius">4dp</dimen>
    <dimen name="programguide_bottom_detail_height">146dp</dimen>
</resources>