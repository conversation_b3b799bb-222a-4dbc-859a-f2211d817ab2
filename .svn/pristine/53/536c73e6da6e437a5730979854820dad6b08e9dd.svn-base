package com.google.chuangke.player

import android.os.Handler
import android.os.Looper
import androidx.media3.common.MimeTypes
import androidx.media3.common.Player
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication.Companion.context
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.common.UserHelper
import com.google.chuangke.common.event.LiveChangeEvent
import com.google.chuangke.common.event.PlayerStatusEvent
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelHistoryBean
import com.google.chuangke.page.dialog.DebugDialogHelper
import com.google.chuangke.player.tvbus.LivePlaySingleton
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import com.wochuang.json.NativeLib
import gojson.gojson.Gojson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.util.Timer
import java.util.TimerTask

class LivePlayHelper {

    companion object {
        fun getInstance() = InstanceHelper.instance
    }

    object InstanceHelper {
        val instance = LivePlayHelper()
    }

    // 用于相同频道需要播放时的控制
    var initPlay = true
    var channelBean: ChannelBean? = null
    var playbackTime = 0L

    fun channelPlay(
        channelBean: ChannelBean,
        isWatchHistory: Boolean = false,
        playback: Boolean = false,
        playbackTime: Long = 0L,
        retry: Boolean = false
    ) {

        // 当前正常频道播放(非回拨)情况，同一频道不再次播放
        if (!initPlay && Config.getInstance().playType == 0 && channelBean.id == Config.getInstance().currentPlayChannel.id) {
            return
        }
        initPlay = false

        Config.getInstance().playType = 0

        // 只要调用了就显示信息
        PlayerHelper.getInstance().showChannelInfoDialog(channelBean)
        PlayerHelper.getInstance().showLoadingDialog()
        PlayerHelper.getInstance().clearContent()
        PlayerHelper.getInstance().stopPlay()

        if (!playback)
            EventBus.getDefault().post(LiveChangeEvent())

        execute { // 真实触发的换台
            this.channelBean = channelBean
            this.playbackTime = playbackTime
            getSource(channelBean, isWatchHistory)
        }
    }

    private fun getSource(
        channelBean: ChannelBean,
        isWatchHistory: Boolean
    ) {
        //记录当前播放类型
        Config.getInstance().playType = 0
        //添加操作日志
        UserHelper.getInstance().addOperationLog(channelBean)
        //记录当前播放频道
        Config.getInstance().currentPlayChannel = channelBean
        val token = SPUtils.getString(context, Constants.SP_KEY_TOKEN, null)

        //非加密频道添加到播放历史
        if (!channelBean.tags!!.contains(Constants.FILTER_CONDITION)) {
            val channelHistoryBean = ChannelHistoryBean()
            channelHistoryBean.unid = channelBean.id
            channelHistoryBean.createTime = (System.currentTimeMillis() / 1000).toInt()
            if (!isWatchHistory) {
                // 非查看历史换台，才存入
                DBApi.getInstance().saveChannelHistory(channelHistoryBean)
            }
        }

        CoroutineScope(Dispatchers.IO).launch {
            val channelId = channelBean.id.toString()
            val data = Gojson.getSource(channelId, token)
            Logger.e(data)

            val jsonObject = JSONObject.parseObject(data)
            var code = jsonObject.getIntValue("code")
            if (code != 1) {
                EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.getSourceCode(code)))
                return@launch
            }

            val jsonArray = jsonObject.getJSONArray("reData")
            if (jsonArray.size == 0) {
                EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.ERROR_GET_SOURCE_NONE))
                return@launch
            }
            SourceHelper.INSTANCE.sourceArray = jsonArray
            SourceHelper.INSTANCE.retryCount = 0

            // 按顺序播放源，直到有可以播的为止
            var started = false
            for (i in jsonArray.indices) {
                started = startPlay(i)
                if (started){
                    break
                }
            }

            if (!started) {
                EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.ERROR_VALID_SOURCE_NONE))
            }
            PlayerHelper.getInstance().setChannelChange(true)
        }

    }

    private fun startPlay(i:Int) :Boolean{
        var index = i
        if (index == SourceHelper.INSTANCE.sourceArray.size){
            index = 0
        }
        SourceHelper.INSTANCE.currentIndex = index

        ticker()

        LivePlaySingleton.getInstance().stop()
        try {
            val jsonArray = SourceHelper.INSTANCE.sourceArray
            val platformName = jsonArray.getString(index)

            val dir = context.filesDir.path
            val platform = platformName.split("_")[0]
            val jsonResult = when (platform) {
                "1350" -> Gojson.start1350(dir, this.channelBean!!.channelId.toString(), playbackTime) // directv
                "1150" -> Gojson.start1150(this.channelBean!!.channelId.toString(), platformName) // fubo
                "0050" -> Gojson.start0050(this.channelBean!!.channelId.toString(), platformName)
                else -> ""
            }
            Logger.e(jsonResult)

            val json = JSONObject.parseObject(jsonResult) ?: return false
            val code = json.getIntValue("code")
            val url = json.getString("url")
            val provider = json.getString("provider")
            val mimeType = json.getString("mimeType")
            if (code != 1) {

                // 发送错误提示
                when (platform){
                    "1150" ->  EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.fuboCode(code)))
                    "1350" ->  EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.directvCode(code)))
                }
                return false
            }

            // tvbus
            if (platform == "0050") {
                val token = SPUtils.getString(context, Constants.SP_KEY_TOKEN, null)
                val tvbusUrl = NativeLib.getLiveUrlById(context, url, token)
                if (tvbusUrl.isNullOrEmpty() || tvbusUrl.length < 20) {
                    return false
                }
                Handler(Looper.getMainLooper()).post {
                    LivePlaySingleton.getInstance().start(tvbusUrl)
                }
                return true
            }

            val mimeTypes = when (mimeType) {
                "m3u8" -> MimeTypes.APPLICATION_M3U8
                "mpd" -> MimeTypes.APPLICATION_MPD
                "mp4" -> MimeTypes.APPLICATION_MP4
                else -> MimeTypes.APPLICATION_M3U8
            }

            DebugDialogHelper.getInstance().appendFixed(
                "${channelBean!!.channelId}, ${channelBean!!.channelNumber}, ${channelBean!!.name}, $platformName, $url ",
                true
            ) // 打印调试日志

            Handler(Looper.getMainLooper()).post {
                PlayerHelper.getInstance().startPlay(provider, url, null, mimeTypes, false)
                PlayerHelper.getInstance().setTrackText(PlayerHelper.TEXT_TRACK_NONE)
                PlayerHelper.getInstance().setTrackAudio("en")
            }

            return true
        } catch (e: Exception) {
            Logger.e(e.message.toString())
        }

        return false
    }

    private fun ticker(){
        SourceHelper.INSTANCE.schedule {
            Handler(Looper.getMainLooper()).post {

                // 每30秒检查一遍状态，如果没有播放则选择下一个源
                val playbackState = PlayerHelper.getInstance().playbackState
                if (playbackState == Player.STATE_IDLE || playbackState == Player.STATE_ENDED) {
                    startPlay(SourceHelper.INSTANCE.currentIndex + 1)
                }else{
                    SourceHelper.INSTANCE.retryCount = 0
                    ticker()
                }
            }
        }
    }

    private var timer = Timer()
    private var timerTask: TimerTask? = null
    private fun execute(runnable: () -> Unit) {
        timerTask?.cancel()
        timerTask = object : TimerTask() {
            override fun run() {
                runnable.invoke()
            }
        }
        timer.schedule(timerTask, 500)
    }

    fun release() {
        initPlay = true
        SourceHelper.INSTANCE.cancel()
    }

}
