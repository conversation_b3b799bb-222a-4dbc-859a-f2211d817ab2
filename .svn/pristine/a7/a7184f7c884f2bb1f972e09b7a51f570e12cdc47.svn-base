package com.google.chuangke.common.event

import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import kotlin.math.abs

class UserInfoEvent
class ContactInfoEvent
class AttentionInfoEvent
class NetworkChangeEvent
class PlayerStatusEvent(val code: Int) {

    // 1 正常
    // 1000+ 获取源错误
    // 2000+ tvbus
    // 3000+ directv
    // 4000+ fubo
    companion object {
        const val NORMAL: Int = 1 // 正常
        const val ERROR_AUTH_FAIL_GOLANG: Int = 1001 // 认证失败
        const val ERROR_GET_SOURCE_NONE: Int = 1002 // 获取不到源
        const val ERROR_VALID_SOURCE_NONE: Int = 1003 // 没有可以播放的源

        fun getSourceCode(nCode: Int): Int {
            return (nCode + 100) * 2 + 1000
        }

        fun tvBusCode(nCode: Int): Int {
            return (abs(nCode.toDouble()) * 3 + 7 + 2000).toInt()
        }

        fun directvCode(nCode: Int): Int {
            return (nCode + 100) * 2 + 3000
        }

        fun fuboCode(nCode: Int): Int {
            return (nCode + 100) * 2 + 4000
        }
    }
}

class FeedbackDialogEvent
class SettingEvent
class EpgDownLoadCompleteEvent
class VodExitEvent
class ChannelPlayEvent(val tagBean: TagBean, val channelBean: ChannelBean, val epgBean: EpgBean?)
class CloseChannelEvent
class CustomGroupEvent
class EditGroupEvent(val customId: Long)
class SearchEvent
class TracksChangedEvent
class LiveChangeEvent
class ExitEvent()