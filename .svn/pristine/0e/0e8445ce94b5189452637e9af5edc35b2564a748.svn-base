package com.google.chuangke.page.menu

import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.bumptech.glide.Glide
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Constants
import com.google.chuangke.common.event.*
import com.google.chuangke.util.SPUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class MenuAppFragment : BaseFragment() {

    private lateinit var mTvTips1: TextView
    private lateinit var mTvTips2: TextView
    private lateinit var mTvTips3: TextView
    private lateinit var mTvTips4: TextView
    private lateinit var mIvQrCode: ImageView
    private lateinit var mTvRefresh: TextView

    override fun layoutId(): Int {
        return R.layout.fragment_menu_app
    }

    override fun initView(view: View) {
        mTvTips1 = view.findViewById(R.id.tv_fragment_menu_app_tips1)
        mTvTips2 = view.findViewById(R.id.tv_fragment_menu_app_tips2)
        mTvTips3 = view.findViewById(R.id.tv_fragment_menu_app_tips3)
        mTvTips4 = view.findViewById(R.id.tv_fragment_menu_app_tips4)
        mIvQrCode = view.findViewById(R.id.iv_fragment_menu_app_qr_code)
        mTvRefresh = view.findViewById(R.id.tv_fragment_menu_app_refresh)
    }

    override fun initListener() {
        mTvRefresh.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    return@setOnKeyListener true
                }
            }
            false
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onAttentionInfoEvent(event: AttentionInfoEvent) {
        val attention = SPUtils.getString(requireContext(), Constants.SP_KEY_ATTENTION, null)
        try {
            val attentionJSONArray = JSONArray.parseArray(attention)

            for ((i, item) in attentionJSONArray.withIndex()){
                val info = item as JSONObject
                when (i){
                    0-> {
                        mTvTips1.append(info.getString("Title"))
                        mTvTips1.append(":")
                        mTvTips1.append(info.getString("Content"))
                    }
                    1-> {
                        mTvTips2.append(info.getString("Title"))
                        mTvTips2.append(":")
                        mTvTips2.append(info.getString("Content"))
                    }
                    2-> {
                        mTvTips3.append(info.getString("Title"))
                        mTvTips3.append(":")
                        mTvTips3.append(info.getString("Content"))
                    }
                    3-> {
                        mTvTips4.append(info.getString("Title"))
                        mTvTips4.append(":")
                        mTvTips4.append(info.getString("Content"))
                    }
                }
                if(info.getString("Title") == "qrcode"){
                    Glide.with(requireActivity())
                        .load(info.getString("Content"))
                        .error(R.mipmap.dif_ic_logo_default)
                        .into(mIvQrCode)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onStart() {
        super.onStart()
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }
}