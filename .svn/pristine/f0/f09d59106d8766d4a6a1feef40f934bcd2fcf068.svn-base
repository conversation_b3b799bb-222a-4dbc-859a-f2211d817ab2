package com.google.chuangke.page.center

import android.animation.LayoutTransition
import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.*
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.get
import androidx.core.view.isVisible
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.leanback.widget.VerticalGridView
import androidx.recyclerview.widget.RecyclerView
import com.google.chuangke.page.center.presenter.ChannelPresenter
import com.google.chuangke.page.center.presenter.TagPresenter
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.common.UnitCallback
import com.google.chuangke.common.event.CloseChannelEvent
import com.google.chuangke.common.event.CustomGroupEvent
import com.google.chuangke.common.event.EditGroupEvent
import com.google.chuangke.common.event.EpgDownLoadCompleteEvent
import com.google.chuangke.data.ChannelViewModel
import com.google.chuangke.data.TAG_ADD
import com.google.chuangke.data.TAG_SEARCH
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.CAN_EDIT
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelCollectionBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.ext.containsAnyOfIgnoreCase
import com.google.chuangke.ext.shows
import com.google.chuangke.ext.toast
import com.google.chuangke.page.center.presenter.EpgPresenter
import com.google.chuangke.page.center.presenter.WeekPresenter
import com.google.chuangke.page.dialog.ChooseGroupDialog
import com.google.chuangke.page.dialog.OnChooseGroupDialogChooseListener
import com.google.chuangke.page.dialog.UnlockDialog
import com.google.chuangke.page.listener.OnProgramSelectListener
import com.google.chuangke.page.menu.MenuLockKeyboardDialog
import com.google.chuangke.page.menu.OnMenuLockKeyboardClickListener
import com.google.chuangke.util.DateUtil
import com.google.chuangke.util.SPUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.lang.IllegalArgumentException
import java.util.*

interface OnMenuRequestFocusListener {
    fun onMenuRequestFocus()
}

class ChannelFragment(
    private val mOnProgramSelectListener: OnProgramSelectListener,
    private var mOnMenuRequestFocusListener: OnMenuRequestFocusListener? = null
) : BaseFragment(), OnChooseGroupDialogChooseListener {

    var especialChannel: Boolean = false

    private val mChannelViewModel: ChannelViewModel by viewModel()

    private lateinit var mTvTag: TextView
    private lateinit var mTvCount: TextView
    private lateinit var mTvDuration: TextView
    private lateinit var mTvProgram: TextView
    private lateinit var mTvDescription: TextView

    private lateinit var mRvTag: VerticalGridView
    private lateinit var mRvChannel: VerticalGridView
    private lateinit var mRvEpg: VerticalGridView
    private lateinit var mRvWeek: VerticalGridView

    private lateinit var mLlChannel: LinearLayout
    private lateinit var mLlRightArrow: LinearLayout
    private lateinit var mLlProgramDetail: LinearLayout
    private lateinit var mTvDate: TextView

    private lateinit var mLlLock: LinearLayout

    private lateinit var mTagAdapter: ArrayObjectAdapter
    private lateinit var mChannelAdapter: ArrayObjectAdapter
    private lateinit var mEpgAdapter: ArrayObjectAdapter
    lateinit var mWeekAdapter: ArrayObjectAdapter

    private lateinit var mWeekPresenter: WeekPresenter
    private lateinit var mChannelPresenter: ChannelPresenter
    private lateinit var mEpgPresenter: EpgPresenter

    private lateinit var mTvPassword: TextView
    private var sbPassword: StringBuilder = StringBuilder("")
    private var mMenuLockKeyboardDialog: MenuLockKeyboardDialog? = null
    private var mUnlockDialog: UnlockDialog? = null

    // 第一次加载
    private var firstLoad = true

    // Epg显示的状态
    private var epgShow = false

    /**
     * 当前待解锁频道index
     */
    private var currentUnlockIndex: Int? = null

    private var mChooseGroupDialog: ChooseGroupDialog? = null

    override fun layoutId(): Int {
        return R.layout.fragment_center_channel
    }

    override fun initView(view: View) {

        val transition = LayoutTransition()
        transition.setDuration(500)
        view.findViewById<ConstraintLayout>(R.id.root_fragment_channel).layoutTransition =
            transition

        mTvTag = view.findViewById(R.id.tv_fragment_channels_channel_tag)
        mTvCount = view.findViewById(R.id.tv_fragment_channels_channel_count)
        mTvDuration = view.findViewById(R.id.tv_fragment_channels_duration)
        mTvProgram = view.findViewById(R.id.tv_fragment_channels_program)
        mTvDescription = view.findViewById(R.id.tv_fragment_channels_description)

        mRvTag = view.findViewById(R.id.rv_fragment_channel_tag)
        mRvChannel = view.findViewById(R.id.rv_fragment_channel_channel)
        mRvEpg = view.findViewById(R.id.rv_fragment_channel_program)
        mRvWeek = view.findViewById(R.id.rv_fragment_channel_date)

        mLlChannel = view.findViewById(R.id.ll_fragment_channels_channel)
        mLlRightArrow = view.findViewById(R.id.ll_fragment_channels_channel_arrow)
        mLlProgramDetail = view.findViewById(R.id.ll_fragment_channel_program_detail)
        mTvDate = view.findViewById(R.id.tv_fragment_channel_date)

        mLlLock = view.findViewById(R.id.ll_fragment_channels_channel_lock)
        mTvPassword = view.findViewById(R.id.tv_fragment_channels_channel_password)

        view.findViewById<View>(R.id.root_fragment_channel).setOnKeyListener { v, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                when (keyCode) {
                    KeyEvent.KEYCODE_DPAD_RIGHT -> {
                        mRvTag.requestFocus()
                        return@setOnKeyListener true
                    }
                }
                return@setOnKeyListener false
            }
            return@setOnKeyListener false
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initAdapter()
        mChannelViewModel.getSevenDate()
    }

    private fun initAdapter() {
        mRvTag.adapter = ItemBridgeAdapter(ArrayObjectAdapter(TagPresenter().also {
            it.needKeep = true
        }).also { mTagAdapter = it }).also {
            it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                    super.onCreate(viewHolder)
                    viewHolder.itemView.setOnClickListener { itemView ->
                        itemView.tag?.let { tag ->
                            // 搜索
                            if ((tag as TagBean).id == TAG_SEARCH) {
                                EventBus.getDefault().post(CloseChannelEvent())
                                startActivity(
                                    Intent(
                                        requireActivity(), ChannelSearchActivity::class.java
                                    )
                                )
                            } else if (tag.id == TAG_ADD) {
                                // 添加自定义
                                EventBus.getDefault().post(CustomGroupEvent())
                            } else if ((tag.custom ?: 0) == CAN_EDIT) {
                                // 自定义的分组进行编辑
                                EventBus.getDefault().post(EditGroupEvent(tag.id))
                            }
                        }
                    }
                }
            })
        }

        mChannelPresenter = ChannelPresenter().also {
            it.needKeep = false
        }
        mRvChannel.adapter = ItemBridgeAdapter(ArrayObjectAdapter(mChannelPresenter).also {
            mChannelAdapter = it
        }).also {
            it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                    super.onCreate(viewHolder)

                    // 频道切换
                    viewHolder.itemView.setOnClickListener { itemView ->
                        val bean = itemView.tag as ChannelBean
                        if (bean.id != Config.getInstance().currentPlayChannel.id && bean.locked == 1) {
                            showUnlockDialog {
                                mOnProgramSelectListener.onProgramSelect(
                                    mTagAdapter[mRvTag.selectedPosition] as TagBean,
                                    bean
                                )
                            }
                        } else {
                            mOnProgramSelectListener.onProgramSelect(
                                mTagAdapter[mRvTag.selectedPosition] as TagBean,
                                bean
                            )
                        }
                    }

                    // 收藏操作
                    viewHolder.itemView.setOnLongClickListener { itemView ->
                        val bean = itemView.tag as ChannelBean
                        if (bean.collection == 1) {
                            DBApi.getInstance().removeCollection(bean.id!!)
                            bean.collection = 0
                            viewHolder.itemView.tag?.let { value ->
                                it.notifyItemChanged(mChannelAdapter.indexOf(value))
                            }
                        } else {
                            val customTagBeansCount = DBApi.getInstance().getCustomTags().size
                            if (customTagBeansCount > 1) {
                                showChooseCustomGroup(mChannelAdapter.indexOf(bean))
                            } else {
                                val collectionBean = ChannelCollectionBean()
                                collectionBean.unid = bean.id!!
                                // 默认是1
                                collectionBean.ctId = 1
                                collectionBean.createTime =
                                    (System.currentTimeMillis() / 1000).toInt()
                                DBApi.getInstance().saveCollection(collectionBean)

                                bean.collection = 1

                                viewHolder.itemView.tag?.let { value ->
                                    it.notifyItemChanged(mChannelAdapter.indexOf(value))
                                }
                            }
                        }
                        return@setOnLongClickListener true
                    }
                }
            })
        }

        mRvEpg.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(EpgPresenter(this@ChannelFragment).also {
                mEpgPresenter = it
            }).also { mEpgAdapter = it }).also {
                it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                    override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                        super.onCreate(viewHolder)
                        viewHolder.itemView.setOnClickListener { itemView ->

                            val channelBean =
                                mChannelAdapter[mRvChannel.selectedPosition] as ChannelBean

                            val epgBean = itemView.tag as EpgBean

                            val callback = {
                                // 没有回播，EPG为空，还没到播放时间，跳转到直播
//                            if (channelBean.playback == 0 || epgBean.beginTime!! > System.currentTimeMillis() / 1000) {
                                mOnProgramSelectListener.onProgramSelect(
                                    mTagAdapter[mRvTag.selectedPosition] as TagBean, channelBean
                                )
//                                requireActivity().toast(getString(R.string.no_playback_to_live))
//                            } else {
//                                // 回播
//                                mOnProgramSelectListener.onProgramSelect(
//                                    mTagAdapter[mRvTag.selectedPosition] as TagBean,
//                                    channelBean,
//                                    epgBean
//                                )
//                            }
                            }

                            if (channelBean.locked == 1 && channelBean.id != Config.getInstance().currentPlayChannel!!.id) {
                                showUnlockDialog(callback)
                            } else {
                                callback.invoke()
                            }
                        }
                    }
                })
            }

        mWeekPresenter = WeekPresenter(this@ChannelFragment)
        mRvWeek.adapter = ItemBridgeAdapter(ArrayObjectAdapter(mWeekPresenter).also {
            mWeekAdapter = it
        }).also {
            it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                    super.onCreate(viewHolder)
                    viewHolder.itemView.setOnClickListener { itemView ->

                    }
                }
            })
        }

    }

    /**
     * 左移右移的显示和隐藏
     * @param isShow true-左侧显示右侧隐藏,false-右侧显示左侧隐藏
     */
    private fun setArrowShow(isShow: Boolean) {
        // 左侧
        mRvTag.visibility = if (isShow) View.VISIBLE else View.GONE
        mLlRightArrow.visibility = if (isShow) View.VISIBLE else View.GONE

        // 右侧
        mLlProgramDetail.visibility = if (isShow) View.GONE else View.VISIBLE
        mRvEpg.visibility = if (isShow) View.GONE else View.VISIBLE
        mTvDate.visibility = if (isShow) View.GONE else View.VISIBLE

        if ((mTagAdapter.get(mRvTag.selectedPosition) as TagBean).name?.containsAnyOfIgnoreCase(
                Config.getInstance().sportChannel
            ) == true
        ) {
            mTvDate.visibility = View.GONE
            mRvWeek.visibility = View.GONE
        } else {
            mRvWeek.visibility = if (isShow) View.GONE else View.VISIBLE
        }

    }

    override fun initObserve() {

        mChannelViewModel.tagLiveData.observe(this) {
            mRvTag.requestFocus()
            mTagAdapter.setItems(it, null)
            if (it.isNotEmpty()) {
                var position = 0
                for (i in it.indices) {
                    // 如果是收藏情况下，直接选中收藏
                    if (it[i].id == Config.getInstance().currentTag.id && it[i].custom == Config.getInstance().currentTag.custom) {
                        position = i
                        break
                    }
                }
                mRvTag.selectedPosition = position
            }
        }

        mChannelViewModel.channelLiveData.observe(this) {
            mTvCount.text = "${if (it.isNotEmpty()) 1 else 0}/${it.size}"
            mChannelAdapter.setItems(it, null)
            if (firstLoad) {
                if (it.isNotEmpty())
                    Handler(Looper.myLooper()!!).postDelayed({
                        var position = 0
                        for (i in it.indices) {
                            if (it[i].id == Config.getInstance().currentPlayChannel.id) {
                                position = i
                                break
                            }
                        }
                        mRvChannel.selectedPosition = position
                        mRvChannel.requestFocus()
                    }, 50L)
            } else {
                if (it.isNotEmpty()) {
                    mRvChannel.selectedPosition = 0
                }
            }

            firstLoad = false
        }

        mChannelViewModel.epgLiveData.observe(this) {
            mEpgAdapter.setItems(it, null)
            if (it.isNotEmpty()) {
                when (pageScroll) {
                    1 -> {
                        mRvEpg.requestFocus()
                        mRvEpg.selectedPosition = it.size - 1
                    }

                    2 -> {
                        mRvEpg.requestFocus()
                        mRvEpg.selectedPosition = 0
                    }

                    else -> {
                        // 匹配是否有当前节目
                        val currentProgramPosition = it.currentProgramPosition()
                        if (currentProgramPosition == -1) {
                            val item =
                                mChannelAdapter.get(mRvChannel.selectedPosition) as ChannelBean?
                            if (item != null && item.tags?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
                                var position = 0
                                //要求列表是从早到晚排序
                                for (index in it.indices) {
                                    if (DateUtil.isToday(Date(it[index].endTime!! * 1000L))) {
                                        position = index
                                        break
                                    }
                                }
                                mRvEpg.selectedPosition = position
                            } else {
                                mRvEpg.selectedPosition = 0
                            }
                        } else {
                            mRvEpg.selectedPosition = currentProgramPosition
                        }
                    }
                }
            } else {
                showEpgInfo(null)
            }
            pageScroll = 0
        }

        mChannelViewModel.dateLiveData.observe(this) {
            mWeekAdapter.setItems(it, null)
            mRvWeek.selectedPosition = CURRENT_DATE_INDEX
        }

    }

    var isTagFirst = false

    override fun initListener() {

        mTvPassword.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }
            if (keyCode in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9) {
                if (sbPassword.length < 4) {
                    sbPassword.append(keyCode - 7)
                    mTvPassword.text = sbPassword
                }
                return@setOnKeyListener true
            }
            if (keyCode == KeyEvent.KEYCODE_DEL) {
                if (sbPassword.isNotEmpty()) {
                    sbPassword.deleteCharAt(sbPassword.lastIndex)
                    mTvPassword.text = sbPassword
                }
                return@setOnKeyListener true
            }
            if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER) {
                showKeyBoardDialog()
            }
            if (keyCode == KeyEvent.KEYCODE_DPAD_RIGHT || keyCode == KeyEvent.KEYCODE_DPAD_UP || keyCode == KeyEvent.KEYCODE_DPAD_DOWN) {
                return@setOnKeyListener true
            }
            false
        }

        mRvTag.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    if (mChannelAdapter.size() == 0) {
                        return@setOnKeyInterceptListener true
                    } else {
                        if (mRvChannel.visibility == View.VISIBLE) {
                            mRvChannel.requestFocus()
                            return@setOnKeyInterceptListener true
                        } else {
                            mTvPassword.requestFocus()
                            return@setOnKeyInterceptListener true
                        }
                    }
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (isTagFirst) {
                        mOnMenuRequestFocusListener?.onMenuRequestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_PAGE_UP -> {
                    var tempPosition = mRvTag.selectedPosition - PAGE_TAG_COUNT
                    if (tempPosition < 1) {
                        tempPosition = 1
                    }
                    mRvTag.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    var tempPosition = mRvTag.selectedPosition + PAGE_DATE_COUNT
                    if (tempPosition >= mTagAdapter.size()) {
                        tempPosition = mTagAdapter.size() - 1
                    }
                    mRvTag.selectedPosition = tempPosition
                }
            }

            return@setOnKeyInterceptListener false
        }

        mRvTag.setOnChildViewHolderSelectedListener(object : OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)

                isTagFirst = mTagAdapter.indexOf(child!!.itemView.tag) == 0

                val id = (child.itemView.tag as TagBean).id
                if (id == TAG_ADD || id == TAG_SEARCH) {
                    return
                }

                // 特殊频道要隐藏日期切换
                if ((child.itemView.tag as TagBean).name?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
                    mTvDate.visibility = View.GONE
                    mRvWeek.visibility = View.GONE
                    especialChannel = true
                } else {
                    especialChannel = false
                    if (epgShow) {
                        mTvDate.visibility = View.VISIBLE
                        mRvWeek.visibility = View.VISIBLE
                    }
                }

                if ((child.itemView.tag as TagBean).passwordAccess!! == 1 && !Config.getInstance().isUnLock) {
                    currentUnlockIndex = position
                    showLock(true)
                } else {
                    showLock(false)
                    mTvTag.text = (child.itemView.tag as TagBean).name
                    mChannelViewModel.getChannelList(position)
                }
            }
        })

        mRvChannel.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    if (epgShow) {
                        mRvWeek.setSelectedPositionSmooth(CURRENT_DATE_INDEX)
                        // 隐藏Epg信息
                        epgShow = false
                        setArrowShow(true)
                        return@setOnKeyInterceptListener true
                    } else {
                        // 解决左按时候，跳转到了menu项
                        mChannelPresenter.needKeep = false
                        mRvTag.requestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    // 如果没有显示Epg
                    if (!epgShow) {
                        epgShow = true
                        setArrowShow(false)
                        return@setOnKeyInterceptListener true
                    }

                    mChannelPresenter.needKeep()

                    // 右侧没数据移动到日期
                    if (mEpgAdapter.size() <= 0 && epgShow) {
                        // 如果特殊频道[mRvWeek]是隐藏的
                        if (mRvWeek.visibility == View.VISIBLE) mRvWeek.requestFocus()
                    } else {
                        mRvEpg.requestFocus()
                        // 滚动到当前的Program去
                        // 数据切换的时候已经滚动到指定位置了
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DPAD_UP, KeyEvent.KEYCODE_DPAD_DOWN -> {
                    // 上下选中位置重置为今天
                    if (epgShow) mRvWeek.setSelectedPositionSmooth(CURRENT_DATE_INDEX)
                }

                KeyEvent.KEYCODE_PAGE_UP -> {
                    // 上下选中位置重置为今天
                    if (epgShow) mRvWeek.setSelectedPositionSmooth(CURRENT_DATE_INDEX)

                    var tempPosition = mRvChannel.selectedPosition - PAGE_CHANNEL_COUNT
                    if (tempPosition < 0) {
                        tempPosition = 0
                    }
                    mTvCount.text = "${tempPosition + 1}/${mChannelAdapter.size()}"
                    mRvChannel.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    // 上下选中位置重置为今天
                    if (epgShow) mRvWeek.setSelectedPositionSmooth(CURRENT_DATE_INDEX)

                    var tempPosition = mRvChannel.selectedPosition + PAGE_CHANNEL_COUNT
                    if (tempPosition >= mChannelAdapter.size()) {
                        tempPosition = mChannelAdapter.size() - 1
                    }
                    mTvCount.text = "${tempPosition + 1}/${mChannelAdapter.size()}"
                    mRvChannel.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_BOOKMARK, KeyEvent.KEYCODE_ZENKAKU_HANKAKU -> {
                    val bean = mChannelAdapter.get(mRvChannel.selectedPosition) as ChannelBean
                    if (bean.collection == 1) {
                        DBApi.getInstance().removeCollection(bean.id!!)
                        bean.collection = 0

                        mRvChannel.adapter?.notifyItemChanged(mRvChannel.selectedPosition)

                    } else {
                        val customTagBeansCount = DBApi.getInstance().getCustomTags().size
                        if (customTagBeansCount > 1) {
                            showChooseCustomGroup(mChannelAdapter.indexOf(bean))
                        } else {
                            val collectionBean = ChannelCollectionBean()
                            collectionBean.unid = bean.id!!
                            // 默认是1
                            collectionBean.ctId = 1
                            collectionBean.createTime =
                                (System.currentTimeMillis() / 1000).toInt()
                            DBApi.getInstance().saveCollection(collectionBean)

                            bean.collection = 1

                            mRvChannel.adapter?.notifyItemChanged(mRvChannel.selectedPosition)
                        }
                    }
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_PROG_YELLOW -> {
                    val bean =
                        mChannelAdapter[mRvChannel.selectedPosition] as ChannelBean
                    if (bean.locked == 1) {
                        showUnlockDialog {
                            mChannelViewModel.saveLockedChannel(bean.id!!)
                            bean.locked = 0
                            mRvChannel.adapter?.notifyItemChanged(mRvChannel.selectedPosition)
                        }
                    } else {
                        bean.locked = 1
                        mChannelViewModel.saveLockedChannel(bean.id!!)
                        mRvChannel.adapter?.notifyItemChanged(mRvChannel.selectedPosition)
                    }

                    return@setOnKeyInterceptListener true
                }
            }
            return@setOnKeyInterceptListener false
        }

        mRvChannel.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            @SuppressLint("SetTextI18n")
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                child?.let {
                    mTvCount.text = "${position + 1}/${mChannelAdapter.size()}"
                    mChannelViewModel.getEpgListByDay(
                        mTagAdapter.get(mRvTag.selectedPosition) as TagBean,
                        it.itemView.tag as ChannelBean,
                        Date()
                    )
                }
            }
        })

        mRvEpg.setOnChildViewHolderSelectedListener(object : OnChildViewHolderSelectedListener() {
            @SuppressLint("SetTextI18n")
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                child?.itemView?.tag?.let {
                    showEpgInfo(it as EpgBean)
                }
            }
        })

        mRvEpg.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    mWeekPresenter.cancelNeedKeep()
                    // 解决左按时候，跳转到了menu项
                    mRvChannel.requestFocus()
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    if (mRvWeek.visibility == View.VISIBLE) {
                        mWeekPresenter.needKeep()
                        mRvWeek.requestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_UP -> {
                    if (mRvEpg.selectedPosition == 0 && !especialChannel && mRvWeek.selectedPosition > 0) {
                        // 上翻页
                        pageScroll = 1
                        mWeekPresenter.needKeep()
                        mRvWeek.requestFocus()
                        mRvWeek.selectedPosition--
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    if (mRvEpg.selectedPosition == mEpgAdapter.size() - 1 && !especialChannel && mRvWeek.selectedPosition < mWeekAdapter.size()) {
                        // 下翻页
                        mWeekPresenter.needKeep()
                        pageScroll = 2
                        mRvWeek.requestFocus()
                        mRvWeek.selectedPosition++
                        return@setOnKeyInterceptListener true
                    }
                }

                KeyEvent.KEYCODE_PAGE_UP -> {
                    var tempPosition = mRvEpg.selectedPosition - PAGE_EPG_COUNT
                    if (tempPosition < 0) {
                        tempPosition = 0
                    }
                    mRvEpg.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    var tempPosition = mRvEpg.selectedPosition + PAGE_EPG_COUNT
                    if (tempPosition >= mEpgAdapter.size()) {
                        tempPosition = mEpgAdapter.size() - 1
                    }
                    mRvEpg.selectedPosition = tempPosition
                }
            }
            false
        }

        mRvWeek.setOnChildViewHolderSelectedListener(object : OnChildViewHolderSelectedListener() {
            @SuppressLint("SetTextI18n")
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                mChannelViewModel.getEpgListByDay(
                    child!!.itemView.tag as Date
                )
            }
        })


        mRvWeek.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_LEFT -> {
                    if (mEpgAdapter.size() <= 0) {
                        mWeekPresenter.cancelNeedKeep()
                        mRvChannel.requestFocus()
                    } else {
                        mRvEpg.requestFocus()
                    }

                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_PAGE_UP -> {
                    var tempPosition = mRvWeek.selectedPosition - PAGE_DATE_COUNT
                    if (tempPosition < 0) {
                        tempPosition = 0
                    }
                    mRvWeek.selectedPosition = tempPosition
                }

                KeyEvent.KEYCODE_PAGE_DOWN -> {
                    var tempPosition = mRvWeek.selectedPosition + PAGE_DATE_COUNT
                    if (tempPosition >= mWeekAdapter.size()) {
                        tempPosition = mWeekAdapter.size() - 1
                    }
                    mRvWeek.selectedPosition = tempPosition
                }
            }
            false
        }
    }

    // 0 普通，1上翻，2下翻
    var pageScroll: Int = 0

    /**
     * 隐藏时候重置
     */
    fun hide() {
        firstLoad = true
        epgShow = false
        showLock(false)
        setArrowShow(true)
        mWeekPresenter.cancelNeedKeep()
        mRvWeek.selectedPosition = CURRENT_DATE_INDEX
    }

    fun show() {
        // 隐藏前处于空列表，直接初始化
        if (mRvChannel.selectedPosition == -1) {
            mChannelViewModel.initData()
            return
        }
        // 特殊频道要隐藏日期切换
        especialChannel =
            Config.getInstance().currentTag.name?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true

        // 可能发生了切台，搜索台数据变化，进行新的查询
        val selectTag = mTagAdapter[mRvTag.selectedPosition] as TagBean
        val selectChannel = mChannelAdapter[mRvChannel.selectedPosition] as ChannelBean
        // 标签没有切换，那么频道列表页没有变
        if (Config.getInstance().currentTag.id == selectTag.id && selectTag.custom == Config.getInstance().currentTag.custom) {
            if (Config.getInstance().currentPlayChannel.id != selectChannel.id) {
                // 频道发生变化
                mChannelViewModel.getChannelList(mRvTag.selectedPosition)
            } else {
                // 没有变化就把焦点切上去，同时把第一次加载的标识消耗掉
                firstLoad = false
                mRvChannel.requestFocus()
                mChannelAdapter.notifyArrayItemRangeChanged(0, mChannelAdapter.size())
            }
        } else {
            // 标签和频道都发生变化（收藏情况包含）
            mChannelViewModel.initData()
        }
    }

    fun onDownMove() {
        mRvTag.postDelayed({ mRvTag[1].requestFocus() }, 100L)
    }

    @SuppressLint("SetTextI18n")
    private fun showEpgInfo(epg: EpgBean?) {
        if (epg != null) {
            val startTime = epg.beginTime?.let { DateUtil.getHourTime(it) }
            val endTime = epg.endTime?.let { DateUtil.getHourTime(it) }

            mTvDuration.text = "$startTime-$endTime"
            mTvProgram.text = epg.name
            mTvDescription.text = epg.description
        } else if (mChannelViewModel.tempChannelBean?.tags?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
            mTvDuration.text = getString(R.string.empty_no_game)
            mTvProgram.text = getString(R.string.empty_no_game)
            mTvDescription.text = getString(R.string.empty_no_game)
        } else {
            mTvDuration.text = getString(R.string.empty_no_data)
            mTvProgram.text = getString(R.string.empty_no_data)
            mTvDescription.text = getString(R.string.empty_no_data)
        }
    }

    private fun showLock(show: Boolean) {
        if (show) {
            mLlChannel.visibility = View.GONE
            mLlRightArrow.visibility = View.GONE
            mRvChannel.visibility = View.GONE
            mLlLock.visibility = View.VISIBLE
        } else {
            if (mLlRightArrow.isVisible) {
                return
            }
            mLlLock.visibility = View.GONE
            mLlChannel.visibility = View.VISIBLE
            mLlRightArrow.visibility = View.VISIBLE
            mRvChannel.visibility = View.VISIBLE
            mRvTag.requestFocus()
        }
    }

    /**
     * 弹出键盘
     */
    private fun showKeyBoardDialog() {
        if (mMenuLockKeyboardDialog == null) {
            mMenuLockKeyboardDialog = MenuLockKeyboardDialog(requireActivity(), R.style.Dialog)
            mMenuLockKeyboardDialog!!.mOnMenuLockKeyboardClickListener =
                object : OnMenuLockKeyboardClickListener {
                    override fun onClick(word: String) {
                        if (sbPassword.length <= 4) {
                            when (word) {
                                requireActivity().getString(R.string.key_board_del) -> {
                                    if (sbPassword.isNotEmpty()) {
                                        sbPassword.deleteCharAt(sbPassword.lastIndex)
                                        mTvPassword.text = sbPassword
                                    }
                                }

                                requireActivity().getString(R.string.key_board_done) -> {
                                    if (sbPassword.length < 4) {
                                        Toast.makeText(
                                            requireActivity(),
                                            requireActivity().getString(R.string.menu_lock_alert),
                                            Toast.LENGTH_SHORT
                                        ).show()
                                    } else {
                                        unLockTag()
                                    }
                                }

                                else -> {
                                    if (sbPassword.length < 4) {
                                        sbPassword.append(word)
                                        mTvPassword.text = sbPassword
                                    }
                                }
                            }
                        }
                    }
                }
        }
        mMenuLockKeyboardDialog?.shows()
    }

    private fun showChooseCustomGroup(index: Int) {
        if (mChooseGroupDialog == null) {
            mChooseGroupDialog = ChooseGroupDialog(requireActivity(), R.style.Dialog, this)
        }
        mChooseGroupDialog?.showDialog(index)
    }

    /**
     * 解锁
     */
    private fun unLockTag() {
        val lockNum = SPUtils.getString(requireActivity(), Constants.SP_KEY_LOCK, "0000");
        if (sbPassword.toString() == lockNum) {
            mMenuLockKeyboardDialog?.dismiss()
            Config.getInstance().isUnLock = true
            showLock(false)
            mChannelViewModel.getChannelList(currentUnlockIndex!!)
        } else {
            Toast.makeText(
                requireActivity(),
                requireActivity().getText(R.string.menu_lock_authentication_failed),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    /**
     * 获取当前选中的日期
     */
    fun getCurrentDate(): Date? {
        if (mRvWeek.selectedPosition > mWeekAdapter.size() - 1) return null
        return mWeekAdapter[mRvWeek.selectedPosition] as Date
    }

    /**
     * Epg下载完成，如果此时channel打开，并没有显示EPG的数据，需要更新一下
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEpgDownLoadCompleteEvent(event: EpgDownLoadCompleteEvent) {
        mChannelAdapter.notifyArrayItemRangeChanged(0, mChannelAdapter.size())
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        EventBus.getDefault().register(this)
        return super.onCreateView(inflater, container, savedInstanceState)
    }

    override fun onDestroyView() {
        EventBus.getDefault().unregister(this)
        super.onDestroyView()
    }

    companion object {
        private const val CURRENT_DATE_INDEX = 7
        private const val PAGE_CHANNEL_COUNT = 7
        private const val PAGE_EPG_COUNT = 7
        private const val PAGE_DATE_COUNT = 7
        private const val PAGE_TAG_COUNT = 12
    }

    override fun onChoose(customIds: List<Long>, index: Int) {
        customIds.forEach { customId ->
            val collectionBean = ChannelCollectionBean()
            val bean = mChannelAdapter.get(index) as ChannelBean
            collectionBean.unid = bean.id
            // 默认是1
            collectionBean.ctId = customId
            collectionBean.createTime =
                (System.currentTimeMillis() / 1000).toInt()
            DBApi.getInstance().saveCollection(collectionBean)

            bean.collection = 1
        }

        mRvChannel.adapter!!.notifyItemChanged(index)
    }

    private fun showUnlockDialog(callback: UnitCallback) {
        if (mUnlockDialog == null) {
            mUnlockDialog = UnlockDialog(requireActivity(), R.style.Dialog, callback = callback)
        } else {
            mUnlockDialog!!.callback = callback
        }

        if (!mUnlockDialog!!.isShowing) {
            mUnlockDialog!!.showDialog()
        }
    }
}

/**
 * 将Epg排序
 * 找出当前播放节目的Position,未找到返回第一项-0
 */
fun List<EpgBean>.currentProgramPosition(): Int {

    if (this.isEmpty()) throw IllegalArgumentException()

    val currentTimeSeconds = DateUtil.currentTimeSeconds()
    for (index in this.indices) {
        if (this[index].beginTime!! <= currentTimeSeconds && this[index].endTime!! > currentTimeSeconds) {
            return index
        }
    }
    return -1
}