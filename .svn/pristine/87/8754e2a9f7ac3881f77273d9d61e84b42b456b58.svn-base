package com.google.chuangke.page.multiple.data

import androidx.lifecycle.viewModelScope
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.TagBean
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch

@OptIn(FlowPreview::class)
class MsChannelViewModel : BaseViewModel() {

    private val _tagState = MutableSharedFlow<CategoryState>(replay = 1)
    val tagState = _tagState.asSharedFlow()

    private val _channelState = MutableSharedFlow<ChannelState>(replay = 1)
    val channelState = _channelState.asSharedFlow()

    val cursorPositionFlow = MutableSharedFlow<TagBean>(replay = 1)

    init {
        viewModelScope.launch {
            cursorPositionFlow
                .debounce(300)
                .collectLatest {
                    getChannelList(tagBean = it)
                }
        }

        initData()
    }

    fun initData() {
        launch {
            DBApi.getInstance().allTag.let {
                _tagState.emit(CategoryState.Success(it.toMutableList()))
            }
        }
    }

    private fun getChannelList(tagBean: TagBean) {
        launch {
            DBApi.getInstance().getChannelByTag(tagBean.id)?.let {
                _channelState.emit(ChannelState.Success(it.toMutableList()))
            }
        }
    }
}