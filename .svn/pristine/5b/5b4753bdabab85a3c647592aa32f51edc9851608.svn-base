<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="300dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_black"
        android:orientation="vertical"
        android:padding="30dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:text="Choose Group"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <androidx.leanback.widget.VerticalGridView
            android:id="@+id/rv_dialog_add_group"
            android:layout_width="wrap_content"
            android:layout_height="150dp"
            android:layout_marginTop="20dp" />

        <TextView
            android:id="@+id/tv_dialog_add_group_save"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="35dp"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="SAVE"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_dialog_add_group_cancel"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="35dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="CANCEL"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp" />
    </LinearLayout>

</RelativeLayout>