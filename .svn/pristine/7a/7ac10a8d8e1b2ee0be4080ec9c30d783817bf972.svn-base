package com.google.chuangke.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Environment;

import androidx.core.content.ContextCompat;

import com.google.chuangke.MyApplication;
import com.google.chuangke.R;
import com.google.chuangke.common.Constants;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

public class FileHelper {

    // 将 JSON 字符串写入文件
    public static void writeJsonToFile(Context context, String json) {
        // 获取文件夹路径
        File folder = Environment.getExternalStoragePublicDirectory(MyApplication.context.getString(R.string.app_name));

        // 如果文件夹不存在，创建文件夹
        if (!folder.exists()) {
            folder.mkdirs();
        }

        // 创建文件对象
        File file = new File(folder, Constants.FILE_NAME_CUSTOM_GROUP);

        try {
            // 使用 FileWriter 将字符串写入文件
            FileWriter writer = new FileWriter(file);
            writer.write(json);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 从文件中读取 JSON 字符串
    public static String readJsonFromFile(Context context) {
        // 获取文件对象
        File folder = Environment.getExternalStoragePublicDirectory(MyApplication.context.getString(R.string.app_name));

        // 创建文件对象
        File file = new File(folder, Constants.FILE_NAME_CUSTOM_GROUP);


        // 如果文件不存在，返回空字符串
        if (!file.exists()) {
            return "";
        }

        // 使用 BufferedReader 读取文件内容
        StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader reader = new BufferedReader(new FileReader(file));
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return stringBuilder.toString();
    }

    public static String readTagSortFromFile(Context context) {
        // 获取文件对象
        File folder;
        if (ContextCompat.checkSelfPermission(
                context, Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED || ContextCompat.checkSelfPermission(
                context, Manifest.permission.READ_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED
        ) {
            folder = Environment.getDataDirectory();

        } else {
            folder = Environment.getExternalStoragePublicDirectory("heatlive");
        }

        // 创建文件对象
        File file = new File(folder, Constants.FILE_NAME_TAG_SORT);


        // 如果文件不存在，返回空字符串
        if (!file.exists()) {
            return "";
        }

        // 使用 BufferedReader 读取文件内容
        StringBuilder stringBuilder = new StringBuilder();
        try {
            BufferedReader reader = new BufferedReader(new FileReader(file));
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return stringBuilder.toString();
    }

    public static void writTagSortToFile(Context context, String tags) {
        // 获取文件夹路径
        File folder;
        if (ContextCompat.checkSelfPermission(
                context, Manifest.permission.WRITE_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED || ContextCompat.checkSelfPermission(
                context, Manifest.permission.READ_EXTERNAL_STORAGE
        ) != PackageManager.PERMISSION_GRANTED
        ) {
            folder = Environment.getDataDirectory();
        } else {
            folder = Environment.getExternalStoragePublicDirectory("heatlive");
        }

        // 如果文件夹不存在，创建文件夹
        if (!folder.exists()) {
            folder.mkdirs();
        }

        // 创建文件对象
        File file = new File(folder, Constants.FILE_NAME_TAG_SORT);

        try {
            // 使用 FileWriter 将字符串写入文件
            FileWriter writer = new FileWriter(file);
            writer.write(tags);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
