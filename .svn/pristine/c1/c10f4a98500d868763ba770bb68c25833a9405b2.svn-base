package com.google.chuangke.page.center

import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.fragment.app.FragmentTransaction
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.HorizontalGridView
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.recyclerview.widget.RecyclerView
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.data.CenterViewModel
import com.google.chuangke.page.listener.OnProgramSelectListener
import com.google.chuangke.page.presenter.CenterOptionPresenter
import org.koin.androidx.viewmodel.ext.android.viewModel

class CenterFragment(private val mOnProgramSelectListener: OnProgramSelectListener) :
    BaseFragment(), OnMenuRequestFocusListener {

    private val mCenterViewModel: CenterViewModel by viewModel()

    private lateinit var mRvMenu: HorizontalGridView
    private lateinit var mTvInfo: TextView

    private lateinit var mOptionAdapter: ArrayObjectAdapter

    private var mChannelFragment: ChannelFragment? = null
    private var mMenuFragment: MenuFragment? = null
    private var mSportsFragment: SportsFragment? = null

    var currentIndex = 1

    override fun layoutId(): Int {
        return R.layout.fragment_center
    }

    override fun initView(view: View) {
        mRvMenu = view.findViewById(R.id.rv_fragment_center_option)
        mTvInfo = view.findViewById(R.id.tv_fragment_center)
        mRvMenu.adapter = ItemBridgeAdapter(ArrayObjectAdapter(CenterOptionPresenter()).also {
            mOptionAdapter = it
        })
    }

    override fun initListener() {
        mRvMenu.setOnChildViewHolderSelectedListener(object : OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                showTab(position)
            }
        })

        mRvMenu.setOnKeyInterceptListener { keyEvent ->

            if (keyEvent.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (keyEvent.keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    if (currentIndex == 1) {
                        mChannelFragment?.onDownMove()
                        return@setOnKeyInterceptListener true
                    }
                }
            }

            return@setOnKeyInterceptListener false
        }
    }

    override fun initObserve() {
        mCenterViewModel.optionLiveData.observe(this) {
            mOptionAdapter.setItems(it, null)
            mRvMenu.selectedPosition = 1
            mRvMenu.requestFocus()
        }
        mCenterViewModel.infoLiveData.observe(this) {
            mTvInfo.text = it
            mTvInfo.isSelected = true;
        }
    }

    private fun showTab(position: Int) {
        currentIndex = position
        val ft = childFragmentManager.beginTransaction()
        hideFragment(ft)
        when (position) {
            1 -> {
                if (mChannelFragment == null) {
                    mChannelFragment = ChannelFragment(mOnProgramSelectListener, this)
                    ft.add(
                        R.id.fl_fragment_center, mChannelFragment!!, "mChannelFragment"
                    )
                } else {
                    ft.show(mChannelFragment!!)
                }
            }

            2 -> {
                if (mSportsFragment == null) {
                    mSportsFragment = SportsFragment()
                    ft.add(
                        R.id.fl_fragment_center, mSportsFragment!!, "mSportsFragment"
                    )
                } else {
                    ft.show(mSportsFragment!!)
                }
            }

            else -> {
                if (mMenuFragment == null) {
                    mMenuFragment = MenuFragment(mOnProgramSelectListener, this)
                    ft.add(
                        R.id.fl_fragment_center, mMenuFragment!!, "mMenuFragment"
                    )
                } else {
                    ft.show(mMenuFragment!!)
                }
            }
        }
        ft.commit()
    }

    private fun hideFragment(ft: FragmentTransaction) {
        mChannelFragment?.let {
            ft.hide(it)
        }
        mMenuFragment?.let {
            ft.hide(it)
        }
        mSportsFragment?.let {
            ft.hide(it)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            mChannelFragment?.hide()
        } else {
            mChannelFragment?.show()
        }
    }

    fun close() {
        if (currentIndex == 0) {
            mMenuFragment?.hide()
            Handler(Looper.myLooper()!!).postDelayed({
                mRvMenu.requestFocus()
                mRvMenu.selectedPosition = 1
            }, 200L)
        }
    }

    override fun onMenuRequestFocus() {
        mRvMenu.requestFocus()
    }
}