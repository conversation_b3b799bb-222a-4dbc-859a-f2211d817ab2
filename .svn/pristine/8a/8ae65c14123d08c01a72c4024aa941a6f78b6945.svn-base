<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="365dp"
        android:layout_height="match_parent"
        android:background="@color/bg_content"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:orientation="vertical"
        tools:ignore="UselessParent">

        <TextView
            android:id="@+id/tv_fragment_menu_lock_old"
            android:layout_width="315dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="70dp"
            android:background="@drawable/selector_input"
            android:drawablePadding="14dp"
            android:focusable="true"
            android:gravity="center_vertical"
            android:hint="@string/menu_lock_old_password"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:textColor="@color/selector_color_hint"
            android:textColorHint="@color/selector_color_hint"
            android:textSize="14sp"
            android:visibility="gone"
            app:drawableStartCompat="@mipmap/ic_menu_input_lock"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_fragment_menu_lock_input"
            android:layout_width="315dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:background="@drawable/selector_input"
            android:drawablePadding="14dp"
            android:focusable="true"
            android:gravity="center_vertical"
            android:hint="@string/menu_lock_please_set_password"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:textColor="@color/selector_color_hint"
            android:textColorHint="@color/selector_color_hint"
            android:textSize="14sp"
            app:drawableStartCompat="@mipmap/ic_menu_input_lock" />

        <TextView
            android:id="@+id/tv_fragment_menu_lock_submit"
            style="@style/font_bold"
            android:layout_width="315dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="@string/menu_lock_submit"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_fragment_menu_lock_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:focusable="false"
            android:paddingStart="25dp"
            android:paddingEnd="25dp"
            android:text="@string/menu_lock_tips"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>