package com.google.chuangke.inject

import android.app.Application
import com.google.chuangke.data.*
import com.google.chuangke.data.menu.*
import com.google.chuangke.database.DBApi
import com.google.chuangke.page.multiple.data.MsChannelViewModel
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidFileProperties
import org.koin.android.ext.koin.androidLogger
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.core.context.startKoin
import org.koin.dsl.module

/**
 *  InjectionApplication
 */
open class InjectionApplication : Application() {

    override fun onCreate() {
        super.onCreate()
        startKoin {
            androidLogger()
            androidContext(this@InjectionApplication)
            androidFileProperties()
            modules(listOf(movieModule))
        }
    }

    /**
     * inject module
     */
    private val movieModule = module {

        single {
            ClassicEpgRepository(get())
        }

        viewModel {
            MsChannelViewModel()
        }

        viewModel {
            ClassicEpgViewModel(get(), get())
        }

        single {
            ManageCustomTagRepository(get())
        }

        viewModel {
            ManageCustomTagViewModel(get())
        }

        viewModel {
            MainViewModel(get())
        }

        viewModel {
            MenuInfoViewModel()
        }

        viewModel {
            MenuFavoriteViewModel(get())
        }

        viewModel {
            ChannelViewModel(get(), get(), get())
        }

        single {
            MenuFavoriteRepository(get())
        }

        viewModel {
            ChannelSearchViewModel(get())
        }

        single {
            MenuSearchRepository(get())
        }

        viewModel {
            CenterViewModel()
        }

        single {
            ChannelRepository(get())
        }

        single {
            EpgRepository(get())
        }

        single { DBApi.getInstance() }
    }
}