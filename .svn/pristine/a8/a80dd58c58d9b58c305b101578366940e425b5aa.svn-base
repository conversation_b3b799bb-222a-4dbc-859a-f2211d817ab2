package com.google.chuangke.util

import android.app.AlertDialog
import android.content.Context
import android.view.ViewGroup
import android.widget.Button
import com.google.chuangke.R

/**
 * 布局焦点切换
 */
fun focusViewGroupSwitching(
    lockView: ViewGroup, unlockView: ViewGroup
) {
    Utils.Ins().changeViewFocus(lockView, false, false)
    Utils.Ins().changeViewDescendantFocusability(lockView, unlockView)
    Utils.Ins().changeViewFocus(unlockView, true, false)
}


/**
 * 弹出框按钮设置
 */
fun settingAlertDialogButtonStyle(resources: Context, button: Button) {
    button.setTextColor(resources.getColorStateList(R.color.selector_dialog_text))
    button.setBackgroundResource(R.drawable.selector_dialog_button)
    button.setPadding(20, 0, 20, 0)
}

/**
 * 弹出框按钮设置
 */
fun AlertDialog.settingAlertDialogButton(negativeFocus: Boolean = true) {

    val positiveButton: Button = getButton(AlertDialog.BUTTON_POSITIVE)
    val negativeButton: Button = getButton(AlertDialog.BUTTON_NEGATIVE)

    settingAlertDialogButtonStyle(context, positiveButton)
    settingAlertDialogButtonStyle(context, negativeButton)

    if (negativeFocus)
        negativeButton.requestFocus()
    else
        positiveButton.requestFocus()
}