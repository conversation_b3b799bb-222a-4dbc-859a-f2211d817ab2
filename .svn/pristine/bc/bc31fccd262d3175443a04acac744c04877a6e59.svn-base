package com.google.chuangke

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import com.google.chuangke.common.Config
import com.google.chuangke.common.UserHelper
import com.google.chuangke.database.ObjectBox
import com.google.chuangke.inject.InjectionApplication
import com.google.chuangke.page.SplashActivity
import com.google.chuangke.player.PlayerHelper
import com.google.chuangke.util.FileUtil
import com.jakewharton.threetenabp.AndroidThreeTen
import com.orhanobut.logger.AndroidLogAdapter
import com.orhanobut.logger.Logger


open class MyApplication : InjectionApplication(), Application.ActivityLifecycleCallbacks {

    override fun onCreate() {
        super.onCreate()
        context = applicationContext
        registerActivityLifecycleCallbacks(this)

        try {
            ObjectBox.init(this)
        } catch (e: Exception) {
            FileUtil.deleteDirWihtFile(filesDir)
            ObjectBox.init(this)
        }

        AndroidThreeTen.init(this)
        initLogger()
        go2.Seq.setContext(this)
    }

    private fun initLogger() {
        Logger.addLogAdapter(object : AndroidLogAdapter() {
            override fun isLoggable(priority: Int, tag: String?): Boolean {
                return BuildConfig.DEBUG
            }
        })
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        lateinit var context: Context
        lateinit var currentActivity: Activity
    }

    private var activityCount = 0

    private fun incrementActivityCount() {
        activityCount++
    }

    private fun decrementActivityCount() {
        activityCount--
    }

    open fun isAppInBackground(): Boolean {
        return activityCount == 0
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {}

    override fun onActivityStarted(activity: Activity) {
        currentActivity = activity
    }

    override fun onActivityResumed(activity: Activity) {
        incrementActivityCount()
//        PlayerHelper.getInstance().play()
    }

    override fun onActivityPaused(activity: Activity) {
        decrementActivityCount()
//        if (isAppInBackground() && activity !is SplashActivity) {
//            PlayerHelper.getInstance().pause()
//        }
    }

    override fun onActivityStopped(activity: Activity) {
//        if (isAppInBackground() && activity !is SplashActivity && !Config.getInstance().isRequestPermission) {
//            UserHelper.getInstance().exit()
//        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {}
    override fun onActivityDestroyed(activity: Activity) {}
}