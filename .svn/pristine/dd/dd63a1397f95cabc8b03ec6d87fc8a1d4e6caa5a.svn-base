package com.google.chuangke.page.dialog

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.EditText
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.DisableSpeechDialog
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.CAN_EDIT
import com.google.chuangke.entity.CustomTagBean

class CustomGroupDialog(
    activity: Activity, themeId: Int
) : DisableSpeechDialog(activity, themeId) {

    private lateinit var mEtName: EditText
    private lateinit var mTvSave: TextView
    private lateinit var mTvCancel: TextView
    private lateinit var mTvDelete: TextView

    private var mCustomTagBean: CustomTagBean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_custom_group)
        initView()
        val window: Window? = this.window
        if (window != null) {
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
            )
            window.setDimAmount(0f)
        }
        initListener()
    }

    private fun initView() {
        mEtName = findViewById(R.id.et_dialog_custom_group)
        mTvSave = findViewById(R.id.tv_dialog_custom_group_save)
        mTvCancel = findViewById(R.id.tv_dialog_custom_group_cancel)
        mTvDelete = findViewById(R.id.tv_dialog_custom_group_delete)
    }

    private fun initListener() {
        mTvSave.setOnClickListener {
            val name = mEtName.text.toString()
            if (name.isNotEmpty()) {
                if (mCustomTagBean == null) {
                    mCustomTagBean = CustomTagBean()
                    mCustomTagBean!!.edit = CAN_EDIT
                    mCustomTagBean!!.createTime = (System.currentTimeMillis() / 1000).toInt()
                }
                mCustomTagBean!!.name = name
                DBApi.getInstance().saveOrUpdateCustomTag(mCustomTagBean!!)
                dismiss()
            }
        }

        mTvCancel.setOnClickListener {
            dismiss()
        }

        mTvDelete.setOnClickListener {
            DBApi.getInstance().deleteCustomTag(mCustomTagBean!!.id!!)
            dismiss()
        }
    }

    fun showDialog(customId: Long? = null) {
        show()
        mEtName.requestFocus()
        mCustomTagBean = if (customId == null) {
            null
        } else {
            DBApi.getInstance().customTag(customId)
        }

        mEtName.setText(mCustomTagBean?.name ?: "")
        if (mCustomTagBean == null) {
            mTvCancel.visibility = View.VISIBLE
            mTvDelete.visibility = View.GONE
        } else {
            mTvCancel.visibility = View.GONE
            mTvDelete.visibility = View.VISIBLE
        }
    }

}