package com.google.chuangke.page.menu.adapter

import android.graphics.Typeface
import android.view.KeyEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.page.adapter.RecyclerExtras
import java.util.*

class SearchChannelAdapter(chanRv: RecyclerView) :
    BaseQuickAdapter<ChannelBean, BaseViewHolder>(R.layout.item_menu_search_channel) {

    var rv: RecyclerView = chanRv
    private var onItemKeyListener: RecyclerExtras.OnItemKeyListener? = null

    fun setOnItemKeyListener(onItemKeyListener: RecyclerExtras.OnItemKeyListener?) {
        this.onItemKeyListener = onItemKeyListener
    }

    fun setSelection(position: Int) {
        if (position < 0 || position >= this.itemCount) {
            return
        }
        var selMVHolder: BaseViewHolder? =
            rv.findViewHolderForAdapterPosition(position) as BaseViewHolder?
        if (selMVHolder != null) {
            selMVHolder.itemView.requestFocus()
        } else {
            rv.scrollToPosition(position)
            rv.post(Runnable {
                selMVHolder =
                    rv.findViewHolderForAdapterPosition(position) as BaseViewHolder?
                selMVHolder?.itemView?.requestFocus()
            })
        }
    }

    override fun convert(holder: BaseViewHolder, item: ChannelBean) {

        holder.setText(R.id.tv_item_menu_search_channel_no, "${item.channelNumber}")
        holder.setText(R.id.tv_item_menu_search_channel_title, item.name)

        val currentEpg = DBApi.getInstance().getCurrentEpgList(item, Date()).let { epgList ->
            if (epgList != null && epgList.isNotEmpty()) {
                epgList[0].name
            } else {
                context.getString(R.string.empty_no_data)
            }
        }

        holder.setText(R.id.tv_item_menu_search_channel_subtitle, currentEpg)

        Glide.with(context)
            .load("${Config.getInstance().logoUrl}${item.channelId}.png?v=${Config.getInstance().resourceVersion}")
            .error(R.mipmap.dif_ic_logo_default)
            .diskCacheStrategy(DiskCacheStrategy.ALL)//缓存所有台标
            .into(holder.getView(R.id.iv_item_menu_search_channel_logo))

        holder.itemView.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(view: View, keyCode: Int, keyEvent: KeyEvent): Boolean {
                onItemKeyListener?.onItemKey(view, holder.layoutPosition, keyEvent, keyCode)

                if (keyEvent.action == KeyEvent.ACTION_UP) {
                    return false
                }

                if (keyCode == KeyEvent.KEYCODE_DPAD_LEFT
                    || keyCode == KeyEvent.KEYCODE_DPAD_RIGHT
                    || keyCode == KeyEvent.KEYCODE_BACK
                    || keyCode == KeyEvent.KEYCODE_DPAD_DOWN && holder.layoutPosition == data.size - 1
                    || keyCode == KeyEvent.KEYCODE_DPAD_UP && holder.layoutPosition == 0
                ) {
                    return true
                }

                return false
            }
        })
        holder.itemView.onFocusChangeListener = View.OnFocusChangeListener { _, hasFocus ->
            selectHolder(holder, hasFocus)
        }
    }

    private fun selectHolder(holder: BaseViewHolder, select: Boolean) {
        holder.itemView.findViewById<LinearLayout>(R.id.ll_item_menu_search_channel_root)
            .isSelected = select
        selectItem(holder.itemView.findViewById(R.id.tv_item_menu_search_channel_no), select)
        selectItem(holder.itemView.findViewById(R.id.tv_item_menu_search_channel_title), select)
        selectItem(holder.itemView.findViewById(R.id.tv_item_menu_search_channel_subtitle), select)
    }

    private fun selectItem(view: TextView, select: Boolean) {
        view.isSelected = select
        view.typeface = if (select) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
    }
}