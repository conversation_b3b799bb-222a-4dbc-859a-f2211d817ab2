package com.google.chuangke.database

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.common.Constants
import com.google.chuangke.common.UserHelper
import com.google.chuangke.common.event.EpgDownLoadCompleteEvent
import com.google.chuangke.entity.*
import com.google.chuangke.util.FileHelper
import com.google.chuangke.util.FileUtil
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import io.objectbox.Box
import io.objectbox.query.QueryBuilder
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.util.*


class DBApi private constructor() {

    // 频道收藏 box
    private var channelCollectionBox: Box<ChannelCollectionBean>

    // 观看历史 box
    private var channelHistoryBox: Box<ChannelHistoryBean>

    // Tag box
    private var tagBox: Box<TagBean>

    // Channel box
    private var channelBox: Box<ChannelBean>

    // EPG box
    private var epgBox: Box<EpgBean>

    private val customTagBeanBox: Box<CustomTagBean>

    private var channelLockBox: Box<ChannelLockBean> =
        ObjectBox.store.boxFor(ChannelLockBean::class.java)

    /**
     * 所有频道
     */
    fun allChannel(): List<ChannelBean> {
        val query = channelBox.query().order(ChannelBean_.channelNumber)
        val list = query.build().find()
        query.close()

        return list.filterNot {
            it.tags?.contains(Constants.FILTER_CONDITION) ?: false || it.tags?.contains(
                "[]"
            ) ?: false
        }
    }

    fun getTagWithoutLock(): List<TagBean> {
        return allTag.filterNot { it.passwordAccess == 1 }
    }

    val allTag: List<TagBean>
        get() {
            val query = tagBox.query()
            val list = query.order(TagBean_.intOrder).build().find()
            query.close()
            return list
        }


    val allSpecialTag: List<String>
        get() {
            val query = tagBox.query(TagBean_.isSpecial.equal(1))
            val list = query.order(TagBean_.intOrder).build().find()
            query.close()
            return list.map { it.name!! }
        }

    val allCollectionChannel: List<ChannelCollectionBean>
        get() {
            val query = channelCollectionBox.query()
            val list = query.build().find()
            query.close()
            return list
        }

    fun customTagChannelsId(customId: Long): List<ChannelCollectionBean> {
        val query = channelCollectionBox.query(ChannelCollectionBean_.ctId.equal(customId))
        val list = query.build().find()
        query.close()
        return list
    }

    /**
     * 删除或新增收藏
     */
    fun saveChannelCollection(collectionBean: ChannelCollectionBean) {

        val query = channelCollectionBox.query(
            ChannelCollectionBean_.unid.equal(
                collectionBean.unid!!
            )
        )

        val list = query.build().find()
        query.close()

        if (list.isNotEmpty()) {
            list.forEach { bean ->
                channelCollectionBox.remove(bean)
            }
        } else {
            channelCollectionBox.put(collectionBean)
        }
    }

    /**
     * 查询某个频道是否收藏
     */
    fun isChannelCollection(channelId: Long): Boolean {

        val query = channelCollectionBox.query(ChannelCollectionBean_.unid.equal(channelId))

        val count = query.build().count()

        query.close()

        return count > 0
    }

    fun getChannelByTag(tagId: Long): List<ChannelBean> {
        val query =
            channelBox.query(ChannelBean_.tags.contains("\"$tagId\"")).order(ChannelBean_.intOrder)
        val list = query.build().find()
        query.close()

        return list
    }

    fun getChannelByChannelNumber(channelNumber: Int): List<ChannelBean> {

        val query = channelBox.query(ChannelBean_.channelNumber.equal(channelNumber))

        val list = query.build().find()

        query.close()

        return list.filterNot {
            it.tags?.contains(Constants.FILTER_CONDITION) ?: false || it.tags?.contains(
                "[]"
            ) ?: false
        }
    }

    fun getChannelByEpg(epgBean: EpgBean): List<ChannelBean> {
        val query = channelBox.query(ChannelBean_.channelId.equal(epgBean.channelId!!))
        val list = query.build().find()
        query.close()
        return list.filterNot { it.tags!!.contains(Constants.FILTER_CONDITION) }
    }

    fun getChannelByChannelId2(channelId: Long): List<ChannelBean> {
        val query = channelBox.query(ChannelBean_.channelId.equal(channelId))
        val list = query.build().find()
        query.close()
        return list.filterNot { it.tags!!.contains(Constants.FILTER_CONDITION) }
    }

    fun getChannelByChannelId(historyId: Long): List<ChannelBean> {
        val query = channelBox.query(ChannelBean_.id.equal(historyId))
        val list = query.build().find()
        query.close()
        return list.filterNot { it.tags!!.contains(Constants.FILTER_CONDITION) }
    }

    fun getChannelByChannelIds(ids: LongArray): List<ChannelBean> {
        val query: QueryBuilder<ChannelBean> =
            channelBox.query().`in`(ChannelBean_.id, ids).order(ChannelBean_.channelNumber)
        val list: List<ChannelBean> = query.build().find()
        query.close()
        return list
    }

    fun getEpgByChannel(channelBean: ChannelBean): List<EpgBean> {

        val query = epgBox.query(EpgBean_.channelId.equal(channelBean.id!!))

        val list = query.build().find()

        query.close()

        return list
    }

    fun getEpgListByDay(channelBean: ChannelBean, date: Date): List<EpgBean> {

        val cl = Calendar.getInstance(Locale.getDefault())
        cl.time = date
        cl.set(Calendar.HOUR_OF_DAY, 0)
        cl.set(Calendar.MINUTE, 0)
        cl.set(Calendar.SECOND, 0)
        cl.set(Calendar.MILLISECOND, 0)

        val dayBegin = cl.timeInMillis / 1000

//        Log.e("dayBegin111", "${cl.time}")

        cl.set(Calendar.HOUR_OF_DAY, 23)
        cl.set(Calendar.MINUTE, 59)
        cl.set(Calendar.SECOND, 59)
        cl.set(Calendar.MILLISECOND, 999)
        val dayEnd = cl.timeInMillis / 1000

//        Log.e("dayEnd111", "${cl.time}")

        val query = epgBox.query(
            EpgBean_.channelId.equal(channelBean.channelId!!.toInt())
                .and(EpgBean_.beginTime.less(dayEnd.toInt()))
                .and(EpgBean_.endTime.greater(dayBegin.toInt()))
        ).order(EpgBean_.beginTime)

        val list = query.build().find()

        query.close()

        return list
    }

    /**
     * 特殊频道查询10天全部内容
     */
    fun getEspecialEpgListByDay(channelBean: ChannelBean, date: Date): List<EpgBean> {

        val cl = Calendar.getInstance(Locale.getDefault())
        cl.time = date
        //后3天
        cl.add(Calendar.DAY_OF_YEAR, 3)
        cl.set(Calendar.HOUR_OF_DAY, 0)
        cl.set(Calendar.MINUTE, 0)
        cl.set(Calendar.SECOND, 0)
        cl.set(Calendar.MILLISECOND, 0)

        val dayEnd = cl.timeInMillis / 1000

//        Log.e("dayBegin111", "${cl.time}")

        // 前7天
        cl.add(Calendar.DAY_OF_YEAR, -10)
        cl.set(Calendar.HOUR_OF_DAY, 23)
        cl.set(Calendar.MINUTE, 59)
        cl.set(Calendar.SECOND, 59)
        cl.set(Calendar.MILLISECOND, 999)
        val dayBegin = cl.timeInMillis / 1000

//        Log.e("dayEnd111", "${cl.time}")

        val query = epgBox.query(
            EpgBean_.channelId.equal(channelBean.channelId!!.toInt())
                .and(EpgBean_.beginTime.greaterOrEqual(dayBegin.toInt()))
                .and(EpgBean_.beginTime.lessOrEqual(dayEnd.toInt()))
        ).order(EpgBean_.beginTime)

        val list = query.build().find()

        query.close()

        return list
    }

    /**
     * 获取当前的epg
     */
    fun getCurrentEpgList(channelBean: ChannelBean, date: Date): List<EpgBean> {
        val currentTime = date.time / 1000
//        Logger.e("mEpgBeanSession:dayBegin->$currentTime")

        val query = epgBox.query(
            EpgBean_.channelId.equal(channelBean.channelId!!)
                .and(EpgBean_.endTime.greaterOrEqual(currentTime))
                .and(EpgBean_.beginTime.lessOrEqual(currentTime))
        )

        val list = query.build().find()

        query.close()

        return list

    }

    /**
     * 获取当前的epg
     */
    fun getSpecialCurrentEpgList(channelBean: ChannelBean, date: Date): List<EpgBean> {

        val cl = Calendar.getInstance(Locale.getDefault())
        cl.time = date
        cl.set(Calendar.HOUR_OF_DAY, 0)
        cl.set(Calendar.MINUTE, 0)
        cl.set(Calendar.SECOND, 1)
        cl.set(Calendar.MILLISECOND, 0)
        val dayBegin = cl.timeInMillis / 1000

        cl.set(Calendar.HOUR_OF_DAY, 23)
        cl.set(Calendar.MINUTE, 59)
        cl.set(Calendar.SECOND, 59)
        cl.set(Calendar.MILLISECOND, 999)
        val dayEnd = cl.timeInMillis / 1000

        val query = epgBox.query(
            EpgBean_.channelId.equal(channelBean.channelId!!)
                .and(EpgBean_.endTime.greaterOrEqual(dayBegin))
                .and(EpgBean_.beginTime.lessOrEqual(dayEnd))
        ).order(EpgBean_.beginTime)

        val list = query.build().find()

        query.close()

        return list

    }

    /**
     * 获取下一个epg
     */
    fun getNextEpg(channelId: Long, startTime: Int): List<EpgBean> {

        val query = epgBox.query(
            EpgBean_.channelId.equal(channelId).and(EpgBean_.beginTime.equal(startTime))

        )

        val list = query.build().find(0, 1)

        query.close()

        return list
    }

    /**
     * 模糊查询搜索频道
     */
    fun getChannelListByKeyword(keyword: String): List<ChannelBean> {
        val query = channelBox.query(
            ChannelBean_.name.contains(
                keyword, QueryBuilder.StringOrder.CASE_INSENSITIVE
            )
        )

        val list = query.build().find()
        query.close()

        return list.filterNot {
            it.tags?.contains(Constants.FILTER_CONDITION) ?: false || it.tags?.contains(
                "[]"
            ) ?: false
        }
    }

    /**
     * 获取近期10条历史观看记录
     */
    fun getChannelHistory(): List<ChannelHistoryBean> {

        val query = channelHistoryBox.query().orderDesc(ChannelHistoryBean_.createTime)

        val list = query.build().find(0, 10)

        query.close()

        return list
    }

    /**
     * 保存历史观看记录
     */
    fun saveChannelHistory(bean: ChannelHistoryBean) {

        val query = channelHistoryBox.query(
            ChannelHistoryBean_.unid.equal(
                bean.unid!!
            )
        )

        val list = query.build().find()
        query.close()

        list.let { ls ->
            ls.forEach {
                channelHistoryBox.remove(it)
            }
        }

        channelHistoryBox.put(bean)

    }

    fun getCustomTags(): MutableList<CustomTagBean> = customTagBeanBox.all

    fun saveOrUpdateCustomTag(customTagBean: CustomTagBean): Long {
        return customTagBeanBox.put(customTagBean)
    }

    fun customTagExist(name: String): Boolean {
        return customTagBeanBox.query(CustomTagBean_.name.equal(name)).build().find().size > 0
    }

    fun customTag(customId: Long): CustomTagBean {
        return customTagBeanBox.get(customId)
    }

    fun deleteCustomTag(id: Long): Boolean {
        val list = channelCollectionBox.query(ChannelCollectionBean_.ctId.equal(id)).build().find()
        list.forEach {
            channelCollectionBox.remove(it)
        }
        return customTagBeanBox.remove(id)
    }

    fun saveCollection(collectionBean: ChannelCollectionBean) {
        channelCollectionBox.put(collectionBean)
    }

    fun removeCollection(id: Long) {
        val query = channelCollectionBox.query(
            ChannelCollectionBean_.unid.equal(
                id
            )
        )

        val list = query.build().find()
        query.close()

        if (list.isNotEmpty()) {
            list.forEach { bean ->
                channelCollectionBox.remove(bean)
            }
        }
    }

    fun initCustomTag() {
        if (customTagBeanBox.count() == 0L) {
            val json = FileHelper.readJsonFromFile(MyApplication.context)
            if (json.isNullOrEmpty()) {
                Logger.e("initCustomTag=>isNullOrEmpty")
                val customTagBean = CustomTagBean().also {
                    it.name = "Favorite List"
                    it.edit = CAN_NOT_EDIT
                    it.createTime = (System.currentTimeMillis() / 1000).toInt()
                }
                val defaultFavoriteId = customTagBeanBox.put(customTagBean)
                val oldData = channelCollectionBox.all
                oldData.forEach {
                    it.ctId = defaultFavoriteId
                    channelCollectionBox.put(it)
                }
            } else {
                Logger.e("initCustomTag=>parseObject")
                val jsonObject = JSON.parseObject(json)
                jsonObject.getString("tags")?.let {
                    val array = JSON.parseArray(it)
                    Logger.e("initCustomTag=>tags:${array.size}")
                    for (item in array) {
                        val itemObject = item as JSONObject
                        val tag = CustomTagBean()
                        tag.name = itemObject.getString("name")
                        tag.edit = itemObject.getInteger("edit")
                        tag.createTime = itemObject.getInteger("createTime")
                        customTagBeanBox.put(tag)
                    }
                }
                jsonObject.getString("channels")?.let {
                    val array = JSON.parseArray(it)
                    Logger.e("initCustomTag=>channels:${array.size}")
                    for (item in array) {
                        val itemObject = item as JSONObject
                        val channel = ChannelCollectionBean()
                        channel.unid = itemObject.getLong("unid")
                        channel.ctId = itemObject.getLong("ctId")
                        channel.createTime = itemObject.getInteger("createTime")
                        channelCollectionBox.put(channel)
                    }
                }
            }
        }
    }

    companion object {
        private var ins: DBApi? = null
        fun getInstance(): DBApi {
            if (ins == null) {
                synchronized(DBApi::class.java) {
                    ins = DBApi()
                }
            }
            return ins!!
        }
    }

    /**
     * EPG下载成功后
     */
    @Synchronized
    fun initEpgDao(dbPath: String): Boolean {
        val file = File(dbPath)
        if (file.isFile && file.exists()) {
            val data: List<EpgBean?>? = DBManager.instance!!.queryEpg(dbPath)
            if (!data.isNullOrEmpty()) {
                try {
                    ObjectBox.store.runInTx {
                        epgBox.removeAll()
                        epgBox.put(data)
                    }
                    EventBus.getDefault().post(EpgDownLoadCompleteEvent())
                    return true
                } catch (e: Exception) {
                    Logger.e(e.message.toString())
                    // 先保存自定义收藏
                    saveCustomGroupConfig()
                    // 删除文件
                    FileUtil.deleteDirWihtFile(File(MyApplication.context.filesDir.path, "objectbox"))
                    SPUtils.remove(MyApplication.context, Constants.DB_NAME_EPG.hashCode().toString())
                    // 退出重新进入
                    UserHelper.getInstance().exit()
                }
            }
        }
        return false
    }

    private fun saveCustomGroupConfig() {
        val tags = getCustomTags()
        val channels = allCollectionChannel
        val map = mutableMapOf<String, Any>()
        map["tags"] = tags
        map["channels"] = channels
        FileHelper.writeJsonToFile(MyApplication.context, JSON.toJSONString(map))
    }

    @Synchronized
    fun initOtherDB(dbPath:String): Boolean {
        val file = File(dbPath)
        if (file.isFile && file.exists()) {
            try {
                ObjectBox.store.runInTx {
                    tagBox.removeAll()
                    channelBox.removeAll()

                    val dMap = DBManager.instance!!.queryOther(dbPath)
                    if (dMap.isNotEmpty()){
                        val channels: List<ChannelBean> = dMap["channels"] as List<ChannelBean>
                        if (channels.isNotEmpty()){
                            channelBox.put(channels)
                        }
                        val tags: List<TagBean> = dMap["tags"] as List<TagBean>
                        if (tags.isNotEmpty()){
                            tagBox.put(tags)
                        }
                    }
                }
                return true
            } catch (e: Exception) {
                Logger.e(e.message.toString())
                FileUtil.deleteDirWithFile(File(MyApplication.context.filesDir.path, "objectbox"))
                SPUtils.remove(MyApplication.context,  Constants.DB_NAME_OTHER.hashCode().toString())
                UserHelper.getInstance().exit()
            }
        }
        return false
    }

    init {
        // 初始化数据库表
        tagBox = ObjectBox.store.boxFor(TagBean::class.java)
        channelBox = ObjectBox.store.boxFor(ChannelBean::class.java)
        channelCollectionBox = ObjectBox.store.boxFor(ChannelCollectionBean::class.java)
        channelHistoryBox = ObjectBox.store.boxFor(ChannelHistoryBean::class.java)
        epgBox = ObjectBox.store.boxFor(EpgBean::class.java)
        customTagBeanBox = ObjectBox.store.boxFor(CustomTagBean::class.java)
    }

    fun release() {
        ins = null
    }

    fun clearHistory() {
        channelHistoryBox.removeAll()
    }

    /**
     * Check the channel is locked
     */
    fun isChannelLocked(channelId: Long): Boolean {
        val query = channelLockBox.query(ChannelLockBean_.unid.equal(channelId))
        val count = query.build().count()
        query.close()

        return count > 0
    }

    fun getEpgListByRange(channelId: Long, startTime: Int, endTime: Int): List<EpgBean> {
        val query = epgBox.query(
            EpgBean_.channelId.equal(channelId.toInt()).and(EpgBean_.beginTime.less(endTime))
                .and(EpgBean_.endTime.greater(startTime))
        ).order(EpgBean_.beginTime)

        val list = query.build().find()
        query.close()

        return list
    }

    /************ Channel Lock ************/
    // Delete or save locked channel
    fun saveLockedChannel(channelLockBean: ChannelLockBean) {
        val query = channelLockBox.query(
            ChannelLockBean_.unid.equal(
                channelLockBean.unid!!
            )
        )

        val list = query.build().find()
        query.close()

        if (list.isNotEmpty()) {
            list.forEach { bean ->
                channelLockBox.remove(bean)
            }
        } else {
            channelLockBox.put(channelLockBean)
        }
    }
}