package com.google.chuangke.page.vod.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter

class SearchKeyPresenter : BasePresenter<Char>() {

    override fun layoutId(): Int {
        return R.layout.item_fragment_menu_search_key
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {
        return false
    }

    override fun bindViewHolder(view: View, item: Char) {
        view.findViewById<TextView>(R.id.tv_item_fragment_menu_search_key).text = item.toString()
    }
}