<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <color name="transparent">#00000000</color>
    <color name="purpler_7B0B82">#5B61ED</color>
    <color name="purpler_per30">#4D5B61ED</color>
    <color name="white_ddd">#DDDDDD</color>
    <color name="bg_content">#99000000</color>
    <color name="bg_parent">#B2000000</color>
    <color name="white_eee">#EEEEEE</color>
    <color name="white_ccc">#CCCCCC</color>
    <color name="gray_999">#999999</color>
    <color name="white_10per">#19FFFFFF</color>
    <color name="white_50per">#80FFFFFF</color>
    <color name="white_60per">#3CFFFFFF</color>
    <color name="white_40per">#66FFFFFF</color>
    <color name="white_20per">#33FFFFFF</color>
    <color name="white_90per">#E6FFFFFF</color>
    <color name="white_aa">#AAFFFFFF</color>
    <color name="color_80dadada">#80DADADA</color>
    <color name="color_C9C9C9">#C9C9C9</color>

    <color name="black_90per">#E6000000</color>
    <color name="black_80per">#CC000000</color>
    <color name="black_70per">#B2000000</color>
    <color name="black_50per">#80000000</color>
    <color name="black_30per">#4C000000</color>
    <color name="black_10per">#19000000</color>
    <color name="black_2per">#05000000</color>

    <!-- Program Guide Start -->
    <color name="programguide_header_background">#202020</color>
    <color name="programguide_content_background">#000000</color>
    <color name="programguide_footer_background">#202020</color>
    <color name="programguide_selected_gap_color">#22FFFFFF</color>
    <color name="programguide_default_gap_color">@android:color/transparent</color>
    <color name="programguide_selected_schedule_color">#1565c0</color>
    <color name="programguide_past_schedule_color">#0E1561</color>
    <color name="programguide_default_schedule_color">#1a237e</color>
    <color name="programguide_progress_background_focused">#404040</color>
    <color name="programguide_progress_background_default">#00000000</color>
    <color name="programguide_progress_foreground_focused">#FFFFFF</color>
    <color name="programguide_progress_foreground_default">#42a5f5</color>
    <color name="programguide_time_text_color">#D0D0D0</color>
    <color name="programguide_channel_text_color">#D0D0D0</color>
    <color name="programguide_time_indicator_line">#A0D0D0D0</color>
    <color name="programguide_time_indicator_triangle">#FFFFFF</color>
    <color name="programguide_placeholder_default_color">#40FFFFFF</color>
    <color name="programguide_filter_border_focused">#1565c0</color>
    <color name="programguide_filter_border_default">#606060</color>
    <color name="programguide_filter_background_focused">#40FFFFFF</color>
    <color name="programguide_filter_background_default">#00000000</color>
    <color name="programguide_filter_icon_color">#FFFFFF</color>
    <color name="programguide_channel_item_background_color_default">#E0000000</color>
    <color name="programguide_channel_item_background_color_selected">#F0303030</color>
    <color name="programguide_detail_text_color">#CCCCCC</color>
    <color name="programguide_button_focused">#1565c0</color>
    <color name="programguide_button_default">#707070</color>
    <color name="programguide_title_text_color_focused">#FFFFFF</color>
    <color name="programguide_title_text_color_in_past">#D0D0D0</color>
    <color name="programguide_title_text_color_default">#F0F0F0</color>
    <!-- Program Guide End -->

</resources>