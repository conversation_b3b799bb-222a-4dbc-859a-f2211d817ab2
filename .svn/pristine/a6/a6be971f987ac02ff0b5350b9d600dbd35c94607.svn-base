package com.google.chuangke.page.center.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.ext.containsAnyOfIgnoreCase
import java.util.*

class ChannelPresenter : BasePresenter<ChannelBean>() {

    override fun layoutId(): Int {
        return R.layout.item_channels
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {

        // 不处理其他控件后续事件
        if (event.action == KeyEvent.ACTION_UP) {
            return false
        }

        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                needKeep = false
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                needKeep = true
            }
        }

        return false
    }

    override fun bindViewHolder(view: View, item: ChannelBean) {

        val tempTvNo = view.findViewById<TextView>(R.id.tv_item_channel_no)
        tempTvNo.text = item.channelNumber.toString()

        val tempTvName = view.findViewById<TextView>(R.id.tv_item_channel_title)
        tempTvName.text = item.name

        val epgList = mutableListOf<EpgBean>()
        epgList.addAll(DBApi.getInstance().getCurrentEpgList(item, Date()))

        // 如果特殊频道当前数据为空，则查询当天的第一条
        if (epgList.isEmpty() && item.tags?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
            epgList.addAll(DBApi.getInstance().getSpecialCurrentEpgList(item, Date()))
        }

        // 当前节目
        val currentEpg =
            if (item.tags?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
                if (epgList.isNotEmpty()) {
                    var name = ""
                    val date = Date().time / 1000
                    for (epg in epgList) {
                        if (epg.beginTime!! < date && epg.endTime!! > date) {
                            name = epg.name ?: ""
                            break
                        }
                    }
                    if (name.isEmpty()) {
                        for (epg in epgList) {
                            if (epg.endTime!! > date) {
                                name = epg.name ?: ""
                                break
                            }
                        }
                    }
                    if (name.isEmpty()) {
                        name = context.getString(R.string.empty_no_game)
                    }
                    name
                } else {
                    context.getString(R.string.empty_no_game)
                }
            } else {
                if (epgList.isNotEmpty()) {
                    epgList[0].name
                } else {
                    context.getString(R.string.empty_no_data)
                }
            }

        view.findViewById<TextView>(R.id.tv_item_channel_subtitle).text = currentEpg

        // 回拨
        view.findViewById<ImageView>(R.id.iv_item_channel_replay).visibility =
            if (item.playback == 1) View.VISIBLE
            else View.GONE

        // 收藏
        if (item.collection == null) {
            val collection = DBApi.getInstance().isChannelCollection(item.id!!)
            item.collection = if (collection) 1 else 0
        }
        view.findViewById<ImageView>(R.id.iv_item_channel_favorite_collection).visibility =
            if (item.collection == 1) View.VISIBLE
            else View.GONE

        // Lock
        if (item.locked == null) {
            val locked = DBApi.getInstance().isChannelLocked(item.id!!)
            item.locked = if (locked) 1 else 0
        }
        val ivLock = view.findViewById<ImageView>(R.id.iv_item_channel_lock)
        ivLock.visibility = if (item.locked == 1) View.VISIBLE
        else View.GONE

        // 正在播放
        val currentPlayGifView = view.findViewById<ImageView>(R.id.iv_item_channel_live)
        Glide.with(context).asGif().load(R.drawable.ic_tv_playing).into(currentPlayGifView)

        // 显示正在播放
        currentPlayGifView.visibility =
            if (item.id == Config.getInstance().currentPlayChannel.id) View.VISIBLE
            else View.INVISIBLE

        // Local
        val tvLocal = view.findViewById<ImageView>(R.id.iv_local)
        tvLocal.visibility =
            if (item.local) View.VISIBLE
            else View.GONE

        // 台标
        Glide.with(context).load(DBApi.getInstance().getChannelIcon(item, null, null))
            .error(R.mipmap.dif_icon_default).diskCacheStrategy(DiskCacheStrategy.ALL)//缓存所有台标
            .into(view.findViewById(R.id.iv_item_channel_logo))

    }

}