package com.google.chuangke.base

import android.content.Context
import android.graphics.Typeface
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.view.ViewCompat
import androidx.leanback.widget.Presenter

@Suppress("UNCHECKED_CAST")
abstract class BaseScalePresenter<T> : Presenter() {

    lateinit var context: Context

    val map = mutableMapOf<Boolean, T>()

    var lastSelectedView: View? = null

    var needKeep: Boolean = false

    abstract fun layoutId(): Int

    /**
     * 要改变字体状态的TextView id列表
     */
    abstract fun addFocusTextStyle(): List<Int>

    abstract fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean

    abstract fun bindViewHolder(view: View, item: T)

    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        context = parent.context

        val rootView = LayoutInflater.from(context).inflate(layoutId(), parent, false)

        rootView.setOnFocusChangeListener { view, hasFocus ->
            map[hasFocus] = view.tag as T
            if (map[true] == map[false] && needKeep) {
                view.isActivated = !hasFocus
                lastSelectedView = view
            } else {
                lastSelectedView?.isActivated = false
            }

            view.isSelected = hasFocus

            focusScale(hasFocus, view)

            addFocusTextStyle().forEach {
                setFocusTextStyle(view, it, hasFocus)
            }
        }

        rootView.setOnKeyListener { v, keyCode, event ->
            return@setOnKeyListener onKeyListener(v, keyCode, event)
        }

        return ViewHolder(rootView)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, item: Any) {
        viewHolder.view.tag = item
        bindViewHolder(viewHolder.view, item as T)
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {
    }

    fun needKeep() {
        needKeep = true
    }

    fun focusScale(scale: Boolean, itemView: View) {
        val scaleFloat = 1.05f
        if (scale) {
            ViewCompat.animate(itemView).scaleX(scaleFloat).scaleY(scaleFloat).translationZ(1.1f)
                .start()
        } else {
            ViewCompat.animate(itemView).scaleX(1.0f).scaleY(1.0f).translationZ(0f).start()
        }
    }
}


private fun setFocusTextStyle(rootView: View, resId: Int, hasFocus: Boolean) {
    rootView.findViewById<TextView>(resId).typeface =
        if (hasFocus) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
}