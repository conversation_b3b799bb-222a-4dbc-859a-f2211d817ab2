<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rl_activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="match_parent">

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main1"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main2"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main3"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="match_parent">

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main4"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main5"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main6"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_weight="1"
            android:layout_height="match_parent">

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main7"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main8"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

            <androidx.media3.ui.PlayerView
                android:id="@+id/spv_activity_main9"
                android:layout_width="match_parent"
                android:layout_weight="1"
                android:layout_height="match_parent"
                android:focusable="false" />

        </LinearLayout>


    </LinearLayout>

    <!--<androidx.media3.ui.PlayerView
        android:id="@+id/spv_activity_main1"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:focusable="false" />

    <androidx.media3.ui.PlayerView
        android:id="@+id/spv_activity_main2"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:layout_toRightOf="@+id/spv_activity_main1"
        android:focusable="false" />

    <androidx.media3.ui.PlayerView
        android:id="@+id/spv_activity_main3"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:layout_below="@+id/spv_activity_main1"
        android:focusable="false" />

    <androidx.media3.ui.PlayerView
        android:id="@+id/spv_activity_main4"
        android:layout_width="480dp"
        android:layout_alignParentEnd="true"
        android:layout_below="@+id/spv_activity_main2"
        android:layout_height="270dp"
        android:focusable="false" />-->

    <androidx.media3.ui.PlayerView
        android:id="@+id/spv_activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="false" />

    <TextView
        android:id="@+id/tv_activity_main_no"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="35dp"
        android:layout_marginEnd="68dp"
        android:focusable="false"
        android:textColor="#ffffff"
        android:textSize="52sp"
        tools:ignore="RelativeOverlap"
        tools:text="999"
        tools:textColor="#000000" />

    <View
        android:id="@+id/focus_catcher"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_activity_live_playback"
        style="@style/big"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:drawablePadding="20dp"
        android:focusable="false"
        tools:drawableRight="@drawable/ic_movie_info_forward"
        tools:text="1min" />

    <ImageView
        android:id="@+id/iv_activity_live_playback"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentEnd="true"
        android:layout_margin="10dp"
        android:src="@drawable/ic_replaying"
        android:visibility="gone"
        tools:visibility="visible" />

</RelativeLayout>