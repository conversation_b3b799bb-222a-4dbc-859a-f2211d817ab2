<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape>
            <corners android:radius="30dp" />
            <solid android:color="@color/white_aa" />
        </shape>
    </item>
    <item android:state_focused="true">
        <shape>
            <corners android:radius="30dp" />
            <solid android:color="@color/white_aa" />
        </shape>
    </item>
    <item android:state_selected="true">
        <shape>
            <corners android:radius="30dp" />
            <solid android:color="@color/white_aa" />
        </shape>
    </item>
    <item android:drawable="@drawable/shape_transparent_unselect" android:state_focused="false" />
    <item android:drawable="@drawable/shape_transparent_unselect" android:state_selected="false" />
</selector>

