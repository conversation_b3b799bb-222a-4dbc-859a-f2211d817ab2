<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="850dp"
        android:layout_height="450dp"
        android:background="@drawable/shape_dialog_epg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_tv_dialog_epg_logo"
                android:layout_width="110dp"
                android:layout_height="96dp"
                android:layout_marginStart="10dp"
                android:layout_marginTop="10dp"
                android:contentDescription="@null"
                tools:src="@mipmap/dif_ic_logo_default" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginStart="11dp"
                android:layout_weight="2"
                android:gravity="start"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_dialog_epg_title"
                    style="@style/font_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="21dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:textColor="@color/white"
                    android:textSize="17sp"
                    tools:text="239TV ASHOOL" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="start"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/dialog_epg_duration"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_dialog_epg_duration"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/white_ccc"
                        android:textSize="12sp"
                        tools:text="13:50-17:40" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="48.5dp"
                        android:text="@string/dialog_epg_program"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/tv_dialog_epg_program"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/white_ccc"
                        android:textSize="12sp"
                        tools:text="A Critica Noticia -News" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_dialog_epg_description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="2"
                    android:textColor="@color/white_ccc"
                    android:textSize="11sp"
                    tools:text="Acompanhe as principais noticias do dia para o esado do Amazon. " />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_marginEnd="30dp"
                android:layout_weight="1"
                android:gravity="center_vertical|end"
                android:orientation="vertical">

                <TextClock
                    style="@style/font_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:format12Hour="hh:mm aa"
                    android:format24Hour="HH:mm"
                    android:textColor="@color/white"
                    android:textSize="25sp"
                    tools:text="16:55" />

                <TextClock
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:format12Hour="EEEE M/dd"
                    android:format24Hour="EEEE M/dd"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    tools:ignore="SmallSp"
                    tools:text="Thursday 2/5" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:focusable="false"
                android:gravity="start"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/dialog_epg_tip1"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/ic_epg_up" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:text="@string/dialog_epg_tip2"
                        android:textColor="@color/white"
                        android:textSize="12sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/dialog_epg_tip3"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/ic_epg_left" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:text="@string/dialog_epg_tip4"
                        android:textColor="@color/white"
                        android:textSize="12sp" />
                </LinearLayout>
            </LinearLayout>

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:focusable="false"
                android:gravity="end"
                android:text="@string/dialog_epg_date"
                android:textColor="@color/white"
                android:textSize="15sp" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="11dp"
                android:contentDescription="@null"
                android:src="@mipmap/ic_epg_date_left" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_dialog_epg_date"
                android:layout_width="392dp"
                android:layout_height="wrap_content" />

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:contentDescription="@null"
                android:src="@mipmap/ic_epg_date_right" />
        </LinearLayout>

        <FrameLayout
            android:id="@+id/fl_dialog_epg_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:descendantFocusability="afterDescendants" />
    </LinearLayout>
</LinearLayout>
