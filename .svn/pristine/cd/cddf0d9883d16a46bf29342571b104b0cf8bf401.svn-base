<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:focusable="false" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@drawable/shape_bg_gradient"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_dialog_menu_lock_keyboard"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:descendantFocusability="afterDescendants" />
    </LinearLayout>
</LinearLayout>