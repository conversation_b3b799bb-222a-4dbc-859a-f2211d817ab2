package com.google.chuangke.page.adapter

import android.view.View
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.ext.containsAnyOfIgnoreCase
import com.google.chuangke.page.ClassicEpgFragment
import com.google.chuangke.view.OnEpgViewControlListener
import com.google.chuangke.view.OnEpgViewFocusedListener
import com.google.chuangke.view.RowView

private val sportChannel =
    arrayOf("NFL", "MLB", "NBA", "NHL", "PPV", "ESPN", "NCAAF") //体育频道NFL、MLB、NBA、NHL、PPV

class RowAdapter(
    private val mClassicEpgFragment: ClassicEpgFragment,
    private val mOnEpgViewFocusedListener: OnEpgViewFocusedListener,
    private val mOnEpgViewControlListener: OnEpgViewControlListener
) : BaseQuickAdapter<ChannelBean, BaseViewHolder>(R.layout.item_classic_epg) {


    override fun convert(holder: BaseViewHolder, item: ChannelBean) {
        holder.setText(R.id.tv_item_classic_epg_channel_name, item.name)
        holder.setText(R.id.tv_item_classic_epg_no, item.channelNumber.toString())

        // Playback
        val ivReplay = holder.getView<ImageView>(R.id.iv_item_classic_epg_replay)
        ivReplay.visibility = if (item.playback == 1) View.VISIBLE
        else View.GONE

        // Logo
        val ivLogo = holder.getView<ImageView>(R.id.iv_item_classic_epg_logo)

        Glide.with(context).load( DBApi.getInstance().getChannelIcon(item, null, null))
            .placeholder(R.mipmap.dif_icon_default)
            .diskCacheStrategy(DiskCacheStrategy.ALL)//缓存所有台标
            .into(ivLogo)

        // Favorite
        if (item.collection == null) {
            val collection = DBApi.getInstance().isChannelCollection(item.id!!)
            item.collection = if (collection) 1 else 0
        }
        val ivFavorite = holder.getView<ImageView>(R.id.iv_item_classic_epg_favorite)
        ivFavorite.visibility = if (item.collection == 1) View.VISIBLE
        else View.GONE

        // Lock
        if (item.locked == null) {
            val locked = DBApi.getInstance().isChannelLocked(item.id!!)
            item.locked = if (locked) 1 else 0
        }
        val ivLock = holder.getView<ImageView>(R.id.iv_item_classic_epg_lock)
        ivLock.visibility = if (item.locked == 1) View.VISIBLE
        else View.GONE

        // Epg
        holder.getView<RowView>(R.id.rowview_item_classic_epg).apply {
            setData(holder.position, getEpgList(item))
            mXOnEpgViewFocusedListener = mOnEpgViewFocusedListener
            mXOnEpgViewControlListener = mOnEpgViewControlListener
        }
    }

    private fun getEpgList(channelBean: ChannelBean): MutableList<EpgBean> {
        val startUtcMillis =
            (mClassicEpgFragment.currentDate.toInstant().toEpochMilli() / 1000).toInt()
        val endUtcMillis = (startUtcMillis + 2.5 * 60 * 60).toInt()

        val isSport = channelBean.tags?.containsAnyOfIgnoreCase(sportChannel) ?: false
        val channelId = channelBean.channelId!!

        val epgBeans =
            DBApi.getInstance().getEpgListByRange(channelId, startUtcMillis, endUtcMillis)
                .toMutableList()

        if (epgBeans.isEmpty()) {
            // No EPG
            epgBeans.add(createGap(startUtcMillis, endUtcMillis, isSport))
        } else {
            // subtract start item
            val firstEpg = epgBeans.first()
            if (firstEpg.beginTime!! < startUtcMillis) {
                firstEpg.startTime = startUtcMillis
            } else if ((firstEpg.beginTime!! - startUtcMillis) > 10) {
                // complete start item
                epgBeans.add(
                    0, createGap(startUtcMillis, firstEpg.beginTime!!, isSport)
                )
            }

            val lastEpg = epgBeans.last()
            if (lastEpg.endTime!! > endUtcMillis) {
                // subtract end item
                lastEpg.finishTime = endUtcMillis
            } else if ((endUtcMillis - lastEpg.endTime!!) > 10) {
                // complete end item
                epgBeans.add(createGap(lastEpg.endTime!!, endUtcMillis, isSport))
            }
        }

        epgBeans.forEach {
            it.channelId = channelBean.channelId!!.toInt()
            it.channelName = channelBean.name
        }

        return epgBeans
    }

    private fun createGap(from: Int, to: Int, isSport: Boolean): EpgBean {
        val epgBean = EpgBean()
        epgBean.name =
            context.getString(if (isSport) R.string.empty_no_game else R.string.empty_no_data)
        epgBean.description =
            context.getString(if (isSport) R.string.empty_no_game else R.string.empty_no_data)
        epgBean.startTime = from
        epgBean.finishTime = to
        epgBean.beginTime = from
        epgBean.endTime = to
        return epgBean
    }

    private fun getViewByPosition(position: Int): View? {
        return getViewByPosition(recyclerView, position)
    }

    private fun getViewByPosition(recyclerView: RecyclerView?, position: Int): View? {
        if (recyclerView == null) {
            return null
        }
        val layoutManager = recyclerView.layoutManager ?: return null
        val firstPos =
            (recyclerView.getChildAt(0).layoutParams as RecyclerView.LayoutParams).viewLayoutPosition
        val lastPos =
            (recyclerView.getChildAt(recyclerView.childCount - 1).layoutParams as RecyclerView.LayoutParams).viewLayoutPosition
        if (position < firstPos || position > lastPos) {
            return null
        }
        //obtainViewByPosition() is implemented by LinearLayoutManager, GridLayoutManager
        val view = layoutManager.findViewByPosition(position)
        return view
    }
}