#默认的proguard-android.txt已经增加了Annotation、native、view的setget方法、Activity参数为view的  方法、Enum枚举、Parcelable、R，此处不再写
#------------------------------------------通用区域----------------------------------------------------
#----------------------基本指令------------------------
-optimizationpasses 5
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers
-dontpreverify
-verbose
-printmapping proguardMapping.txt
-optimizations !code/simplification/cast,!field/*,!class/merging/*
-keepattributes *Annotation*,InnerClasses
-keepattributes Signature
-keepattributes SourceFile,LineNumberTable

#如果引用了v4或者v7包
-dontwarn android.support.**
-keep class android.support.** { *; }
-keep interface android.support.** { *; }
-keep public class * extends android.support.**
-dontwarn android.support.**

#如果引用了androidx包
-keep class com.google.android.material.** {*;}
-keep class androidx.** {*;}
-keep public class * extends androidx.**
-keep interface androidx.** {*;}
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**
-dontwarn androidx.**

#---------------------默认保留-------------------------
## 基础保留 ##
-keep public class * extends android.app.Fragment
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider
-keep public class * extends android.app.backup.BackupAgentHelper
-keep public class * extends android.preference.Preference

-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
    public void set*(...);
}
# 保持自定义控件类不被混淆
-keepclasseswithmembers class * {
    public <init>(android.content.Context, android.util.AttributeSet);
    public <init>(android.content.Context, android.util.AttributeSet, int);
}
#保持自定义控件类不被混淆
-keepclassmembers class * extends android.app.Activity {
    public void *(android.view.View);
}
-keepclassmembers enum * {     # 保持枚举 enum 类不被混淆
    public static **[] values();
    public static ** valueOf(java.lang.String);
}
-keep class * implements android.os.Parcelable { # 保持 Parcelable 不被混淆
    public static final android.os.Parcelable$Creator *;
}
-keep class * implements java.io.Serializable # 保持 Serializable 不被混淆
#保持 Serializable 不被混淆并且enum 类也不被混淆
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    !static !transient <fields>;
    !private <fields>;
    !private <methods>;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}
#不混淆资源类
-keepclassmembers class **.R$* {
    public static <fields>;
}
# 保持 native 方法不被混淆
-keepclasseswithmembernames class * {
    native <methods>;
}
#EventBus的注解
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
#WebView
-keepclassmembers class * extends android.webkit.WebView {*;}
-keepclassmembers class * extends android.webkit.WebViewClient {*;}
-keepclassmembers class * extends android.webkit.WebChromeClient {*;}
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

#-------------------------------------------greenDao-------------------------------------------------
-keep class **$Properties { *; }


# If you do NOT use SQLCipher:
-dontwarn net.sqlcipher.database.**
# If you do NOT use RxJava:
-dontwarn rx.**

#-------------------------------------------项目定义区-------------------------------------------------
#sqflite
-keep class com.tekartik.sqflite.** { *; }
-keep class com.pauldemarco.** {*;}
-keep class com.boskokg.** {*;}
-keep class com.wochuang.json.** {*;}
-keep class com.tvbus.engine.** {*;}
-keep class org.minidns.** {*;}
-keep class com.google.go.nativelib.** {*;}

-keep class rx.internal.** {*;}

-keep class go2.** { *; }



