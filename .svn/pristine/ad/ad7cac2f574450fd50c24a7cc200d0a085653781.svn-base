package com.google.chuangke.data

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.TagBean
import java.util.Calendar
import java.util.Date

class ClassicEpgViewModel(
    private val mClassicEpgRepository: ClassicEpgRepository,
    private val manageCustomTagRepository: ManageCustomTagRepository
) :
    BaseViewModel() {

    val classicEpgLiveData: MutableLiveData<MutableList<ChannelBean>> = MutableLiveData()
    val dayListLiveData: MutableLiveData<MutableList<Date>> = MutableLiveData()
    val tagLiveData: MutableLiveData<List<TagBean>> = MutableLiveData()

    init {
        getDays()
    }

    /**
     * 获取日期
     */
    private fun getDays() {
        val days = mutableListOf<Date>()
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        days.add(calendar.time)

        val afterDays = 3
        // 前三天
        for (i in 1..afterDays) {
            calendar.add(Calendar.DAY_OF_YEAR, 1)
            days.add(calendar.time)
        }

        // 后7天
        calendar.add(Calendar.DAY_OF_YEAR, -afterDays)
        for (i in 1..7) {
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            days.add(0, calendar.time)
        }
        dayListLiveData.postValue(days)
    }

    fun initTag() {
        launch {
            val tempTagList = mutableListOf<TagBean>()

            manageCustomTagRepository.getCustomTags().let {
                val customTags = it.map { customBean ->
                    TagBean(
                        customBean.id!!, customBean.name, 0, -1, custom = customBean.edit
                    )
                }
                tempTagList.addAll(customTags)
            }

            tempTagList.add(
                TagBean(
                    TAG_ALL, MyApplication.context.getString(R.string.txt_tag_all), 0, -1
                )
            )
            mClassicEpgRepository.getTagList().let {
                tempTagList.addAll(it)
            }

            tagLiveData.value = tempTagList
        }
    }

    fun fetchEpgList(tagBean: TagBean) {
        launch {
            mClassicEpgRepository.getChannelList(
                tagBean
            ).let { channels ->
                classicEpgLiveData.postValue(channels)
            }
        }
    }
}