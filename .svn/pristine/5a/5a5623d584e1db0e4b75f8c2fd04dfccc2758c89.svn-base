package com.google.chuangke.data.menu

import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class MenuSearchRepository(private val mDBApi: DBApi) {

    suspend fun getChannelListByKeyword(keyword: String): List<ChannelBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.getChannelListByKeyword(keyword) ?: mutableListOf()
        }
    }

}