package com.google.chuangke.common

object Constants {
    const val SP_KEY_TOKEN = "token"
    const val SP_KEY_IP = "ip"
    const val SP_KEY_USER_INFO = "userInfo"
    const val SP_KEY_LOCK = "lock"
    const val SP_KEY_FAQ = "faq"
    const val SP_KEY_ATTENTION = "attention"
    const val SP_KEY_CONTACT_US = "contactUs"
    const val SP_KEY_CONNECT_TYPE = "connectType"
    const val SP_KEY_NOTIFICATION = "notification"
    const val SP_KEY_RESOURCE_VERSION = "resource_version"
    const val SP_KEY_DMA = "dma"

    // 上一次播放的标签
//    const val SP_LAST_TIME_TAG = "lastTimeTag"
    const val SP_LAST_TIME_TAG = "lastTimeTagId"

    // 上一次播放的频道
//    const val SP_LAST_TIME_CHANNEL = "lastTimeChannel"
    const val SP_LAST_TIME_CHANNEL = "lastTimeChannelId"

    const val DATABASE_PATH = "data/data/com.huishine.traveler"

    const val DATABASE_OTHER = "other.db"
    const val DATABASE_VOD = "vod.db"
    const val DATABASE_DCHANNEL = "dchannel.db"
    const val DATABASE_EPG = "depg.db"

    const val DB_NAME_OTHER = "other"
    const val DB_NAME_EPG = "epg"

    const val OTHER_CHANNEL_TABLE_NAME = "channel"
    const val OTHER_TAG_TABLE_NAME = "tag"
    const val EPG_EPG_TABLE_NAME = "epg"

    const val CHANNEL_MIDDLE_MOVE_LEFT = "channel_middle_move_left"
    const val CHANNEL_MIDDLE_MOVE_RIGHT = "channel_middle_move_right"
    const val CHANNEL_MIDDLE_UP = "channel_middle_up"
    const val CHANNEL_RIGHT_LEFT = "channel_right_left"

    // ip地址
    const val IP_STRING =
        "https://pro.ip-api.com/json/?key=26pQNlE2x9UwB66&fields=status,country,countryCode,region,regionName,city,district,zip,lat,lon,timezone,currentTime,org,as,query,isp"

    // 换台等待时间
    const val NUMBER_CHANGE_CHANNEL_TIME = 3000L

    // 加锁频道
    const val FILTER_CONDITION = "\"p\":1"

    // 设置是否显示网速
    const val SETTING_SPEED_VISIBLE = "speed_visible"

    // 数据
    const val INTENT_DATA = "intent_data"

    // 外部存储自定义分类的文件名称
    const val FILE_NAME_CUSTOM_GROUP = "config.json"

    const val SP_KEY_FONT_SCALE = "fontScale"
}