package com.google.chuangke.page

import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import android.speech.SpeechRecognizer.RESULTS_RECOGNITION
import android.util.Log
import android.view.LayoutInflater
import android.widget.Toast
import androidx.viewbinding.ViewBinding
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.R
import com.google.chuangke.base.BaseActivity
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.http.HttpCallback
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.page.dialog.SpeechDialog
import com.orhanobut.logger.Logger
import kotlin.math.max

abstract class SpeechActivity<VB : ViewBinding>(inflate: (LayoutInflater) -> VB) :
    BaseActivity<VB>(inflate), RecognitionListener {

    private lateinit var speech: SpeechRecognizer
    private lateinit var recognizerIntent: Intent

    lateinit var dialog: SpeechDialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        dialog = SpeechDialog(
            mContext = this,
            onSpeechResultClick = ::onSpeechResultClick,
            startSpeech = ::startSpeech
        )
        initSpeechRecognizer()
        dialog.setOnDismissListener {
            speech.stopListening()
        }
    }

    abstract fun onSpeechResultClick(channelBean: ChannelBean)

    private fun initSpeechRecognizer() {
        speech = SpeechRecognizer.createSpeechRecognizer(this)
        Log.i("isRecognitionAvailable: ", "${SpeechRecognizer.isRecognitionAvailable(this)}")
        speech.setRecognitionListener(this)
        recognizerIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {

            // Given an hint to the recognizer about what the user is going to say
            putExtra(
                RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM
            )

            // Specify how many results you want to receive. The results will be sorted
            // where the first result is the one with   higher confidence.
            putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
        }
    }

    override fun onReadyForSpeech(params: Bundle?) {
        if (!dialog.isShowing) {
            dialog.show()
        }
        dialog.changeState(0)
        dialog.showState("Speaking now...")
        dialog.focusScale(true)
        Log.e("Speech123", "onReadyForSpeech")
    }

    override fun onBeginningOfSpeech() {
////        dialog.showState("Speaking now...")
        maxLevel = 0f
        Log.e("Speech123", "onBeginningOfSpeech")
        dialog.changeState(1)
    }

    override fun onRmsChanged(rmsdB: Float) {
        Log.e("Speech123", "onRmsChanged:$rmsdB")
        maxLevel = max(maxLevel, rmsdB)

    }

    var maxLevel = 0f

    override fun onBufferReceived(buffer: ByteArray?) {
////        dialog.showState("Searching...")
        Log.e("Speech123", "onBufferReceived")
//        dialog.changeState(3)
    }

    override fun onEndOfSpeech() {
////        dialog.showState("End Of Speech...")
        Log.e("Speech123", "onEndOfSpeech")
//        dialog.changeState(4)
    }

    override fun onError(error: Int) {
//        Log.e("Speech123", "onError")
        dialog.changeState(5)
        dialog.focusScale(false)

        dialog.showState(
            getString(
                if (maxLevel < 2)
                    R.string.speech_error_no_sound else R.string.speech_error_no_match
            )
        )

//        dialog.setResultList(mutableListOf())
    }

    /**
     * recognition results
     */
    override fun onResults(results: Bundle?) {
        Log.e("Speech123", "onResults")
        dialog.changeState(6)
        dialog.showState("Matched results")
        dialog.focusScale(false)
        val spokenList: ArrayList<String>? = results?.getStringArrayList(RESULTS_RECOGNITION)
        val sb = StringBuilder()
        spokenList?.let {
            if (it.isNotEmpty()) {
                it.forEach { str ->
                    sb.append(str)
                    sb.append(" \t ")
                }
            }
            Logger.d("SpeechActivity", sb.toString())

            dialog.showResult(sb.toString())
            // 搜索内容上传
            uploadSearch(sb.toString())
        }

        searchContent(spokenList)
    }

    override fun onPartialResults(partialResults: Bundle?) {
        Log.e("Speech123", "onPartialResults")
//        dialog.changeState(7)
    }

    override fun onEvent(eventType: Int, params: Bundle?) {
        Log.e("Speech123", "onEvent")
//        dialog.changeState(8)
    }

    var isListening = false

    override fun onRequestPermissionsResult(
        requestCode: Int, permissions: Array<String?>, grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        Config.getInstance().isRequestPermission = false

        when (requestCode) {
            REQUEST_RECORD_PERMISSION -> if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED && !dialog.isShowing) {
                startSpeech()
            } else {
                Toast.makeText(
                    this@SpeechActivity, getString(R.string.permission_denied), Toast.LENGTH_SHORT
                ).show()
            }

            REQUEST_READ_PERMISSION -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    DBApi.getInstance().initCustomTag()
                }
            }
        }
    }

    private fun startSpeech() {
        speech.startListening(recognizerIntent)
        isListening = true
    }

    /**
     * 检查是否可以测试登录
     */
    private fun uploadSearch(string: String) {
        Thread {
            val params = mutableMapOf<String, String>()
            params["voiceCommand"] = string
            HttpHelper.getInstance()
                .postApiWithoutEncrypt("saveVoiceCommand", params, object : HttpCallback() {
                    override fun onSuccess(jsonObject: JSONObject) {
                        super.onSuccess(jsonObject)
                        Logger.d("search upload :${jsonObject.getInteger("code")}")
                    }

                    override fun onError(err: String) {
                        Logger.d("search upload failed:$err")
                    }
                })
        }.start()
    }

    /**
     * 语音搜索
     */
    abstract fun searchContent(spokenList: ArrayList<String>?)

    companion object {
        const val REQUEST_RECORD_PERMISSION = 0x130
        const val REQUEST_READ_PERMISSION = 0x131
    }

}