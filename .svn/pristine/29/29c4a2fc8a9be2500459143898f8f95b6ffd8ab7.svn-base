<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/bg_content"
    android:layout_height="match_parent">

    <View
        android:id="@+id/view_activity_channel_search"
        android:layout_width="220dp"
        android:layout_height="0dp"
        android:background="@color/bg_content"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_activity_channel_search_back"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="22dp"
        android:background="@drawable/selector_white_light"
        android:contentDescription="@null"
        android:drawablePadding="8dp"
        android:focusable="true"
        android:gravity="center"
        android:paddingStart="8dp"
        android:paddingTop="4dp"
        android:paddingEnd="8dp"
        android:paddingBottom="4dp"
        android:scaleType="centerInside"
        android:text="Back"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:drawableLeftCompat="@mipmap/ic_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_activity_channel_search_key"
        android:layout_width="190dp"
        android:layout_height="31.5dp"
        android:layout_marginStart="15dp"
        android:layout_marginTop="75dp"
        android:background="@drawable/selector_input"
        android:focusable="false"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_activity_channel_search_input"
            style="@style/font_bold"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="15dp"
            android:layout_weight="1"
            android:background="@null"
            android:ellipsize="start"
            android:focusable="false"
            android:gravity="center_vertical"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textColorHint="@color/white_eee"
            android:textSize="13.5sp" />

        <TextView
            android:id="@+id/tv_activity_channel_search_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="11dp"
            android:layout_marginBottom="3dp"
            android:focusable="false"
            android:text="@string/menu_search_default_count"
            android:textColor="@color/white"
            android:textSize="10sp"
            tools:ignore="SmallSp" />
    </LinearLayout>

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_activity_channel_search_key"
        android:layout_width="220dp"
        android:layout_height="288dp"
        android:layout_marginTop="15dp"
        android:descendantFocusability="afterDescendants"
        android:paddingStart="15dp"
        android:paddingEnd="15dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ll_activity_channel_search_key" />

    <ImageView
        android:id="@+id/iv_activity_channel_search_delete"
        android:layout_width="38dp"
        android:layout_height="36dp"
        android:layout_marginStart="15dp"
        android:background="@drawable/selector_menu_search_btn"
        android:focusable="true"
        android:padding="12dp"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_vod_backspace"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_activity_channel_search_key" />

    <TextView
        android:id="@+id/tv_activity_channel_search_0"
        android:layout_width="38dp"
        android:layout_height="36dp"
        android:background="@drawable/selector_menu_search_btn"
        android:focusable="true"
        android:gravity="center"
        android:text="@string/menu_search_char_0"
        android:textColor="@color/white"
        android:textSize="13.5sp"
        app:layout_constraintStart_toEndOf="@+id/iv_activity_channel_search_delete"
        app:layout_constraintTop_toBottomOf="@+id/rv_activity_channel_search_key" />

    <TextView
        android:id="@+id/tv_activity_channel_search_dot"
        android:layout_width="38dp"
        android:layout_height="36dp"
        android:background="@drawable/selector_menu_search_btn"
        android:focusable="true"
        android:gravity="center"
        android:paddingBottom="6dp"
        android:text="@string/txt_vod_dot"
        android:textColor="@color/white"
        android:textSize="13.5sp"
        app:layout_constraintStart_toEndOf="@+id/tv_activity_channel_search_0"
        app:layout_constraintTop_toBottomOf="@+id/rv_activity_channel_search_key" />

    <TextView
        android:id="@+id/tv_activity_channel_search_space"
        android:layout_width="38dp"
        android:layout_height="36dp"
        android:background="@drawable/selector_menu_search_btn"
        android:focusable="true"
        android:gravity="center"
        android:paddingTop="6dp"
        android:text="@string/txt_vod_space"
        android:textColor="@color/white"
        android:textSize="10sp"
        app:layout_constraintStart_toEndOf="@+id/tv_activity_channel_search_dot"
        app:layout_constraintTop_toBottomOf="@+id/rv_activity_channel_search_key" />

    <ImageView
        android:id="@+id/iv_activity_channel_search_clear"
        android:layout_width="38dp"
        android:layout_height="36dp"
        android:background="@drawable/selector_menu_search_btn"
        android:contentDescription="@null"
        android:focusable="true"
        android:padding="12dp"
        android:scaleType="fitXY"
        android:src="@mipmap/ic_vod_clear"
        app:layout_constraintStart_toEndOf="@+id/tv_activity_channel_search_space"
        app:layout_constraintTop_toBottomOf="@+id/rv_activity_channel_search_key" />

    <LinearLayout
        android:id="@+id/ll_activity_channel_search_result"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:orientation="horizontal"
        app:layout_constraintStart_toEndOf="@+id/view_activity_channel_search"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:layout_marginTop="16dp"
            android:focusable="false"
            android:textColor="#A99AAD"
            android:textSize="12sp"
            tools:text="@string/menu_search_result" />

        <TextView
            android:id="@+id/tv_activity_channel_search_result_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:focusable="false"
            android:textColor="#A99AAD"
            android:textSize="12sp"
            tools:text="101" />

    </LinearLayout>

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_activity_channel_search_result"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="16.5dp"
        android:descendantFocusability="afterDescendants"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/view_activity_channel_search"
        app:layout_constraintTop_toBottomOf="@+id/ll_activity_channel_search_result" />

</androidx.constraintlayout.widget.ConstraintLayout>