package com.google.chuangke.page.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter

class CenterOptionPresenter : BasePresenter<String>() {

    init {
        needKeep = true
    }

    override fun layoutId(): Int {
        return R.layout.item_fragment_center_option
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf(R.id.tv_item_fragment_center_option)
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): <PERSON><PERSON>an {
        return false
    }

    override fun bindViewHolder(view: View, item: String) {
        (view as TextView).text = item
    }

}