package com.google.chuangke.page.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.VerticalGridView
import com.google.chuangke.R
import com.google.chuangke.base.DisableSpeechDialog
import com.google.chuangke.common.Constants
import com.google.chuangke.common.UnitCallback
import com.google.chuangke.ext.toast
import com.google.chuangke.page.presenter.SearchKeyPresenter
import com.google.chuangke.util.SPUtils

class UnlockDialog(
    activity: Activity,
    themeId: Int,
    var callback: UnitCallback
) : DisableSpeechDialog(activity, themeId) {
    private lateinit var mEtPassword: TextView
    private lateinit var mRvKey: VerticalGridView
    private lateinit var mKeyAdapter: ArrayObjectAdapter
    private var inputText: StringBuilder = StringBuilder("")

    private val chars: MutableList<Char> = mutableListOf()

    init {
        for (j in 0..9) {
            chars.add(Char(48 + j))
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_lock)
        initView()
        val window: Window? = this.window
        if (window != null) {
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
            )
            window.setDimAmount(0f)
        }
        initListener()
    }

    private fun initView() {
        mEtPassword = findViewById(R.id.et_dialog_lock_password)
        mRvKey = findViewById(R.id.vg_key)
        mRvKey.setNumColumns(5)
        mRvKey.adapter = ItemBridgeAdapter(ArrayObjectAdapter(SearchKeyPresenter()).also {
            mKeyAdapter = it
        })

        mKeyAdapter.setItems(chars, null)

    }

    @SuppressLint("SetTextI18n")
    private fun initListener() {
        mRvKey.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    unlock(mKeyAdapter.get(mRvKey.selectedPosition).toString())
                    return@setOnKeyInterceptListener true
                }

                in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                    unlock((event.keyCode - 7).toString())
                    return@setOnKeyInterceptListener true
                }

                KeyEvent.KEYCODE_DEL -> {
                    clearInput()
                    return@setOnKeyInterceptListener true
                }
            }

            return@setOnKeyInterceptListener false
        }
    }

    private fun unlock(number: String) {
        if (inputText.length < 4) {
            inputText.append(number)
            var star = ""
            inputText.length.downTo(1).forEach { _ ->
                star = "$star*"
            }
            mEtPassword.text = star

            // 第四位判断
            if (inputText.length == 4) {
                val password = inputText.toString()
                val lockNum =
                    SPUtils.getString(context, Constants.SP_KEY_LOCK, "0000")
                if (password == lockNum) {
                    callback.invoke()
                    dismiss()
                } else {
                    context.toast(
                        context.getText(R.string.menu_lock_authentication_failed)
                            .toString()
                    )
                }

                // 清空
                clearInput()
            }
        }
    }

    /**
     * 关闭对话的时候把输入框进行重置
     */
    @SuppressLint("SetTextI18n")
    override fun dismiss() {
        clearInput()
        super.dismiss()
    }

    private fun clearInput() {
        inputText.clear()
        mEtPassword.text = ""
    }

    /**
     * 解锁
     */
    fun showDialog() {
        show()
        mRvKey.requestFocus()
    }

}

