package com.google.chuangke.page.dialog

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.provider.Settings.ACTION_CAPTIONING_SETTINGS
import android.view.*
import android.widget.TextView
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.Presenter
import androidx.leanback.widget.VerticalGridView
import com.google.chuangke.R
import com.google.chuangke.base.BaseSpeechDialog
import com.google.chuangke.page.dialog.SettingType.*
import com.google.chuangke.player.PlayerHelper

/**
 * 设置界面
 */
class SettingDialog(var ct: Context) : BaseSpeechDialog(ct) {

    private lateinit var mRecyclerView: VerticalGridView
    private lateinit var mAdapter: ArrayObjectAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window!!.requestFeature(Window.FEATURE_NO_TITLE)
        val view: View = LayoutInflater.from(ct).inflate(R.layout.dialog_setting, null)
        setContentView(view)
        window!!.setBackgroundDrawable(ColorDrawable(0x00000000))
        window!!.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
        )

        initView(view)
        initData()
    }

    private fun initData() {
        val list = mutableListOf(SPEED_VISIBLE)
        mAdapter.setItems(list, null)
    }

    private fun initView(view: View) {
        mRecyclerView = view.findViewById(R.id.rv_dialog_setting)
        mRecyclerView.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(SettingDialogPresenter(ct)).also {
                mAdapter = it
            })
    }

    override fun dismiss() {
        PlayerHelper.getInstance().setSystemCaptionStyle()
        super.dismiss()
    }
}

enum class SettingType {
    SPEED_VISIBLE
}

class SettingDialogPresenter(var context: Context) : Presenter() {

    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        val view = LayoutInflater.from(context).inflate(R.layout.item_dialog_setting, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, item: Any) {
        val textView = viewHolder.view as TextView
        when (item as SettingType) {
            SPEED_VISIBLE -> {
                textView.text = context.getString(R.string.setting_captions_style)
            }
        }

        textView.setOnClickListener {
            val intent = Intent(ACTION_CAPTIONING_SETTINGS)
            val packageManager = context.packageManager
            if (packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY).size > 0) {
                (context as Activity).startActivity(intent)
            }
        }
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder) {

    }

}