package com.google.chuangke.util

import android.app.Activity
import android.content.Context
import android.content.SharedPreferences
import android.util.Base64
import java.io.*
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method

class SPUtils private constructor() {

    /**
     * 创建一个解决SharedPreferencesCompat.apply方法的一个兼容类
     *
     * <AUTHOR>
     */
    private object SharedPreferencesCompat {

        private val sApplyMethod = findApplyMethod()

        /**
         * 反射查找apply的方法
         *
         * @return
         */
        private fun findApplyMethod(): Method? {
            try {
                val clz: Class<*> = SharedPreferences.Editor::class.java
                return clz.getMethod("apply")
            } catch (e: NoSuchMethodException) {
            }
            return null
        }

        /**
         * 如果找到则使用apply执行，否则使用commit
         *
         * @param editor
         */
        fun apply(editor: SharedPreferences.Editor) {
            try {
                if (sApplyMethod != null) {
                    sApplyMethod.invoke(editor)
                    return
                }
            } catch (e: IllegalArgumentException) {
            } catch (e: IllegalAccessException) {
            } catch (e: InvocationTargetException) {
            }
            editor.commit()
        }
    }

    companion object {

        /**
         * 保存在手机里面的文件名
         */
        private const val FILE_NAME = "traveler_cfg_data"

        /**
         * 保存数据的方法，我们需要拿到保存数据的具体类型，然后根据类型调用不同的保存方法
         *
         * @param context
         * @param key
         * @param obj
         */
        fun put(context: Context, key: String?, obj: Any) {
            //根据默认值类型判断转换类型，坑
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            val editor = sp.edit()
            when (obj) {
                is String -> {
                    editor.putString(key, obj)
                }
                is Int -> {
                    editor.putInt(key, obj)
                }
                is Boolean -> {
                    editor.putBoolean(key, obj)
                }
                is Float -> {
                    editor.putFloat(key, obj)
                }
                is Long -> {
                    editor.putLong(key, obj)
                }
                else -> {
                    editor.putString(key, obj.toString())
                }
            }
            SharedPreferencesCompat.apply(editor)
        }

        /**
         * 得到保存数据的方法，我们根据默认值得到保存的数据的具体类型，然后调用相对于的方法获取值
         *
         * @param context
         * @param key
         * @param defaultObject
         * @return
         */
        fun getObject(context: Context, key: String?, defaultObject: Any?): Any? {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            when (defaultObject) {
                is String -> {
                    return sp.getString(key, defaultObject as String?)
                }
                is Int -> {
                    return sp.getInt(key, (defaultObject as Int?)!!)
                }
                is Boolean -> {
                    return sp.getBoolean(key, (defaultObject as Boolean?)!!)
                }
                is Float -> {
                    return sp.getFloat(key, (defaultObject as Float?)!!)
                }
                is Long -> {
                    return sp.getLong(key, (defaultObject as Long?)!!)
                }
                else -> return null
            }
        }

        /**
         * 移除某个key值已经对应的值
         *
         * @param context
         * @param key
         */
        fun remove(context: Context, key: String?) {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            val editor = sp.edit()
            editor.remove(key)
            SharedPreferencesCompat.apply(editor)
        }
        /**
         * 移除某个key值已经对应的值(同步)
         *
         * @param context
         * @param key
         */
        fun removeSync(context: Context, key: String?):Boolean {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            val editor = sp.edit()
            editor.remove(key)
            return editor.commit()
        }

        /**
         * 清除所有数据
         *
         * @param context
         */
        fun clear(context: Context) {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            val editor = sp.edit()
            editor.clear()
            SharedPreferencesCompat.apply(editor)
        }

        fun clearSync(context: Context):Boolean {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            val editor = sp.edit()
            editor.clear()
            return editor.commit()
        }

        /**
         * 查询某个key是否已经存在
         *
         * @param context
         * @param key
         * @return
         */
        fun contains(context: Context, key: String?): Boolean {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            return sp.contains(key)
        }

        /**
         * 返回所有的键值对
         *
         * @param context
         * @return
         */
        fun getAll(context: Context): Map<String, *> {
            val sp = context.getSharedPreferences(
                FILE_NAME,
                Context.MODE_PRIVATE
            )
            return sp.all
        }

        /**
         * 针对复杂类型存储<对象>
         *
         * @param context
         * @param key
         * @param obj
         */
        fun putObject(context: Context, key: String?, obj: Any?) {
            val sp = context.getSharedPreferences(FILE_NAME, Context.MODE_PRIVATE)
            val byteOutStream = ByteArrayOutputStream()
            var out: ObjectOutputStream? = null
            try {
                out = ObjectOutputStream(byteOutStream)
                out.writeObject(obj)
                val objectVal = String(Base64.encode(byteOutStream.toByteArray(), Base64.DEFAULT))
                val editor = sp.edit()
                editor.putString(key, objectVal)
                SharedPreferencesCompat.apply(editor)
            } catch (e: IOException) {
                e.printStackTrace()
            } finally {
                try {
                    byteOutStream.close()
                    out?.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
        }

        fun getObject(context: Context, key: String?): Any? {
            val sp = context.getSharedPreferences(FILE_NAME, Context.MODE_PRIVATE)
            if (sp.contains(key)) {
                val objectVal = sp.getString(key, null)
                val buffer = Base64.decode(objectVal, Base64.DEFAULT)
                val byteInputStream = ByteArrayInputStream(buffer)
                var ois: ObjectInputStream? = null
                try {
                    ois = ObjectInputStream(byteInputStream)
                    return ois.readObject()
                } catch (e: StreamCorruptedException) {
                    e.printStackTrace()
                } catch (e: IOException) {
                    e.printStackTrace()
                } catch (e: ClassNotFoundException) {
                    e.printStackTrace()
                } finally {
                    try {
                        byteInputStream.close()
                        ois?.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
            return null
        }

        fun putString(context: Context, key: String?, value: String?): Boolean {
            val sp = context.getSharedPreferences(FILE_NAME, Activity.MODE_PRIVATE)
            val editor = sp.edit()
            editor.putString(key, value)
            return editor.commit()
        }

        fun getString(context: Context, key: String?, defaultValue: String?): String? {
            val sp = context.getSharedPreferences(FILE_NAME, Activity.MODE_PRIVATE)
            return sp.getString(key, defaultValue)
        }

    }

}