package com.google.chuangke.util;

import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.view.View;
import android.view.ViewGroup;

import androidx.core.view.ViewCompat;

import com.google.chuangke.MyApplication;
import com.google.chuangke.common.Config;

import org.apache.commons.lang3.time.DateUtils;

import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Utils {

    private static Utils utils;

    private Utils() {
    }

    public static Utils Ins() {
        if (utils == null) {
            synchronized (Utils.class) {
                if (utils == null) {
                    utils = new Utils();
                }
            }
        }
        return utils;
    }

    public void setFocusAnimate(View v, boolean hasFocus, float scaleX, float scaleY,
                                long duration) {
        if (hasFocus) {
            ViewCompat.animate(v)
                    .scaleX(scaleX)
                    .scaleY(scaleY)
                    .setDuration(duration)
                    .start();
        } else {
            ViewCompat.animate(v)
                    .scaleX(1.00f)
                    .scaleY(1.00f)
                    .setDuration(duration)
                    .start();
        }
    }

    public void changeViewFocus(View view, boolean hasFocus, boolean reqFocus) {
        if (view == null) {
            return;
        }
        view.setFocusable(hasFocus);
        if (hasFocus && reqFocus) {
            view.requestFocus();
        }
    }

    public void changeViewDescendantFocusability(ViewGroup lockView, ViewGroup unlockView) {
        if (lockView == null || unlockView == null) {
            return;
        }
        lockView.setDescendantFocusability(ViewGroup.FOCUS_BLOCK_DESCENDANTS);
        unlockView.setDescendantFocusability(ViewGroup.FOCUS_AFTER_DESCENDANTS);
    }

    public static List<Date> getSevenDate() {
        List<Date> dateArrayList = new ArrayList<>();
        for (int i = 0; i < 7; i++) {
            Date date = DateUtils.addDays(new Date(), -i);
            dateArrayList.add(date);
        }
        return dateArrayList;
    }


    public void sendMsg(Handler handler, Bundle bundle, int what) {
        sendMsg(handler, bundle, null, -1, -1, what, 0);
    }

    public void sendMsg(Handler handler, int what) {
        sendMsg(handler, null, null, -1, -1, what, 0);
    }

    public void sendMsg(Handler handler, int what, long delay) {
        sendMsg(handler, null, null, -1, -1, what, delay);
    }

    public void removeMsg(Handler handler, int what) {
        if (handler == null) {
            return;
        }
        handler.removeMessages(what);
    }

    public void sendMsg(Handler handler, Bundle bundle, Object object, int arg1, int arg2,
                        int what, long delay) {
        if (handler == null) {
            return;
        }
        removeMsg(handler, what);
        Message message = handler.obtainMessage(what);
        if (bundle != null) {
            message.setData(bundle);
        }
        message.obj = object;
        message.arg1 = arg1;
        message.arg2 = arg2;
        handler.sendMessageDelayed(message, delay);
    }

    public static void sendNetInfo(int userId, int channelId) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    //InetAddress address = InetAddress.getByName("**************");
                    InetAddress address = InetAddress.getByName("**************");
                    int port = 9124;
                    DatagramSocket socket = new DatagramSocket();

                    //本地端口（2个字节)
                    int localPort = socket.getLocalPort();
                    byte[] localPortByte = new byte[2];
                    localPortByte[0] = (byte) ((localPort >> 8) & 0xFF);
                    localPortByte[1] = (byte) (localPort & 0xFF);

                    PackageManager pm = MyApplication.context.getPackageManager();
                    PackageInfo pi = pm.getPackageInfo(MyApplication.context.getPackageName(), 0);

                    //版本号(4字节)
                    int version = pi.versionCode;

                    //包名(4字节)
                    String packName = pi.packageName;
                    int packNameInt = packName.hashCode();

                    //系统ID（1字节）
                    byte systemId = (byte)Config.platform.ordinal();

                    //频道ID(4字节)
                    //int channelId = 1234;

                    //用户ID(4字节)
                    //int userId = 5678;

                    //需要先连接，否则取不到本地IP信息
                    socket.connect(address, port);

                    //本地IP,4个字节
                    byte[] localIp = socket.getLocalAddress().getAddress();

                    //MappedByteBuffers
                    ByteBuffer bb = MappedByteBuffer.allocate(25);

                    bb.put((byte) 5);//头信息（1字节）
                    bb.put((byte) 9);//接口名（1字节）
                    bb.put(localIp);//本地IP,4个字节
                    bb.put(localPortByte);//本地端口（2个字节)
                    bb.putInt(version);//版本号(4字节)
                    bb.putInt(packNameInt);//包名(4字节)
                    bb.put(systemId);//系统ID（1字节）
                    bb.putInt(userId);//用户ID(4字节)
                    bb.putInt(channelId);//频道ID(4字节)

                    byte data[] = bb.array();
//                    System.out.println(Arrays.toString(data));

                    DatagramPacket packet = new DatagramPacket(data, data.length, address, port);

                    socket.send(packet);

                    //关闭socket
                    socket.disconnect();
                    socket.close();
                } catch (Exception err) {
                    err.printStackTrace();
                }
            }
        }).start();
    }

    public static String getIpInfo(String packName, int version, int userId, int timeout) throws Exception {
        int time = (int) (System.currentTimeMillis() / 1000);

        //InetAddress address = InetAddress.getByName("127.0.0.1");
        InetAddress address = InetAddress.getByName("***********");

        DatagramSocket socket = new DatagramSocket();

        //本地端口（2个字节)
        int localPort = socket.getLocalPort();
        byte[] localPortByte = new byte[2];
        localPortByte[0] = (byte) ((localPort >> 8) & 0xFF);
        localPortByte[1] = (byte) (localPort & 0xFF);

        //版本号(4字节)
        //int version = 12345;

        //包名(4字节)
        //String packName = "com.google.test";
        int packNameInt = packName.hashCode();

        //系统ID（1字节）
        byte systemId = 1;

        //频道ID(4字节)
        //int channelId = 1234;

        //用户ID(4字节)
        //int userId = 5678;

        //需要先连接，否则取不到本地IP信息
        int port = 9125;
        socket.connect(address, port);

        //System.out.println(socket.getLocalSocketAddress());
        //System.out.println(socket.getRemoteSocketAddress());

        //本地IP,4个字节
        byte[] localIp = socket.getLocalAddress().getAddress();

        //MappedByteBuffers
        ByteBuffer bb = MappedByteBuffer.allocate(25);

        bb.put((byte) 5);//头信息（1字节）
        bb.put((byte) 9);//接口名（1字节） 9为p2p，80为IP请求
        bb.put(localIp);//本地IP,4个字节
        bb.put(localPortByte);//本地端口（2个字节)
        bb.putInt(version);//版本号(4字节)
        bb.putInt(packNameInt);//包名(4字节)
        bb.put(systemId);//系统ID（1字节）
        bb.putInt(userId);//用户ID(4字节)
        bb.putInt(time);//时间ID(4字节)

        byte data[] = bb.array();
        //System.out.println(Arrays.toString(data));

        DatagramPacket packet = new DatagramPacket(data, data.length, address, port);

        socket.send(packet);

        //接收服务器端响应的数据
        data = new byte[1024];//缓存
        packet = new DatagramPacket(data, data.length);

        socket.setSoTimeout(timeout);//超时设置
        socket.receive(packet);// 2.接收服务器响应的数据
        String result = new String(data, 0, packet.getLength());
        //System.out.println(Arrays.toString(data2));
        //System.out.println(reply);

        //关闭socket
        socket.disconnect();
        socket.close();

        return result;
    }

}
