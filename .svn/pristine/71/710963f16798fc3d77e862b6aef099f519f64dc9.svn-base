package com.google.chuangke.common

import android.app.Activity
import android.app.AlertDialog
import android.app.Dialog
import android.app.ProgressDialog
import android.content.Intent
import android.content.pm.PackageInfo
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.core.content.FileProvider
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.http.ProgressResponseBody
import com.google.chuangke.page.LoadInfo
import com.google.chuangke.page.SplashLoadingEvent
import com.google.chuangke.util.FileUtil
import com.google.chuangke.util.settingAlertDialogButtonStyle
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.Response
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.io.IOException
import java.text.DecimalFormat

class UpgradeHelper(val activity: Activity) {
    /**
     * 检查版本
     */
    fun checkUpdate(showTips: Boolean) {
        GlobalThreadPools.getInstance().execute {
            val url: String = if (Config.platform == Config.Platform.TEST) {
                Config.getInstance().baseUrl + "app/liveultra.json"
            } else {
                Config.getInstance().staticUrl + "app/liveultra.json"
            }

            HttpHelper.getInstance().get(url, object : Callback {
                override fun onResponse(call: Call, response: Response) {
                    try {
                        val result = response.body?.string()
                        val jsonObject = JSONObject.parseObject(result)
                        val localPackageName = MyApplication.context.packageName
                        val serverPackageName = jsonObject.getString("pn")
                        if (localPackageName != serverPackageName) {
                            noNewVersion()
                            return
                        }
                        val pi: PackageInfo = activity.packageManager.getPackageInfo(activity.packageName, 0)
                        val localVerCode = pi.versionCode
                        val serverVerCode = jsonObject.getInteger("VersionCode")
                        if (serverVerCode <= localVerCode) {
                            noNewVersion()
                            return
                        }
                        showNewVersion(jsonObject)
                    } catch (e: Exception) {
                        noNewVersion()
                    }
                }

                override fun onFailure(call: Call, e: IOException) {
                    noNewVersion()
                }

                fun noNewVersion() {
                    if (showTips) {
                        Handler(Looper.getMainLooper()).post {
                            Toast.makeText(activity, "No new version", Toast.LENGTH_SHORT).show()
                        }
                    }
                    EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.CHECK_DEVICE_AUTH))
                }

                fun showNewVersion(jsonObject: JSONObject) {
                    activity.runOnUiThread { showUpdateDialog(jsonObject) }
                }
            })
        }
    }

    /**
     * 显示更新对话框
     */
    private fun showUpdateDialog(jsonObject: JSONObject) {
        val mAlertDialogBuilder =
            AlertDialog.Builder(activity, R.style.XUpdate_DialogTheme)
                .setTitle(
                    String.format(
                        activity.getString(R.string.upgrade_title),
                        jsonObject.getString("VersionName")
                    )
                )
                .setMessage(jsonObject.getString("ModifyContent"))
                .setPositiveButton(R.string.upgrade_positive_button, null)
                .setCancelable(false)

        val forceUpdate = jsonObject.getBoolean("ForceUpdate")
        if (forceUpdate == null || !forceUpdate) {
            mAlertDialogBuilder.setNegativeButton(R.string.upgrade_negative_button, null).setCancelable(false)
        }
        val mAlertDialog = mAlertDialogBuilder.create()
        mAlertDialog.show()

        val positiveButton = mAlertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
        val negativeButton = mAlertDialog.getButton(AlertDialog.BUTTON_NEGATIVE)
        settingAlertDialogButtonStyle(activity, positiveButton)
        settingAlertDialogButtonStyle(activity, negativeButton)
        positiveButton.requestFocus()
        positiveButton.setOnClickListener { //先检查权限
            checkPermission(mAlertDialog, jsonObject)
        }
        negativeButton.setOnClickListener {
            mAlertDialog.dismiss()
            EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.CHECK_DEVICE_AUTH))
        }
    }

    /**
     * 检查权限
     */
    private fun checkPermission(dialog: Dialog, updateRootBean: JSONObject) {
        val downloadUrl = updateRootBean.getString("DownloadUrl")
        val apkName: String = downloadUrl.substring(downloadUrl.lastIndexOf('/'), downloadUrl.length)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val hasInstallPermission = activity.applicationContext.packageManager.canRequestPackageInstalls()
            if (!hasInstallPermission) {
                startInstallPermissionSettingActivity()
            } else {
                showDownLoadDialog(apkName, downloadUrl)
                dialog.dismiss()
            }
        } else {
            showDownLoadDialog(apkName, downloadUrl)
            dialog.dismiss()
        }
    }

    /**
     * 跳转到设置-允许安装未知来源-页面
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    private fun startInstallPermissionSettingActivity() {
        val intent =
            Intent(
                Settings.ACTION_MANAGE_UNKNOWN_APP_SOURCES,
                Uri.parse("package:" + activity.applicationContext.packageName)
            )
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        activity.startActivity(intent)
    }


    /**
     * 显示下载对话框
     */
    private fun showDownLoadDialog(fileName: String, url: String) {
        val progressDialog = ProgressDialog(activity, R.style.XUpdate_DialogTheme)
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL)
        progressDialog.setCancelable(false)
        progressDialog.setMessage(activity.getString(R.string.upgrade_download_progress))
        progressDialog.show()

        val filePath = activity.cacheDir.toString() + File.separator + fileName
        var downloadUrl = url
        if (!url.startsWith("http:") && !url.startsWith("https:")) {
            downloadUrl = Config.getInstance().baseUrl + url
        }
        val request: Request = Request.Builder()
            .url(downloadUrl)
            .get()
            .build()

        val interceptor = Interceptor { chain ->
            val originalResponse = chain.proceed(chain.request())
            originalResponse.newBuilder()
                .body(ProgressResponseBody(originalResponse.body) { bytesRead, contentLength, done ->
                    progressDialog.max = contentLength.toInt()
                    progressDialog.progress = bytesRead.toInt()
                    progressDialog.setProgressNumberFormat(
                        info(bytesRead.toDouble(), contentLength.toDouble())
                    )
                    if (done) {
                        installApk(File(filePath))
                        progressDialog.dismiss()
//                        EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.CHECK_DEVICE_AUTH))
                    }
                })
                .build()
        }
        val client = HttpHelper.getInstance().client.newBuilder()
            .addNetworkInterceptor(interceptor)
            .build()

        client.newCall(request)
            .enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    progressDialog.dismiss()
                }

                override fun onResponse(call: Call, response: Response) {
                    val inputStream = response.body!!.byteStream()

                    FileUtil.writeFile4InputStream(inputStream, filePath)
                }
            })
    }

    /**
     * 展示进度信息
     */
    private fun info(progress: Double, total: Double): String {
        val mb = 1024 * 1024
        val format = DecimalFormat("#.##")
        val sProgress = format.format(progress / mb) + "M"
        val sTotal = format.format(total / mb) + "M"
        return "$sProgress/$sTotal"
    }

    private fun installApk(file: File) {
        try {
            val openFile = getFileIntent(file)
            activity.startActivity(openFile)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getFileIntent(file: File): Intent {
        val intent = Intent("android.intent.action.VIEW")
        if (Build.VERSION.SDK_INT >= 24) {
            val uri =
                FileProvider.getUriForFile(activity, activity.applicationContext.packageName + ".fileProvider", file)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            intent.addCategory("android.intent.category.DEFAULT")
            intent.setDataAndType(uri, getMIMEType(file))
        } else {
            val uri = Uri.fromFile(file)
            val type = getMIMEType(file)
            intent.addCategory("android.intent.category.DEFAULT")
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.setDataAndType(uri, type)
        }
        return intent
    }

    private fun getMIMEType(f: File): String {
        val type: String
        val fName = f.name
        // 取得扩展名
        val end = fName
            .substring(fName.lastIndexOf(".") + 1)
        type = if (end == "apk") {
            "application/vnd.android.package-archive"
        } else {
            // /*如果无法直接打开，就跳出软件列表给用户选择 */
            "*/*"
        }
        return type
    }
}