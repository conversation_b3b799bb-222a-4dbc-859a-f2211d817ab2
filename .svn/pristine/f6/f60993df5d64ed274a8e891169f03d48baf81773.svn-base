package com.google.chuangke.data

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelLockBean
import com.google.chuangke.entity.CustomTagBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.ext.containsAnyOfIgnoreCase
import com.google.chuangke.util.FileHelper
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.flow
import java.util.*

const val TAG_SEARCH = -1L
const val TAG_ALL = -2L
const val TAG_HISTORY = -3L
const val TAG_FAVORITE = -4L
const val TAG_ADD = -5L
const val TEXT_FAVORITE_LIST = "Favorite List"

class ChannelViewModel(
    private val mChannelRepository: ChannelRepository,
    private val mEpgRepository: EpgRepository,
    private val manageCustomTagRepository: ManageCustomTagRepository
) : BaseViewModel() {

    val tagLiveData: MutableLiveData<List<TagBean>> = MutableLiveData()
    val channelLiveData: MutableLiveData<MutableList<ChannelBean>> = MutableLiveData()
    val epgLiveData: MutableLiveData<List<EpgBean>> = MutableLiveData()
    val dateLiveData: MutableLiveData<List<Date>> = MutableLiveData()

    var tempChannelBean: ChannelBean? = null

    val defaultAndCustomTags = mutableListOf<TagBean>()
    val normalTags = mutableListOf<TagBean>()

    val cursorPositionFlow = MutableSharedFlow<Int>(replay = 1)

    init {
        launch {
            cursorPositionFlow
                .debounce(300)
                .collectLatest {
                    getChannelList(it)
                }
        }
        getSevenDate()
        initData()
    }

    fun initData() {
        launch {

            defaultAndCustomTags.clear()
            normalTags.clear()

            defaultAndCustomTags.addAll(generateDefaultTag())

            manageCustomTagRepository.getCustomTags().let {
                val customTags = it.map { customBean ->
                    TagBean(
                        customBean.id!!, customBean.name, 0, -1, custom = customBean.edit
                    )
                }
                defaultAndCustomTags.addAll(customTags)
            }

            mChannelRepository.getTagList().let {
                // 读取保存好的排序文件
                val tagsString = FileHelper.readTagSortFromFile(MyApplication.context)
                normalTags.addAll(
                    if (!tagsString.isNullOrEmpty()) {
                        sortList(it, tagsString.split(","))
                    } else {
                        it
                    }
                )

            }

            tagLiveData.value = defaultAndCustomTags + normalTags
        }
    }

    private val getCustomTagFlow: Flow<List<CustomTagBean>> = flow {
        val list = manageCustomTagRepository.getCustomTags()
        emit(list)
    }

    private fun generateDefaultTag(): MutableList<TagBean> {
        val tempTagList = mutableListOf<TagBean>()
        tempTagList.add(
            TagBean(
                TAG_SEARCH,
                MyApplication.context.getString(R.string.txt_tag_search),
                0,
                -1,
                custom = -1
            )
        )
        if (DBApi.getInstance().getCustomTags().size < 3) {
            tempTagList.add(
                TagBean(
                    TAG_ADD,
                    MyApplication.context.getString(R.string.txt_tag_add),
                    0,
                    -1,
                    custom = -1
                )
            )
        }

        tempTagList.add(
            TagBean(
                TAG_ALL, MyApplication.context.getString(R.string.txt_tag_all), 0, -1, custom = -1
            )
        )
        tempTagList.add(
            TagBean(
                TAG_HISTORY,
                MyApplication.context.getString(R.string.txt_tag_history),
                0,
                -1,
                custom = -1
            )
        )
//        tempTagList.add(
//            TagBean(
//                TAG_FAVORITE, MyApplication.context.getString(R.string.txt_tag_favorite_list), 0, -1
//            )
//        )
        return tempTagList
    }

    /**
     * 获取当前后七天的日期
     */
    fun getSevenDate() {
        launch {
            mChannelRepository.getSevenDate().let {
                dateLiveData.postValue(it)
            }
        }
    }

    /**
     * 获取分类下的频道
     */
    fun getChannelList(position: Int) {
        tagLiveData.value?.let { tagList ->
            val tagBean = tagList[position]
            launch {
                if ((tagBean.custom ?: 0) > 0) {
                    mChannelRepository.getChannelListByCustomTag(tagBean.id).let {
                        if (tagBean.name.equals(TEXT_FAVORITE_LIST)) {
                            val dmaChannels = DBApi.getInstance().dmaChannel().map {
                                it.local = true
                                it
                            }
                            it.addAll(dmaChannels)
                            it.sortBy { !it.local }
                            val list = it.distinctBy { it.channelNumber }.toMutableList()
                            channelLiveData.postValue(list)
                        } else {
                            channelLiveData.postValue(it)
                        }
                    }
                } else {
                    mChannelRepository.getChannelList(tagBean).let {
                        channelLiveData.postValue(it)
                    }
                }
            }
        }
    }

    /**
     * 获取EPG栏目
     */
    fun getEpgListByDay(tagBean: TagBean, channelBean: ChannelBean, date: Date) {
        tempChannelBean = channelBean
        launch {
            // 特殊频道要隐藏日期切换
            if (tagBean.name?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
                mEpgRepository.getEspecialEpgListByDay(channelBean, date).let {
                    epgLiveData.postValue(it)
                }
            } else {
                mEpgRepository.getEpgListByDay(channelBean, date).let {
                    epgLiveData.postValue(it)
                }
            }
        }
    }

    /**
     * 获取EPG栏目
     */
    fun getEpgListByDay(date: Date) {
        launch {
            mEpgRepository.getEpgListByDay(tempChannelBean!!, date).let {
                epgLiveData.postValue(it)
            }
        }
    }

    /**
     * 加锁
     */
    fun saveLockedChannel(channelId: Long) {
        launch {
            val channelLockBean = ChannelLockBean()
            channelLockBean.unid = channelId
            channelLockBean.createTime = (System.currentTimeMillis() / 1000).toInt()
            mChannelRepository.saveLockedChannel(channelLockBean)
        }
    }


    fun topCategory(tagBean: TagBean) {
        val position = normalTags.indexOfFirst { it.name == tagBean.name }
        val targetTagBean = normalTags.removeAt(position)
        normalTags.add(0, targetTagBean)
        FileHelper.writTagSortToFile(
            MyApplication.context,
            normalTags.joinToString(",") { it.name!! })
        tagLiveData.value = defaultAndCustomTags + normalTags
    }
}

internal fun sortList(list: List<TagBean>, order: List<String>): List<TagBean> {
    // 创建顺序映射表（字符串 -> 索引位置）
    val orderMap = order
        .distinct() // 去除重复项（可选）
        .withIndex()
        .associate { it.value to it.index }

    // 分离在顺序中和不在顺序中的元素
    val (inOrder, notInOrder) = list.partition {
        orderMap.containsKey(it.name)
    }

    // 对在顺序中的元素排序
    val sortedInOrder = inOrder.sortedBy { orderMap[it.name] }

    // 组合结果（已排序元素 + 未排序元素）
    return sortedInOrder + notInOrder
}