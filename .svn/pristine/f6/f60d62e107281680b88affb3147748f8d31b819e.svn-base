package com.google.chuangke.page.multiple

import android.content.Context
import androidx.media3.common.MimeTypes
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.common.Constants
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import gojson.gojson.Gojson

fun generatePlayUrl(context: Context, channelId: String): PlayUrlEntity {
    val token = SPUtils.getString(context, Constants.SP_KEY_TOKEN, null)
    val data = Gojson.getSource(channelId, token)
    Logger.e(data)

    val jsonObject = JSONObject.parseObject(data)
    var code = jsonObject.getIntValue("code")
    if (code != 1) {
        code = (code + 100) * 2
        return PlayUrlEntity(code)
    }

    val jsonArray = jsonObject.getJSONArray("reData")
    if (jsonArray.size == 0) {
        return PlayUrlEntity(400)
    }

    // 按顺序播放源，直到有可以播的为止
    for (i in jsonArray.indices) {
        try {
            val dataJson = jsonArray.getJSONObject(i)
            val content = dataJson.getString("content")
            val platformName = dataJson.getString("platformName")

            val dir = context.filesDir.path
            val jsonResult = when (platformName) {
//                "1350", "Directv" -> Gojson.start1350(dir, channelId, content, 0)
                "1150", "Fubo" -> Gojson.start1150(channelId, content)
                else -> ""
            }
            Logger.e(jsonResult)

            val json = JSONObject.parseObject(jsonResult) ?: continue
            code = json.getIntValue("code")
            val url = json.getString("url")
            val provider = json.getString("provider")
            val mimeType = json.getString("mimeType")
            if (code != 1) {
                continue
            }

            val mimeTypes = when (mimeType) {
                "m3u8" -> MimeTypes.APPLICATION_M3U8
                "mpd" -> MimeTypes.APPLICATION_MPD
                "mp4" -> MimeTypes.APPLICATION_MP4
                else -> MimeTypes.APPLICATION_M3U8
            }
            return PlayUrlEntity(code, url, provider, mimeTypes)
        } catch (e: Exception) {
            Logger.e(e.message.toString())
        }
    }

    return PlayUrlEntity(401)
}
