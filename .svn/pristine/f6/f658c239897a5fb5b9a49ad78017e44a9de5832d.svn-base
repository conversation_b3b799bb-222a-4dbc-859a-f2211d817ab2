<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_channel_root"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:layout_marginBottom="7dp"
    android:background="@drawable/bg_item_white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:background="@color/black">

    <ImageView
        android:id="@+id/iv_item_channel_live"
        android:layout_width="14dp"
        android:layout_height="15dp"
        android:layout_marginStart="10dp"
        android:contentDescription="@null"
        android:focusable="false"
        android:visibility="invisible"
        tools:src="@mipmap/ic_channel_live"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tv_item_channel_no"
        android:layout_width="28dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:duplicateParentState="true"
        android:focusable="false"
        android:gravity="center"
        android:textColor="@color/selector_color_white_purple"
        android:textSize="12sp"
        tools:text="8888" />

    <ImageView
        android:id="@+id/iv_item_channel_logo"
        android:layout_width="56dp"
        android:layout_height="50dp"
        android:layout_marginStart="12dp"
        android:contentDescription="@null"
        android:focusable="false"
        android:scaleType="fitCenter" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:duplicateParentState="true"
        android:focusable="false"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_channel_title"
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:ellipsize="marquee"
            android:focusable="false"
            android:marqueeRepeatLimit="marquee_forever"
            android:maxLines="1"
            android:singleLine="true"
            android:textColor="@color/selector_color_white_purple"
            android:textSize="15sp"
            tools:text="BABY TV" />

        <TextView
            android:id="@+id/tv_item_channel_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:ellipsize="marquee"
            android:focusable="false"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/selector_color_white_purple"
            android:textSize="9sp"
            tools:ignore="SmallSp"
            tools:text="Da Noite Para o Dia" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_item_channel_lock"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginEnd="8dp"
        android:contentDescription="@null"
        android:duplicateParentState="true"
        android:src="@drawable/selector_lock"
        android:visibility="invisible"
        tools:visibility="visible" />

</LinearLayout>