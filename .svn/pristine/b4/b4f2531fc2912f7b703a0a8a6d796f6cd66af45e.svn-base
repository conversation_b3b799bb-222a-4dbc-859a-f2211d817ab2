package com.google.chuangke.page.dialog

import android.app.Activity
import android.os.Bundle
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.R
import com.google.chuangke.base.BaseSpeechDialog
import com.google.chuangke.common.event.ActionBarSettingEpgEvent
import com.google.chuangke.common.event.FeedbackDialogEvent
import com.google.chuangke.common.event.SettingEvent
import com.google.chuangke.page.MainActivity
import com.google.chuangke.player.PlayerHelper
import org.greenrobot.eventbus.EventBus

class ActionBarSettingDialog(private val activity: Activity, themeId: Int) :
    BaseSpeechDialog(activity, themeId) {

    private lateinit var mRecyclerView: RecyclerView
    private lateinit var mTvVocalTract: TextView
    private lateinit var mTvCaptions: TextView
    private lateinit var mTvResolutionRatio: TextView
    private lateinit var mTvFeedback: TextView
    private lateinit var mTvEpg: TextView
    private lateinit var mTvSetting: TextView
    private lateinit var mTvMultiScreen: TextView
    private lateinit var mActionBarSettingAdapter: ActionBarSettingAdapter

    private val ratio = mutableListOf("Auto", "Full", "16:9", "4:3")
    private lateinit var audioTracks: MutableList<String>
    private lateinit var textTracks: MutableList<String>
    private var selection: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_action_bar_setting)
        initView()
        initFocusListener()
    }

    private fun initView() {
        mRecyclerView = findViewById(R.id.rv_dialog_action_bar_setting)
        mTvVocalTract = findViewById(R.id.tv_dialog_action_bar_setting_vocal_tract)
        mTvCaptions = findViewById(R.id.tv_dialog_action_bar_setting_captions)
        mTvResolutionRatio = findViewById(R.id.tv_dialog_action_bar_setting_resolution_ratio)
        mTvFeedback = findViewById(R.id.tv_dialog_action_bar_setting_feedback)
        mTvEpg = findViewById(R.id.tv_dialog_action_bar_setting_epg)
        mTvSetting = findViewById(R.id.tv_dialog_action_bar_setting_setting)
        mTvMultiScreen = findViewById(R.id.tv_dialog_action_bar_setting_multiple)

        mActionBarSettingAdapter = ActionBarSettingAdapter()
        mRecyclerView.layoutManager = LinearLayoutManager(activity, RecyclerView.HORIZONTAL, false)
        mRecyclerView.adapter = mActionBarSettingAdapter

        initData()
    }

    fun initData(isVod: Boolean) {
        if (isVod) {
            mTvEpg.visibility = View.GONE
            mTvFeedback.visibility = View.GONE
            mTvSetting.visibility = View.GONE
        }

        initData()
    }

    fun initData() {
        audioTracks = PlayerHelper.getInstance().audioTracks
        if (audioTracks.isEmpty()) {
            mTvVocalTract.visibility = View.GONE
        } else {
            mTvVocalTract.visibility = View.VISIBLE
        }

        textTracks = PlayerHelper.getInstance().textTracks
        if (textTracks.isEmpty()) {
            mTvCaptions.visibility = View.GONE
        } else {
            mTvCaptions.visibility = View.VISIBLE
        }

    }

    private fun initFocusListener() {
        mTvEpg.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 0
                mActionBarSettingAdapter.setNewInstance(mutableListOf())
            }
        }
        mTvVocalTract.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 1
                mActionBarSettingAdapter.setNewInstance(audioTracks)
            }
        }
        mTvCaptions.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 2
                mActionBarSettingAdapter.setNewInstance(textTracks)
            }
        }
        mTvResolutionRatio.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 3
                mActionBarSettingAdapter.setNewInstance(ratio)
            }
        }
        mTvFeedback.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 5
                mActionBarSettingAdapter.setNewInstance(mutableListOf())
            }
        }
        mTvSetting.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 6
                mActionBarSettingAdapter.setNewInstance(mutableListOf())
            }
        }
        mTvMultiScreen.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                selection = 7
                mActionBarSettingAdapter.setNewInstance(mutableListOf())
            }
        }

        mTvFeedback.setOnClickListener {
            dismiss()
            EventBus.getDefault().post(FeedbackDialogEvent())
        }

        mTvSetting.setOnClickListener {
            dismiss()
            EventBus.getDefault().post(SettingEvent())
        }
        mTvVocalTract.setOnClickListener {
//            PlayerHelper.getInstance().getTracks()
        }

        mTvVocalTract.setOnKeyListener { _, keyCode, _ ->
            when (keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    dismiss()
                    return@setOnKeyListener true
                }
            }
            false
        }
        mTvEpg.setOnKeyListener { _, keyCode, _ ->
            when (keyCode) {
                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    dismiss()
                    EventBus.getDefault().post(ActionBarSettingEpgEvent())
                    return@setOnKeyListener true
                }
            }
            false
        }

        mTvMultiScreen.setOnClickListener {
            (activity as MainActivity).onMultiScreenClick()
        }

        mActionBarSettingAdapter.setOnItemClickListener { _, _, position ->
            when (selection) {
                1 -> {
                    PlayerHelper.getInstance().setTrackAudio(audioTracks[position])
                }

                2 -> {
                    PlayerHelper.getInstance().setTrackText(textTracks[position])
                }

                3 -> {
                    PlayerHelper.getInstance().setRatio(position)
                }
            }
            dismiss()
        }

    }

    override fun dismiss() {
        mTvEpg.requestFocus()
        mTvEpg.postDelayed({
            super.dismiss()
        }, 100)
    }
}

class ActionBarSettingAdapter :
    BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_dialog_action_bar_setting) {

    override fun convert(holder: BaseViewHolder, item: String) {
        holder.setText(R.id.tv_item_dialog_epg_setting, item)
    }

}