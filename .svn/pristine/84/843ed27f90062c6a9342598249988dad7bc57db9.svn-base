package com.google.chuangke.data

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelLockBean
import com.google.chuangke.entity.CustomTagBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.ext.containsAnyOfIgnoreCase
import com.orhanobut.logger.Logger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.*

const val TAG_SEARCH = -1L
const val TAG_ALL = -2L
const val TAG_HISTORY = -3L
const val TAG_FAVORITE = -4L
const val TAG_ADD = -5L

class ChannelViewModel(
    private val mChannelRepository: ChannelRepository,
    private val mEpgRepository: EpgRepository,
    private val manageCustomTagRepository: ManageCustomTagRepository
) : BaseViewModel() {

    val tagLiveData: MutableLiveData<List<TagBean>> = MutableLiveData()
    val channelLiveData: MutableLiveData<MutableList<ChannelBean>> = MutableLiveData()
    val epgLiveData: MutableLiveData<List<EpgBean>> = MutableLiveData()
    val dateLiveData: MutableLiveData<List<Date>> = MutableLiveData()

    var tempChannelBean: ChannelBean? = null

    init {
        getSevenDate()
        initData()
    }

    fun initData() {
        launch {
            val list = generateDefaultTag()

            manageCustomTagRepository.getCustomTags().let {
                val customTags = it.map { customBean ->
                    TagBean(
                        customBean.id!!, customBean.name, 0, -1, custom = customBean.edit
                    )
                }
                list.addAll(customTags)
            }

            mChannelRepository.getTagList().let {
                list.addAll(it)
            }

            tagLiveData.value = list
        }
    }

    private val getCustomTagFlow: Flow<List<CustomTagBean>> = flow {
        val list = manageCustomTagRepository.getCustomTags()
        emit(list)
    }

    private fun generateDefaultTag(): MutableList<TagBean> {
        val tempTagList = mutableListOf<TagBean>()
        tempTagList.add(
            TagBean(
                TAG_SEARCH, MyApplication.context.getString(R.string.txt_tag_search), 0, -1
            )
        )
        if (DBApi.getInstance().getCustomTags().size < 3) {
            tempTagList.add(
                TagBean(
                    TAG_ADD, MyApplication.context.getString(R.string.txt_tag_add), 0, -1
                )
            )
        }

        tempTagList.add(
            TagBean(
                TAG_ALL, MyApplication.context.getString(R.string.txt_tag_all), 0, -1
            )
        )
        tempTagList.add(
            TagBean(
                TAG_HISTORY, MyApplication.context.getString(R.string.txt_tag_history), 0, -1
            )
        )
//        tempTagList.add(
//            TagBean(
//                TAG_FAVORITE, MyApplication.context.getString(R.string.txt_tag_favorite_list), 0, -1
//            )
//        )
        return tempTagList
    }

    /**
     * 获取当前后七天的日期
     */
    fun getSevenDate() {
        launch {
            mChannelRepository.getSevenDate().let {
                dateLiveData.postValue(it)
            }
        }
    }

    /**
     * 获取分类下的频道
     */
    fun getChannelList(position: Int) {
        tagLiveData.value?.let { tagList ->
            val tagBean = tagList[position]
            launch {
                if ((tagBean.custom ?: 0) > 0) {
//                    Logger.e("xxxxxxxxx:${tagBean.id}")
                    mChannelRepository.getChannelListByCustomTag(tagBean.id).let {
                        channelLiveData.postValue(it)
                    }
                } else {
                    mChannelRepository.getChannelList(tagBean).let {
                        channelLiveData.postValue(it)
                    }
                }
            }
        }
    }

    /**
     * 获取EPG栏目
     */
    fun getEpgListByDay(tagBean: TagBean, channelBean: ChannelBean, date: Date) {
        tempChannelBean = channelBean
        launch {

            // 特殊频道要隐藏日期切换
            if (tagBean.name?.containsAnyOfIgnoreCase(Config.getInstance().sportChannel) == true) {
                mEpgRepository.getEspecialEpgListByDay(channelBean, date).let {
                    epgLiveData.postValue(it)
                }
            } else {
                mEpgRepository.getEpgListByDay(channelBean, date).let {
                    epgLiveData.postValue(it)
                }
            }
        }
    }

    /**
     * 获取EPG栏目
     */
    fun getEpgListByDay(date: Date) {
        launch {
            mEpgRepository.getEpgListByDay(tempChannelBean!!, date).let {
                epgLiveData.postValue(it)
            }
        }
    }

    /**
     * 加锁
     */
    fun saveLockedChannel(channelId: Long) {
        launch {
            val channelLockBean = ChannelLockBean()
            channelLockBean.unid = channelId
            channelLockBean.createTime = (System.currentTimeMillis() / 1000).toInt()
            mChannelRepository.saveLockedChannel(channelLockBean)
        }
    }


}