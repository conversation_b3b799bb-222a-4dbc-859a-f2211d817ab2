<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_epg_root"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:background="@drawable/bg_item_white"
    android:baselineAligned="false"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:background="@color/black">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_weight="1"
        android:duplicateParentState="true"
        android:focusable="false"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_epg_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:focusable="false"
            android:textColor="@color/selector_color_white_purple"
            android:textSize="12sp"
            tools:text="14:00-17:00" />

        <TextView
            android:id="@+id/tv_item_epg_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:ellipsize="marquee"
            android:focusable="false"
            android:marqueeRepeatLimit="marquee_forever"
            android:singleLine="true"
            android:textColor="@color/selector_color_white_purple"
            android:textSize="14sp"
            tools:text="i ne oite com vanilovanilovanilovanilovanilovanilovanilo" />
    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rl_item_epg_control"
        android:layout_width="75dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:visibility="gone"
        tools:visibility="visible">

        <TextView
            android:id="@+id/tv_item_epg_playing"
            style="@style/font_bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/item_epg_playing"
            android:textColor="@color/purpler_7B0B82"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <ImageView
            android:id="@+id/iv_item_channel_replay"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:contentDescription="@null"
            android:focusable="true"
            android:src="@drawable/selector_channel_replay"
            android:visibility="gone"
            tools:visibility="visible" />
    </RelativeLayout>

</LinearLayout>