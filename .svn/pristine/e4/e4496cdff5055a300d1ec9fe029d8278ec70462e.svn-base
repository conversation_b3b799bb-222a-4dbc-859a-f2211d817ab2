package com.google.chuangke.player

import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.activity.result.launch
import androidx.annotation.OptIn
import androidx.lifecycle.lifecycleScope
import com.alibaba.fastjson.JSONObject
import androidx.media3.common.MimeTypes
import androidx.media3.common.util.UnstableApi
import com.google.chuangke.MyApplication.Companion.context
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.common.GlobalThreadPools
import com.google.chuangke.common.UserHelper
import com.google.chuangke.common.event.LiveChangeEvent
import com.google.chuangke.common.event.PlayerStatusEvent
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelHistoryBean
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import gojson.gojson.Gojson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.util.Timer
import java.util.TimerTask

class LivePlayHelper {

    companion object {
        fun getInstance() = InstanceHelper.instance
    }

    object InstanceHelper {
        val instance = LivePlayHelper()
    }

    // 用于相同频道需要播放时的控制
    var initPlay = true

    fun channelPlay(
        channelBean: ChannelBean,
        isWatchHistory: Boolean = false,
        playback: Boolean = false,
        playbackTime: Long = 0L,
        retry: Boolean = false
    ) {

        // 当前正常频道播放(非回拨)情况，同一频道不再次播放
        if (!initPlay && Config.getInstance().playType == 0 && channelBean.id == Config.getInstance().currentPlayChannel.id) {
            return
        }
        initPlay = false

        Config.getInstance().playType = 0

        // 只要调用了就显示信息
        PlayerHelper.getInstance().showChannelInfoDialog(channelBean)
        PlayerHelper.getInstance().showLoadingDialog()
        PlayerHelper.getInstance().stopPlay()

        if (!playback)
            EventBus.getDefault().post(LiveChangeEvent())
        // 处理连点事件
        execute {
            play(channelBean, isWatchHistory, playbackTime, retry)
        }
    }

    private fun play(
        channelBean: ChannelBean,
        isWatchHistory: Boolean,
        playbackTime: Long,
        retry: Boolean
    ) {
        //记录当前播放类型
        Config.getInstance().playType = 0
        //添加操作日志
        UserHelper.getInstance().addOperationLog(channelBean)
        //记录当前播放频道
        Config.getInstance().currentPlayChannel = channelBean
        val token = SPUtils.getString(context, Constants.SP_KEY_TOKEN, null)

        //非加密频道添加到播放历史
        if (!channelBean.tags!!.contains(Constants.FILTER_CONDITION)) {
            val channelHistoryBean = ChannelHistoryBean()
            channelHistoryBean.unid = channelBean.id
            channelHistoryBean.createTime = (System.currentTimeMillis() / 1000).toInt()
            if (!isWatchHistory) {
                // 非查看历史换台，才存入
                DBApi.getInstance().saveChannelHistory(channelHistoryBean)
            }
        }

        CoroutineScope(Dispatchers.IO).launch {
            val channelId = channelBean.id.toString()
            val data = Gojson.getSource(channelId, token)
            Logger.e(data)

            val jsonObject = JSONObject.parseObject(data)
            var code = jsonObject.getIntValue("code")
            if (code != 1) {
                code = (code + 100) * 2
                EventBus.getDefault().post(PlayerStatusEvent(code, 0))
                return@launch
            }

            val jsonArray = jsonObject.getJSONArray("reData")
            if (jsonArray.size == 0) {
                EventBus.getDefault().post(PlayerStatusEvent(400, 0))
                return@launch
            }

            // 按顺序播放源，直到有可以播的为止
            try {
                var playing = false
                for (i in jsonArray.indices) {
                    val dataJson = jsonArray.getJSONObject(i)
                    val content = dataJson.getString("content")
                    val platformName = dataJson.getString("platformName")

                    val dir = context.filesDir.path
                    val jsonResult = when (platformName) {
                        "1350", "Directv" -> Gojson.start1350(dir, channelId, content, playbackTime)
                        "1150", "Fubo" -> Gojson.start1150(channelId, content)
                        else -> ""
                    }
                    Logger.e(jsonResult)

                    val json = JSONObject.parseObject(jsonResult) ?: continue
                    code = json.getIntValue("code")
                    val url = json.getString("url")
                    val provider = json.getString("provider")
                    val mimeType = json.getString("mimeType")
                    if (code != 1) {
                        continue
                    }

                    val mimeTypes = when (mimeType) {
                        "m3u8" -> MimeTypes.APPLICATION_M3U8
                        "mpd" -> MimeTypes.APPLICATION_MPD
                        "mp4" -> MimeTypes.APPLICATION_MP4
                        else -> MimeTypes.APPLICATION_M3U8
                    }

                    Handler(Looper.getMainLooper()).post {
                        PlayerHelper.getInstance().startPlay(provider, url, null, mimeTypes, retry)
                        PlayerHelper.getInstance().setTrackText(PlayerHelper.TEXT_TRACK_NONE)
                        PlayerHelper.getInstance().setTrackAudio("en")
                    }

                    PlayerHelper.getInstance().setChannelChange(true)

                    playing = true
                    break
                }

                if (!playing){
                    EventBus.getDefault().post(PlayerStatusEvent(401, 0))
                }
            } catch (e: Exception) {
                Logger.e(e.message.toString())
            }
        }

    }

    private var timer = Timer()
    private var timerTask: TimerTask? = null
    private fun execute(runnable: () -> Unit) {
        timerTask?.cancel()
        timerTask = object : TimerTask() {
            override fun run() {
                runnable.invoke()
            }
        }
        timer.schedule(timerTask, 500)
    }

    fun release() {
        initPlay = true
    }

}
