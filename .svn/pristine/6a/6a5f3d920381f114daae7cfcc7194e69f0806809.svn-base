package com.google.chuangke.page.center.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.page.center.ChannelFragment
import com.google.chuangke.util.DateUtil
import java.text.SimpleDateFormat
import java.util.*

class WeekPresenter(private var mChannelsFragment: ChannelFragment) : BasePresenter<Date>() {

    override fun layoutId(): Int {
        return R.layout.item_weeks
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> {
                needKeep = true
            }
        }

        return false
    }

    override fun bindViewHolder(view: View, item: Date) {

        // 天转化格式
        val dateFm = SimpleDateFormat("dd", Locale.ENGLISH)

        // 星期转化格式
        val weekFm = SimpleDateFormat("EEE", Locale.ENGLISH)

        // 日期
        view.findViewById<TextView>(R.id.tv_item_week_day).text =
            if (DateUtil.isToday(item)) context.getString(R.string.week_day_today)
            else dateFm.format(item).toInt().toString()

        // 周几
        view.findViewById<TextView>(R.id.tv_item_week_week).text =
            weekFm.format(item).uppercase()

        // 分割线
        view.findViewById<ImageView>(R.id.iv_item_week_day).visibility =
            if (mChannelsFragment.mWeekAdapter.indexOf(item) == mChannelsFragment.mWeekAdapter.size() - 1) View.GONE else View.VISIBLE
    }

    fun cancelNeedKeep() {
        needKeep = false
        lastSelectedView?.isActivated = false
        lastSelectedView = null
        map.clear()
    }

}