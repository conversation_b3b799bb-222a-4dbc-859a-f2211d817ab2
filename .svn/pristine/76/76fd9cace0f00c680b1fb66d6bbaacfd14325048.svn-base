package com.google.chuangke.page.dialog

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.KeyEvent
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.VerticalGridView
import com.google.chuangke.R
import com.google.chuangke.base.DisableSpeechDialog
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.CustomTagBean
import com.google.chuangke.page.menu.presenter.CustomTagPresenter

interface OnChooseGroupDialogChooseListener {
    fun onChoose(customIds: List<Long>, index: Int)
}

class ChooseGroupDialog(
    activity: Activity,
    themeId: Int,
    private val mOnChooseGroupDialogChooseListener: OnChooseGroupDialogChooseListener
) : DisableSpeechDialog(activity, themeId) {

    private lateinit var mVerticalGridView: VerticalGridView
    private lateinit var mAdapter: ArrayObjectAdapter
    private lateinit var mTvOk: TextView
    private lateinit var mTvCancel: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.dialog_add_group)
        initView()
        val window: Window? = this.window
        if (window != null) {
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
            )
            window.setDimAmount(0f)
        }
        initListener()
    }

    private fun initView() {
        mTvOk = findViewById(R.id.tv_dialog_add_group_save)
        mTvCancel = findViewById(R.id.tv_dialog_add_group_cancel)
        mVerticalGridView = findViewById(R.id.rv_dialog_add_group)
        mVerticalGridView.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(CustomTagPresenter()).also {
                mAdapter = it
            }).also {
                it.setAdapterListener(object : ItemBridgeAdapter.AdapterListener() {
                    override fun onCreate(viewHolder: ItemBridgeAdapter.ViewHolder) {
                        super.onCreate(viewHolder)
                        viewHolder.itemView.setOnClickListener { itemView ->
                            itemView.tag?.let { tag ->
                                (tag as CustomTagBean).let { customTagBean ->
                                    customTagBean.isSelect = !customTagBean.isSelect
                                    viewHolder.itemView.tag?.let { value ->
                                        it.notifyItemChanged(mAdapter.indexOf(value))
                                    }
                                }
                            }
                        }
                    }
                })
            }
    }

    private fun initListener() {
        mVerticalGridView.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    if (mVerticalGridView.selectedPosition == mAdapter.size() - 1) {
                        mTvOk.requestFocus()
                        return@setOnKeyInterceptListener true
                    }
                }
            }

            return@setOnKeyInterceptListener false
        }
        mTvOk.setOnClickListener {
            val selectedList = list.filter {
                it.isSelect
            }
            mOnChooseGroupDialogChooseListener.onChoose(
                selectedList.map { it.id!! }, tempIndex
            )
            dismiss()
        }
        mTvCancel.setOnClickListener {
            dismiss()
        }
    }

    private var tempIndex: Int = 0
    private var list: List<CustomTagBean> = mutableListOf()

    fun showDialog(index: Int) {
        show()
        tempIndex = index
        list = DBApi.getInstance().getCustomTags()
        mVerticalGridView.requestFocus()
        mAdapter.setItems(list, null)
    }

}
