<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="40dp"
    android:layout_height="40dp"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:background="@drawable/selector_dialog_epg_date"
    android:focusable="true"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/iv_ll_item_dialog_epg_date"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:focusable="false"
        android:src="@drawable/shape_today"
        android:visibility="gone"
        tools:visibility="visible" />

    <LinearLayout
        android:id="@+id/ll_item_dialog_epg_date"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:duplicateParentState="true"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="1dp">

        <TextView
            android:id="@+id/tv_item_dialog_epg_date_no"
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:focusable="false"
            android:textColor="@color/programguide_selector_item_dialog_epg_date_text"
            android:textSize="16sp"
            tools:text="28" />

        <TextView
            android:id="@+id/tv_item_dialog_epg_date_week"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:focusable="false"
            android:textColor="@color/selector_item_dialog_epg_date_subtext"
            android:textSize="11sp"
            tools:text="SUN" />
    </LinearLayout>
</FrameLayout>