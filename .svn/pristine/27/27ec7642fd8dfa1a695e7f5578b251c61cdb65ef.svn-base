package com.google.chuangke.page.menu

import android.content.Intent
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.TextView
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Constants
import com.google.chuangke.common.UserHelper
import com.google.chuangke.ext.toast
import com.google.chuangke.http.HttpCallback
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.page.*
import com.google.chuangke.util.SPUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class MenuAccountFragment : BaseFragment() {

    private lateinit var mTvAccountName: TextView
    private lateinit var mTvExpiryDateTitle: TextView
    private lateinit var mTvExpiryDate: TextView
    private lateinit var mTvLogin: TextView
    private lateinit var mTvTips: TextView

    override fun layoutId(): Int {
        return R.layout.fragment_menu_account
    }

    override fun initView(view: View) {
        mTvAccountName = view.findViewById(R.id.tv_fragment_menu_account_account_name)
        mTvExpiryDateTitle = view.findViewById(R.id.tv_fragment_menu_account_expiry_date_title)
        mTvExpiryDate = view.findViewById(R.id.tv_fragment_menu_account_expiry_date)
        mTvLogin = view.findViewById(R.id.tv_fragment_menu_account_login)
        mTvTips = view.findViewById(R.id.tv_fragment_menu_account_tips)

        initTips()
    }

    private fun initTips() {
        val part1 = getString(R.string.menu_account_login_tips1)
        val ssb = SpannableStringBuilder(part1)
        ssb.setSpan(
            ForegroundColorSpan(resources.getColor(R.color.white)),
            0,
            part1.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        mTvTips.text = ssb

        setData()
    }

    override fun initListener() {

        mTvLogin.setOnClickListener {
            if (mTvLogin.text == "Logout") {
                logout()
            } else {
                startActivity(Intent(requireActivity(), LoginActivity::class.java))
            }
        }
    }

    private fun setData() {
        val userInfo = SPUtils.getObject(MyApplication.context, Constants.SP_KEY_USER_INFO) as String
        val userObject = JSONObject.parseObject(userInfo)
        if (userObject.getInteger("isTest") != 1) {
            mTvLogin.text = "Logout"
            mTvTips.visibility = View.INVISIBLE
            mTvLogin.visibility = View.INVISIBLE
        }
        mTvAccountName.text = userObject.getString("code")
        mTvExpiryDate.text = userObject.getString("endDate_str")
    }

    private fun logout() {
        HttpHelper.getInstance().postApi("logout", JSONObject(), object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    requireActivity().runOnUiThread { requireActivity().toast(msg) }
                    return
                }
                SPUtils.clearSync(requireContext())
                requireActivity().runOnUiThread {
                    UserHelper.getInstance().exit()
                }
            }

            override fun onError(err: String) {
                requireActivity().runOnUiThread { requireActivity().toast(err) }
            }
        })
    }


}