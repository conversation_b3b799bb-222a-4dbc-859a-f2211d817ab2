package com.google.chuangke.data.menu

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.base.BaseViewModel

class MenuInfoViewModel :
    BaseViewModel() {

    val detailLiveData: MutableLiveData<List<Pair<String?, String?>>> = MutableLiveData()

    init {
        initDetail()
    }

    fun initDetail() {
        val detailList = mutableListOf<Pair<String?, String?>>()

//        val contactStr = SPUtils.getString(MyApplication.context, Constants.SP_KEY_CONTACT_US, null)
//        val jsonArray = JSONArray.parseArray(contactStr)
//        for (item in jsonArray) {
//            val jsonObject = item as JSONObject
//            detailList.add(Pair(jsonObject.getString("type"), jsonObject.getString("content")))
//        }

        detailList.add(Pair("", ""))
        detailList.add(
            Pair(
                "",
                "- Please check FAQ inside the Feedback for general questions\n- For channel/app issues, you can submit inside Feedback\n"
            )
        )
        detailList.add(
            Pair(
                "",
                "You can contact us together with following info:"
            )
        )
        detailList.add(Pair("", "(1) Machine MAC number;"))
        detailList.add(Pair("", "(2) Photo(s)/video(s) of the issue;"))
        detailList.add(Pair("", "(3) Short description of the issue by email/WhatsApp/online chat on our official website."))

        detailLiveData.postValue(detailList)
    }
}