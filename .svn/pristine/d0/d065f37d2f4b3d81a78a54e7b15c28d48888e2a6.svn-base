package com.google.chuangke.base

import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.LayoutInflater
import androidx.appcompat.app.AppCompatActivity
import androidx.viewbinding.ViewBinding
import com.google.chuangke.common.HomeWatcherReceiver
import org.greenrobot.eventbus.EventBus

abstract class BaseActivity<VB : ViewBinding>(private val inflate: (LayoutInflater) -> VB) :
    AppCompatActivity() {
    private var mHomeKeyReceiver: HomeWatcherReceiver? = null

    lateinit var binding: VB

    abstract fun initView()

    abstract fun initListener()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = inflate(layoutInflater)
        setContentView(binding.root)

        initView()
        initListener()

        if (isRegisterEventBus()) {
            EventBus.getDefault().register(this)
        }

        mHomeKeyReceiver = HomeWatcherReceiver()
        val homeFilter = IntentFilter(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)
        registerReceiver(mHomeKeyReceiver, homeFilter)
    }

    override fun onDestroy() {
        if (isRegisterEventBus()) {
            EventBus.getDefault().unregister(this)
        }

        if (null != mHomeKeyReceiver) {
            unregisterReceiver(mHomeKeyReceiver)
        }
        super.onDestroy()
    }

    //重写注册EventBus
    open fun isRegisterEventBus(): Boolean {
        return false
    }

}