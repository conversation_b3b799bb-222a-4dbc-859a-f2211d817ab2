package com.google.chuangke.page.multiple

import android.annotation.SuppressLint
import android.net.Uri
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.children
import androidx.core.view.get
import androidx.media3.common.MediaItem
import androidx.media3.common.MimeTypes
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.HttpDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.ui.PlayerView
import com.google.chuangke.R
import com.google.chuangke.base.BaseActivity
import com.google.chuangke.databinding.FragmentMultipleBinding
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.player.multiple.SourceLoader
import com.google.chuangke.player.multiple.SourceLoaderPool
import com.google.chuangke.util.FocusAnimationUtils
import com.google.chuangke.util.ScreenUtils


class MultipleScreenActivity :
    BaseActivity<FragmentMultipleBinding>(FragmentMultipleBinding::inflate) {

    private var mChannelFragment: MsChannelFragment? = null

    val sourceLoaderList = mutableMapOf<Int, SourceLoader>()

    private val views = mutableListOf<FrameLayout>()

    private var position: Int = 0

    private var dp480: Int = 0
    private var dp960: Int = 0
    private var dp270: Int = 0
    private var dp540: Int = 0

    override fun initView() {
        binding.root.children.forEach {
            if (it is FrameLayout)
                views.add(it)
        }
        dp480 = ScreenUtils.dp2px(this@MultipleScreenActivity, 480)
        dp960 = ScreenUtils.dp2px(this@MultipleScreenActivity, 960)
        dp270 = ScreenUtils.dp2px(this@MultipleScreenActivity, 270)
        dp540 = ScreenUtils.dp2px(this@MultipleScreenActivity, 540)
    }

    private fun showChannelFragment() {
        val ft = supportFragmentManager.beginTransaction()
        if (mChannelFragment == null) {
            mChannelFragment = MsChannelFragment()
            ft.add(
                android.R.id.content, mChannelFragment!!, "mChannelFragment"
            )
        } else {
            ft.show(mChannelFragment!!)
        }
        ft.commit()
    }

    fun disChannelFragment() {
        val ft = supportFragmentManager.beginTransaction()
        ft.hide(mChannelFragment!!)
        ft.commit()
    }

    private var scale = false

    override fun initListener() {
        binding.playerView1.requestFocus()
        setOnClickListener(binding.fl1)
        setOnClickListener(binding.fl2)
        setOnClickListener(binding.fl3)
        setOnClickListener(binding.fl4)
    }

    private fun setOnClickListener(view: FrameLayout) {
        view.setOnKeyListener { v, _, event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    if (scale) {
                        changeScale(v)
                    } else {
                        finish()
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    when (v.id) {
                        R.id.fl1 -> {
                            position = 0
                            if (sourceLoaderList[position] != null) {
                                if (!scale) {
                                    changeScale(v)
                                }
                            } else {
                                showChannelFragment()
                            }
                        }

                        R.id.fl2 -> {
                            position = 1
                            if (sourceLoaderList[position] != null) {
                                if (!scale) {
                                    changeScale(v)
                                }
                            } else {
                                showChannelFragment()
                            }
                        }

                        R.id.fl3 -> {
                            position = 2
                            if (sourceLoaderList[position] != null) {
                                if (!scale) {
                                    changeScale(v)
                                }
                            } else {
                                showChannelFragment()
                            }
                        }

                        R.id.fl4 -> {
                            position = 3
                            if (sourceLoaderList[position] != null) {
                                if (!scale) {
                                    changeScale(v)
                                }
                            } else {
                                showChannelFragment()
                            }
                        }
                    }
                    return@setOnKeyListener true
                }

                KeyEvent.KEYCODE_SETTINGS, KeyEvent.KEYCODE_MENU -> {
                    if (!scale) {
                        showChannelFragment()
                    }
                    return@setOnKeyListener true
                }
            }

            return@setOnKeyListener false
        }
    }

    private fun changeScale(v: View) {
        scale = !scale
        views.filter { it.id != v.id }
            .forEach { it.visibility = if (scale) View.GONE else View.VISIBLE }

        FocusAnimationUtils.animateViewSizeAndScale(
            v,
            if (scale) dp480 else dp960,
            if (scale) dp960 else dp480,
            if (scale) dp270 else dp540,
            if (scale) dp540 else dp270,
            1000L,
            {
                if (scale) {
                    val params = (v as ViewGroup)[0].layoutParams
                    params.width = dp960
                    params.height = dp540
                    v[0].layoutParams = params
                    v[2].visibility = View.GONE
                }

            }, {
                if (!scale) {
                    val params = (v as ViewGroup)[0].layoutParams
                    params.width = dp480
                    params.height = dp270
                    v[0].layoutParams = params
                    v[2].visibility = View.VISIBLE
                }
            })
    }

    /**
     * 创建播放器并设置到 PlayerView
     */
    @SuppressLint("UnsafeOptInUsageError")
    private fun createPlayer(playerView: PlayerView, playUrl: String): ExoPlayer {
        val player = ExoPlayer.Builder(this).build()
        playerView.player = player
        playerView.controllerAutoShow = false
        playerView.useController = false
        playerView.keepScreenOn = true
        playerView.setKeepContentOnPlayerReset(true)

        // 设置媒体源
        val mediaBuilder = MediaItem.Builder().setUri(Uri.parse(playUrl))
        mediaBuilder.setMimeType(MimeTypes.APPLICATION_M3U8)
        val mediaItem = mediaBuilder.build()

        val dataSourceFactory: HttpDataSource.Factory =
            DefaultHttpDataSource.Factory().setReadTimeoutMs(60_000).setConnectTimeoutMs(60_000)
        val ms = DefaultMediaSourceFactory(dataSourceFactory).createMediaSource(mediaItem)

        player.setMediaSource(ms)
        player.prepare()
        player.volume = 0.0f
        player.playWhenReady = true

        return player
    }

    /**
     * 设置焦点监听
     */
    private fun setFocusListener(view: View, sourceLoader: SourceLoader?) {
        view.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                // 获取焦点时打开声音
                sourceLoader?.exoPlayer?.volume = 1.0f
            } else {
                // 失去焦点时关闭声音
                sourceLoader?.exoPlayer?.volume = 0.0f
            }
        }
    }

    override fun onDestroy() {
        // 释放播放器资源
        for (sourceLoader in sourceLoaderList) {
            SourceLoaderPool.getInstance().release(sourceLoader.value)
        }
        sourceLoaderList.clear()
        super.onDestroy()
    }

    fun onProgramSelect(channelBean: ChannelBean) {
        disChannelFragment()
        when (position) {
            0 -> {
                binding.add1.visibility = View.GONE
                val model = SourceLoaderPool.getInstance().acquire(
                    channelBean,
                    binding.playerView1
                )
                sourceLoaderList[position] = model

                setFocusListener(binding.fl1, model)
            }

            1 -> {
                binding.add2.visibility = View.GONE
                val model = SourceLoaderPool.getInstance().acquire(
                    channelBean,
                    binding.playerView2
                )
                sourceLoaderList[position] = model
                setFocusListener(binding.fl2, model)
            }

            2 -> {
                binding.add3.visibility = View.GONE
                val model = SourceLoaderPool.getInstance().acquire(
                    channelBean,
                    binding.playerView3
                )
                sourceLoaderList[position] = model
                setFocusListener(binding.fl3, model)
            }

            3 -> {
                binding.add4.visibility = View.GONE
                val model = SourceLoaderPool.getInstance().acquire(
                    channelBean,
                    binding.playerView4
                )
                sourceLoaderList[position] = model
                setFocusListener(binding.fl4, model)
            }
        }
    }

    override fun onBackPressed() {
        super.onBackPressed()
    }

}