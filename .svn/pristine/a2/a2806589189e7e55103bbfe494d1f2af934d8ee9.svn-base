package com.google.chuangke.page

import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageInfo
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Bundle
import android.provider.Settings
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.R
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.common.LiveService
import com.google.chuangke.common.UpgradeHelper
import com.google.chuangke.common.UserHelper
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.util.SPUtils
import com.google.chuangke.util.Utils
import com.orhanobut.logger.Logger
import com.wochuang.json.DeviceIdUtil
import com.wochuang.json.NativeLib
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Request
import okhttp3.Response
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock
import kotlin.math.abs

/**
 * 通过EventBus发送SplashLoadingEvent事件，来处理各个任务，并且展示进度
 */
@SuppressLint("CustomSplashScreen")
class SplashActivity : Activity() {
    private lateinit var mPb: ProgressBar
    private lateinit var mTvInfo: TextView

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)
        Config.getInstance().release()
        initView()
        EventBus.getDefault().register(this)
    }

    private fun initView() {
        mPb = findViewById(R.id.pb_activity_splash)
        mTvInfo = findViewById(R.id.tv_activity_splash_info)
        mPb.max = 100
    }

    override fun onResume() {
        super.onResume()
        onSplashLoadingEvent(SplashLoadingEvent(LoadInfo.CHECK_NET_WORK))
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            UserHelper.getInstance().exit() //退出时必须执行
        }
        return super.onKeyDown(keyCode, event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onSplashLoadingEvent(event: SplashLoadingEvent) {
        mPb.progress = event.loadInfo.percent
        mTvInfo.text = event.loadInfo.tips

        when (event.loadInfo) {
            LoadInfo.CHECK_NET_WORK -> checkNetwork()
            LoadInfo.GET_IP_INFO -> getIpInfo()
            LoadInfo.GET_BASE_URL -> getBaseUrl()
            LoadInfo.CHECK_VERSION -> UpgradeHelper(this@SplashActivity).checkUpdate(false)
            LoadInfo.CHECK_DEVICE_AUTH -> auth()
            LoadInfo.DEVICE_LOGIN -> deviceAuth()
            LoadInfo.CHECK_TESTABLE -> testable()
            LoadInfo.TEST_LOGIN -> testAuth()
            LoadInfo.GET_USER_INFO -> UserHelper.getInstance().getUserInfo(this@SplashActivity)
            LoadInfo.GET_RESOURCE -> startService(Intent(this, LiveService::class.java))
            LoadInfo.UNZIP_DB -> {} //只展示进度即可
            LoadInfo.COMPLETE -> {
                startActivity(Intent(this@SplashActivity, MainActivity::class.java))
                finish()
            }

            else -> {}
        }
    }

    private fun checkNetwork() {
        if (!isNetSystemUsable()) {
            showNetworkDialog()
        } else {
            onSplashLoadingEvent(SplashLoadingEvent(LoadInfo.GET_IP_INFO))
        }
    }

    /**
     * 判断当前网络是否可用
     */
    private fun isNetSystemUsable(): Boolean {
        var isNetUsable = false
        val manager = getSystemService(CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkCapabilities = manager.getNetworkCapabilities(manager.activeNetwork)
        if (networkCapabilities != null) {
            isNetUsable =
                networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
        }
        return isNetUsable
    }

    private var alter: AlertDialog? = null

    private fun showNetworkDialog() {
        if (alter != null && alter!!.isShowing) {
            return
        }

        alter = AlertDialog.Builder(this@SplashActivity, R.style.XUpdate_DialogTheme)
            .setMessage("Unable to access the Internet, please check the network and try again.")
            .setNegativeButton("TRY AGAIN") { _, _ ->
                alter!!.dismiss()
                onResume()
            }.setPositiveButton("OPEN NETWORK SETTINGS") { _, _ ->
                alter!!.dismiss()
                startActivity(Intent(Settings.ACTION_WIFI_SETTINGS))
            }.setCancelable(false).create()

        alter!!.show()

        val positiveButton: Button = alter!!.getButton(AlertDialog.BUTTON_POSITIVE)
        val negativeButton: Button = alter!!.getButton(AlertDialog.BUTTON_NEGATIVE)

        settingButton(positiveButton)
        settingButton(negativeButton)

        negativeButton.requestFocus()
    }

    private fun settingButton(button: Button) {
        button.setTextColor(resources.getColorStateList(R.color.selector_dialog_text, null))
        button.setBackgroundResource(R.drawable.selector_dialog_button)
        button.setPadding(20, 0, 20, 0)
        button.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    alter!!.dismiss()
                    finish()
                    return@setOnKeyListener true
                }
            }
            false
        }
    }

    private fun getBaseUrl() {
        val lock = ReentrantLock()
        for (i in 0..4) {
            CoroutineScope(Dispatchers.IO).launch {
                val urlInfo: String = NativeLib.getBase(this@SplashActivity, i)
                if (!TextUtils.isEmpty(urlInfo) && urlInfo != "-1") {
                    val jsonArray: JSONArray = JSONArray.parseArray(urlInfo)
                    val jsonObject: JSONObject = jsonArray.getJSONObject(0)
                    val url: String = jsonObject.getString("s")

                    lock.lock()
                    if (Config.getInstance().baseUrl != null) {
                        lock.unlock()
                        return@launch
                    }
                    Config.getInstance().baseUrl = url

                    // 发送事件才能在主线程处理，直接调用还是在子线程
                    EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.CHECK_VERSION))
                    lock.unlock()
                }
            }
        }
    }

    /**
     * 鉴权
     */
    private fun auth() {
        val token: String? = SPUtils.getString(this@SplashActivity, Constants.SP_KEY_TOKEN, "")
        if (!TextUtils.isEmpty(token)) {
            onSplashLoadingEvent(SplashLoadingEvent(LoadInfo.GET_USER_INFO))
            return
        }

        if (isDeviceAuth()) {
            onSplashLoadingEvent(SplashLoadingEvent(LoadInfo.DEVICE_LOGIN))
        } else {
            onSplashLoadingEvent(SplashLoadingEvent(LoadInfo.TEST_LOGIN))
        }
    }

    private fun isDeviceAuth(): Boolean {
        Config.getInstance().deviceAuthSN = NativeLib.getSystemInfo(this)
        return "-1" != Config.getInstance().deviceAuthSN
    }

    /**
     * 检查是否可以测试登录
     */
    private fun testable() {
        HttpHelper.getInstance().postApi("testable", JSONObject(), object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                e.printStackTrace()
            }

            override fun onResponse(call: Call, response: Response) {
                val jsonObject: JSONObject?
                try {
                    val result = response.body!!.string()
                    val responseData = NativeLib.getResponseData(this@SplashActivity, result)
                    jsonObject = JSONObject.parseObject(responseData)
                } catch (e: Exception) {

                    Log.e("UserHelper", "api接口返回异常:" + call.request().url)
                    return
                }
                if (jsonObject == null) {
                    return
                }

                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    runOnUiThread {
                        Toast.makeText(this@SplashActivity, msg, Toast.LENGTH_LONG).show()
                    }
                    return
                }
                val reData = jsonObject.getString("reData")
                val reJson = JSONObject.parseObject(reData)
                val enable = reJson.getInteger("enable")
                if (enable == 1) {
                    EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.TEST_LOGIN))
                } else {
                    startActivity(Intent(this@SplashActivity, LoginActivity::class.java))
                    finish()
                }
            }
        })
    }

    /**
     * 登录测试账号
     */
    private fun testAuth() {
        HttpHelper.getInstance().postApi("logintest", getTestParams(), object : Callback {
            override fun onFailure(call: Call, e: IOException) {

            }

            override fun onResponse(call: Call, response: Response) {
                val jsonObject: JSONObject?
                try {
                    val result = response.body!!.string()
                    val responseData = NativeLib.getResponseData(this@SplashActivity, result)
                    jsonObject = JSONObject.parseObject(responseData)
                } catch (e: Exception) {
                    Log.e("UserHelper", "api接口返回异常:" + call.request().url)
                    return
                }
                if (jsonObject == null) {
                    return
                }

                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    runOnUiThread {
                        startActivity(Intent(this@SplashActivity, LoginActivity::class.java))
                        finish()
                    }
                    return
                }
                val reData = jsonObject.getString("reData")
                val respJson = JSONObject.parseObject(reData)
                val token = respJson.getString("token")
                SPUtils.putString(this@SplashActivity, Constants.SP_KEY_TOKEN, token)
                EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.GET_USER_INFO))
            }
        })
    }

    @SuppressLint("HardwareIds")
    private fun getTestParams(): JSONObject {
        val mac = DeviceIdUtil.getMac()
        val cpuId = DeviceIdUtil.getCPUId()
        var sn: String? = Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        if (TextUtils.isEmpty(mac) && TextUtils.isEmpty(cpuId) && TextUtils.isEmpty(sn)) {
            val sp = getSharedPreferences("data", MODE_PRIVATE)
            sn = sp.getString("RANDOM_SN", "")
        }

        val params = JSONObject()
        params["id"] = "test"
        params["password"] = "test"
        params["testSn"] = sn
        params["testMac"] = mac
        params["testCpuId"] = cpuId
        return params
    }

    /**
     * 设备鉴权登录
     */
    private fun deviceAuth() {
        if (Config.getInstance().deviceAuthSN.length < 48) {
            return
        }
        val account: String = Config.getInstance().deviceAuthSN.substring(8, 14)
        val password: String = Config.getInstance().deviceAuthSN.substring(40, 48)
        val params = JSONObject()
        params["id"] = account
        params["password"] = password
        HttpHelper.getInstance().postApi("getinfo", params, object : Callback {
            override fun onFailure(call: Call, e: IOException) {

            }

            override fun onResponse(call: Call, response: Response) {
                val jsonObject: JSONObject?
                try {
                    val result = response.body!!.string()
                    val responseData = NativeLib.getResponseData(this@SplashActivity, result)
                    jsonObject = JSONObject.parseObject(responseData)
                } catch (e: Exception) {
                    Log.e("UserHelper", "api接口返回异常:" + call.request().url)
                    return
                }
                if (jsonObject == null) {
                    return
                }

                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    logDeviceInfo()
                    EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.CHECK_TESTABLE))
                    return
                }

                val reData = jsonObject.getString("reData")
                val respJson = JSONObject.parseObject(reData)
                val token = respJson.getString("token")
                SPUtils.put(this@SplashActivity, Constants.SP_KEY_TOKEN, token)
                EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.GET_USER_INFO))
            }
        })
    }

    /**
     * 上传设备信息
     */
    private fun logDeviceInfo() {
        val params = JSONObject()
        params["cpuId"] = DeviceIdUtil.getCPUId()
        params["sn"] = DeviceIdUtil.getSN()
        params["mac"] = DeviceIdUtil.getMac()
        HttpHelper.getInstance().postApi("logdeviceinfo", params, object : Callback {
            override fun onFailure(call: Call, e: IOException) {

            }

            override fun onResponse(call: Call, response: Response) {

            }
        })
    }

    private fun getIpInfo() {
        CoroutineScope(Dispatchers.IO).launch {
            var ipJSONObject: JSONObject? = null
            val diffTimeLimit = 259200000//3*24*60*60*1000

            val request: Request = Request.Builder().url(Constants.IP_STRING).build()
            try {
                val client =
                    HttpHelper.getInstance().client.newBuilder().callTimeout(5, TimeUnit.SECONDS)
                        .build()
                val res = client.newCall(request).execute()
                val ipString = res.body!!.string()

                ipJSONObject = JSONObject.parseObject(ipString)
                res.close()
            } catch (e: Exception) {
                Logger.e("" + e.message)
            }

            // 标记是否走了自己的接口
            var myip = false
            if (ipJSONObject == null || TextUtils.isEmpty(ipJSONObject.getString("countryCode"))) {
                try {
                    val pi: PackageInfo = packageManager.getPackageInfo(packageName, 0)
                    val nativeVersionCode = pi.versionCode
                    val ipInfo = Utils.getIpInfo(packageName, nativeVersionCode, 1, 5000)
                    ipJSONObject = JSONObject.parseObject(ipInfo)
                    myip = true
                } catch (e: Exception) {
                    Logger.e("" + e.message)
                }
            }

            // 时间校对
            if (ipJSONObject != null) {
                try {
                    val sysTimeStr = ipJSONObject.getString("currentTime")
                    val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ", Locale.getDefault())
                    val sysDate = sdf.parse(sysTimeStr)
                    if (sysDate != null) {
                        val now = Date()
                        if (abs(sysDate.time - now.time) > diffTimeLimit) {
                            withContext(Dispatchers.Main) { showTimeErrorDialog() }
                            return@launch
                        }
                    }
                } catch (e: Exception) {
                    Logger.e("" + e.message)
                }
            }

            // 没有ip信息或者中国地区，不能使用
            if (ipJSONObject == null || TextUtils.isEmpty(ipJSONObject.getString("countryCode"))) {

                withContext(Dispatchers.Main) {
                    val ipErrorAlert = AlertDialog.Builder(this@SplashActivity, R.style.XUpdate_DialogTheme)
                        .setMessage(getString(R.string.ip_error))
                        .setPositiveButton("OK") { _, _ -> finish() }.setCancelable(false).create()
                    ipErrorAlert.show()
                    setOKFocus(ipErrorAlert)
                }
            } else if (ipJSONObject.getString("countryCode").uppercase() == "CN") {
                val ip: String = if (myip) {
                    "(${ipJSONObject["query"]})"
                } else {
                    "${ipJSONObject["query"]}"
                }

                withContext(Dispatchers.Main) {
                    val ipErrorAlert = AlertDialog.Builder(this@SplashActivity, R.style.XUpdate_DialogTheme)
                        .setMessage(getString(R.string.region_error, ip))
                        .setPositiveButton("OK") { _, _ -> finish() }.setCancelable(false).create()
                    ipErrorAlert.show()
                    setOKFocus(ipErrorAlert)
                }
            } else if (ipJSONObject.getString("countryCode").uppercase() != "US") {
                val ip: String = if (myip) {
                    "(${ipJSONObject["query"]})"
                } else {
                    "${ipJSONObject["query"]}"
                }

                withContext(Dispatchers.Main) {
                    val ipErrorAlert = AlertDialog.Builder(this@SplashActivity, R.style.XUpdate_DialogTheme)
                        .setMessage(getString(R.string.region_error_us))
                        .setPositiveButton("OK") { _, _ -> finish() }.setCancelable(false).create()
                    ipErrorAlert.show()
                    setOKFocus(ipErrorAlert)
                }
            } else {
                SPUtils.put(this@SplashActivity, Constants.SP_KEY_IP, ipJSONObject.toJSONString())
                EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.GET_BASE_URL))
            }
        }
    }

    private fun showTimeErrorDialog() {
        if (alter != null && alter!!.isShowing) {
            return
        }
        val now = Date()
        val sdf = SimpleDateFormat("MMM d, yyyy", Locale.getDefault())
        Config.getInstance().release()
        alter = AlertDialog.Builder(this@SplashActivity, R.style.XUpdate_DialogTheme)
            .setMessage(
                "Please make sure your device Date and Time are set correctly.\n${
                    sdf.format(
                        now
                    )
                }"
            )
            .setNegativeButton("TRY AGAIN") { _, _ ->
                alter!!.dismiss()
                onResume()
            }
            .setPositiveButton("OPEN DATE SETTINGS") { _, _ ->
                alter!!.dismiss()
                startActivity(Intent(Settings.ACTION_DATE_SETTINGS))
            }
            .setCancelable(false)
            .create()

        alter!!.show()

        val positiveButton: Button = alter!!.getButton(AlertDialog.BUTTON_POSITIVE)
        val negativeButton: Button = alter!!.getButton(AlertDialog.BUTTON_NEGATIVE)

        settingButton(positiveButton)
        settingButton(negativeButton)

        negativeButton.requestFocus()
    }

    private fun setOKFocus(dialog: AlertDialog) {
        val button = dialog.getButton(AlertDialog.BUTTON_POSITIVE)
        button.requestFocus()
    }

}


enum class LoadInfo(val percent: Int, val tips: String) {
    CHECK_NET_WORK(0, "Checking Network0"), //检查网络
    GET_IP_INFO(5, "Checking Network"),  // 获取ip信息
    GET_BASE_URL(10, "Time Calibration"), // 获取baseUrl
    CHECK_VERSION(12, "Checking Version"), // 检查版本
    CHECK_DEVICE_AUTH(15, "Refreshing DNS"), // 检查是否设备鉴权
    DEVICE_LOGIN(20, "Establishing Encrypted Channel 1"), //设备登录
    CHECK_TESTABLE(20, "Establishing Encrypted Channel 2"), // 检查是否有测试账号
    TEST_LOGIN(30, "Establishing Encrypted Channel 3"), //测试登录
    GET_USER_INFO(40, "Iterative Transmission"), //获取用户信息
    GET_RESOURCE(60, "Receiving Data"), // 下载资源
    UNZIP_DB(90, "Data Verification"), // 插入数据库
    COMPLETE(100, "Completed"), //完成
}

class SplashLoadingEvent(val loadInfo: LoadInfo)