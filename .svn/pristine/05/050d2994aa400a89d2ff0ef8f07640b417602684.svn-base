package com.google.chuangke.common

import android.app.Activity
import android.app.ActivityManager
import android.app.AlertDialog
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import androidx.appcompat.app.AppCompatActivity
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication.Companion.context
import com.google.chuangke.R
import com.google.chuangke.common.event.AttentionInfoEvent
import com.google.chuangke.common.event.ContactInfoEvent
import com.google.chuangke.common.event.ExitEvent
import com.google.chuangke.common.event.PlayerStatusEvent
import com.google.chuangke.common.event.UserInfoEvent
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.ext.toast
import com.google.chuangke.http.HttpCallback
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.page.LoadInfo
import com.google.chuangke.page.LoginActivity
import com.google.chuangke.page.SplashLoadingEvent
import com.google.chuangke.player.LivePlayHelper
import com.google.chuangke.player.PlayerHelper
import com.google.chuangke.util.FileHelper
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import gojson.gojson.Gojson
import org.greenrobot.eventbus.EventBus
import kotlin.system.exitProcess

class UserHelper {

    companion object {
        fun getInstance() = InstanceHelper.instance
    }

    object InstanceHelper {
        val instance = UserHelper()
    }


    fun getUserInfo(activity: Activity) {
        HttpHelper.getInstance().postApi("userinfo", JSONObject(), object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code == 2) { //账号过期
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    activity.runOnUiThread {
                        AlertDialog.Builder(activity, R.style.XUpdate_DialogTheme)
                            .setMessage(msg)
                            .setPositiveButton("OK") { _, _ ->
                                activity.startActivity(Intent(activity, LoginActivity::class.java))
                                activity.finish()
                            }
                            .setCancelable(false)
                            .create()
                            .show()
                    }
                    return
                }

                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    SPUtils.removeSync(activity, Constants.SP_KEY_USER_INFO)
                    SPUtils.removeSync(activity, Constants.SP_KEY_TOKEN)
                    activity.runOnUiThread {
                        AlertDialog.Builder(activity, R.style.XUpdate_DialogTheme)
                            .setMessage(msg)
                            .setPositiveButton("Exit") { _, _ -> exit() }
                            .setCancelable(false)
                            .create()
                            .show()
                    }
                    return
                }

                val reData = jsonObject.getString("reData")
                SPUtils.putObject(context, Constants.SP_KEY_USER_INFO, reData)
                Config.getInstance().resourceVersion = jsonObject.getJSONObject("reData").getString("resource_version")
                EventBus.getDefault().post(UserInfoEvent())

                // gojson 初始化
                Gojson.setOption("run_path", context.filesDir.path)
                Gojson.setOption("auth_model", "loop")
                Gojson.setOption("auth_url", Config.getInstance().baseUrl)
                Gojson.setOption("account", SPUtils.getString(context, Constants.SP_KEY_TOKEN, ""))
                val i =Gojson.init { res: Long, msg: String? ->
                    if (res == -2L) {
                        Logger.e(msg!!)
                        EventBus.getDefault().post(PlayerStatusEvent(327, 0)) //异常结束
                    }
                }
                Logger.e("$i")

                EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.GET_RESOURCE))
            }

            override fun onError(err: String) {
                activity.runOnUiThread { activity.toast(err) }
            }
        })
    }

    fun lazyLoad() {
        val userInfo = SPUtils.getObject(context, Constants.SP_KEY_USER_INFO) as String
        val userObject = JSONObject.parseObject(userInfo)
        val serverResourceVersion = userObject.getString("resource_version")
        val nativeResourceVersion = SPUtils.getString(context, Constants.SP_KEY_RESOURCE_VERSION, "")

        // 资源版本不变则不重新获取
        if (serverResourceVersion.equals(nativeResourceVersion)){
            return
        }

        getFAQ()
        getContactList()
        getAttention()
        getNotification()

        SPUtils.putString(context, Constants.SP_KEY_RESOURCE_VERSION, serverResourceVersion)
    }

    private fun getFAQ() {
        val jsonObject = JSONObject()
        jsonObject["category"] = "live"
        HttpHelper.getInstance().postApi("getFaqList", jsonObject, object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    Logger.e(msg)
                    return
                }
                val reData = jsonObject.getString("reData")
                SPUtils.putString(context, Constants.SP_KEY_FAQ, reData)
            }

            override fun onError(err: String) {
                Logger.e(err)
            }
        })
    }

    private fun getNotification() {
        val jsonObject = JSONObject()
        HttpHelper.getInstance().postApi("getNotificationList", jsonObject, object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    Logger.e(msg)
                    return
                }
                val reData = jsonObject.getString("reData")
                SPUtils.putString(context, Constants.SP_KEY_NOTIFICATION, reData)
            }

            override fun onError(err: String) {
                Logger.e(err)
            }
        })
    }

    private fun getAttention() {
        val params = JSONObject()
        params["appPackage"] = context.packageName
        HttpHelper.getInstance().postApi("getAttentionList", params, object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    Logger.e(msg)
                    return
                }
                val reData = jsonObject.getString("reData")

                try {
                    val jsonArray = JSONArray.parseArray(reData)
                    if (jsonArray.size > 0) {
                        val attention = (jsonArray[0] as JSONObject).getString("attentionInfo")
                        SPUtils.putString(context, Constants.SP_KEY_ATTENTION, attention)
                        EventBus.getDefault().postSticky(AttentionInfoEvent())
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

            override fun onError(err: String) {
                Logger.e(err)
            }
        })
    }

    private fun getContactList() {
        HttpHelper.getInstance().postApi("getContactList", JSONObject(), object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    Logger.e(msg)
                    return
                }
                val reData = jsonObject.getString("reData")
                SPUtils.putString(context, Constants.SP_KEY_CONTACT_US, reData)
                EventBus.getDefault().postSticky(ContactInfoEvent())
            }

            override fun onError(err: String) {
                Logger.e(err)
            }
        })
    }

    fun addOperationLog(channelBean: ChannelBean) {
        try {
            val id = (channelBean.name + System.currentTimeMillis()).hashCode()

            val optData = JSONObject()
            optData["channelId"] = channelBean.channelId
            optData["channelName"] = channelBean.name
            optData["duration"] = 0
            optData["channelNumber"] = channelBean.channelNumber
            optData["tag"] = channelBean.tags
            val optJson = JSONObject()
            optJson["optType"] = "live"
            optJson["id"] = id
            optJson["optData"] = optData.toJSONString()
            GlobalThreadPools.getInstance().execute {
                HttpHelper.getInstance().postApi("operationlog", optJson, object : HttpCallback() {
                    override fun onSuccess(jsonObject: JSONObject) {
                        super.onSuccess(jsonObject)
                        val code = jsonObject.getInteger("code")
                        if (code != 1) {
                            val reData = jsonObject.getJSONObject("reData")
                            val msg = reData.getString("msg")
                            Logger.e(msg)
                            return
                        }
                    }

                    override fun onError(err: String) {
                        Logger.e(err)
                    }
                })
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun addErrorLog(
        channelId: String,
        channelNumber: Int,
        name: String?,
        code: Int?,
        errorLog: String?,
        description: String?
    ) {
        try {
            if (TextUtils.isEmpty(channelId)) return
            if (TextUtils.isEmpty(SPUtils.getString(context, Constants.SP_KEY_TOKEN, ""))) return

            val json = JSONObject()
            json["channelId"] = channelId
            json["channelNumber"] = channelNumber
            json["name"] = name
            json["code"] = code
            json["errorLog"] = errorLog
            json["description"] = description
            GlobalThreadPools.getInstance().execute {
                HttpHelper.getInstance().postApi("loglive", json, object : HttpCallback() {
                    override fun onSuccess(jsonObject: JSONObject) {
                        super.onSuccess(jsonObject)
                        if (jsonObject.getInteger("code") != 1) {
                            val reData = jsonObject.getJSONObject("reData")
                            val msg = reData.getString("msg")
                            Logger.e(msg)
                            return
                        }
                    }

                    override fun onError(err: String) {
                        Logger.e(err)
                    }
                })
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun exit() {
        PlayerHelper.getInstance().release()
        Config.getInstance().release()
        DBApi.getInstance().release()
        HttpHelper.getInstance().release()
        LivePlayHelper.getInstance().release()

        val activityManager =
            context.getSystemService(AppCompatActivity.ACTIVITY_SERVICE) as ActivityManager
        val appTaskList = activityManager.appTasks
        for (appTask in appTaskList) {
            appTask.finishAndRemoveTask()
        }

        exitProcess(0)
    }
}
