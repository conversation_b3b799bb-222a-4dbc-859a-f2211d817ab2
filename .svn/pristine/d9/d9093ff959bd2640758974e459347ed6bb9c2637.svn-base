package com.google.chuangke.page.dialog.feedback

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.View
import android.widget.Toast
import androidx.leanback.widget.VerticalGridView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.fastjson.JSONObject
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Config
import com.google.chuangke.common.EventFilter
import com.google.chuangke.common.event.SearchEvent
import com.google.chuangke.http.HttpCallback
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.page.adapter.RecyclerExtras
import com.google.chuangke.page.dialog.FeedbackDismissEvent
import com.google.chuangke.page.dialog.FeedbackItemFocusEvent
import com.google.chuangke.page.dialog.FeedbackTabEvent
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class FeedbackFragment : BaseFragment() {
    private lateinit var recyclerView: VerticalGridView
    private lateinit var feedbackAdapter: FeedbackAdapter

    override fun layoutId(): Int {
        return R.layout.fragment_feedback_feedback
    }

    override fun initView(view: View) {
        recyclerView = view.findViewById(R.id.rv_fragment_feedback)

        val tabTitles = arrayOf(
            getString(R.string.fragment_feedback_options1),
            getString(R.string.fragment_feedback_options2),
            getString(R.string.fragment_feedback_options3),
            getString(R.string.fragment_feedback_options4),
            getString(R.string.fragment_feedback_options5),
            getString(R.string.fragment_feedback_options6),
            getString(R.string.fragment_feedback_options7)
        )
        val list = ArrayList<JSONObject>()
        for (i in tabTitles.indices) {
            val jsonObject = JSONObject()
            jsonObject["title"] = tabTitles[i]
            jsonObject["id"] = i + 1
            list.add(jsonObject)
        }

        recyclerView.setOnKeyInterceptListener { event ->
            if (event.action != KeyEvent.ACTION_UP && event.keyCode == KeyEvent.KEYCODE_SEARCH) {
                EventBus.getDefault().post(SearchEvent())
                return@setOnKeyInterceptListener true
            }

            return@setOnKeyInterceptListener false
        }

        recyclerView.layoutManager = LinearLayoutManager(requireActivity())
        recyclerView.adapter = FeedbackAdapter(recyclerView).also { feedbackAdapter = it }
        feedbackAdapter.setList(list)
    }


    override fun initListener() {
        feedbackAdapter.setOnItemKeyListener(object : RecyclerExtras.OnItemKeyListener {
            override fun onItemKey(view: View, position: Int, keyEvent: KeyEvent, keyCode: Int) {
//                val title = feedbackAdapter.getItem(position).getString("title")
//                feedback(title)

            }
        })
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFeedbackItemForceEvent(event: FeedbackItemFocusEvent) {
        if (event.index == 0) {
            recyclerView.getChildAt(0).requestFocus()
        }
    }
}

class FeedbackAdapter(recyclerView: RecyclerView) :
    BaseQuickAdapter<JSONObject, BaseViewHolder>(R.layout.item_fragment_feedback) {

    private var eventFilter =
        EventFilter.Builder().setType(EventFilter.TYPE_OUT_TIME).setTime(500).build()
    var rv: RecyclerView = recyclerView
    private var onItemKeyListener: RecyclerExtras.OnItemKeyListener? = null

    fun setOnItemKeyListener(onItemKeyListener: RecyclerExtras.OnItemKeyListener?) {
        this.onItemKeyListener = onItemKeyListener
    }

    override fun convert(holder: BaseViewHolder, item: JSONObject) {
        holder.setText(R.id.tv_item_fragment_feedback_id, item.getString("id"))
        holder.setText(R.id.tv_item_fragment_feedback_title, item.getString("title"))

        holder.itemView.setOnKeyListener(object : View.OnKeyListener {
            override fun onKey(view: View, i: Int, keyEvent: KeyEvent): Boolean {
                onItemKeyListener?.onItemKey(view, holder.layoutPosition, keyEvent, i)

                if (keyEvent.action == KeyEvent.ACTION_UP) {
                    return false
                }

                if (i == KeyEvent.KEYCODE_BACK) {
                    EventBus.getDefault().post(FeedbackDismissEvent())
                    return true
                }

                if (i == KeyEvent.KEYCODE_DPAD_LEFT
                    || i == KeyEvent.KEYCODE_DPAD_DOWN && holder.layoutPosition == data.size - 1
                    || i == KeyEvent.KEYCODE_DPAD_UP && holder.layoutPosition == 0
                ) {
                    return true
                }

                if (i == KeyEvent.KEYCODE_DPAD_RIGHT) {
                    EventBus.getDefault().post(FeedbackTabEvent(1))
                    return true
                }
                return false
            }
        })

        holder.itemView.setOnClickListener {
            if (eventFilter.filter()) {
                feedback(item.getString("title"))
            }
        }

    }

    private fun feedback(content: String) {
        val lastFeedbackTime= SPUtils.getObject(context,"last_feedback_time", 0L)
        val lastFeedbackTimeLong = lastFeedbackTime as Long
        if ((System.currentTimeMillis() - lastFeedbackTimeLong) < 1000 * 13) {
            Handler(Looper.getMainLooper()).postDelayed({
                Toast.makeText(MyApplication.context, "Successful", Toast.LENGTH_SHORT).show()
                EventBus.getDefault().post(FeedbackDismissEvent())
            }, 500)
            return
        }

        val channelBean = Config.getInstance().currentPlayChannel
        val json = JSONObject()
        json["channelId"] = channelBean.channelId
        json["channelName"] = channelBean.name
        json["content"] = content
        json["type"] = 1// 0 vod, 1 live

        HttpHelper.getInstance().postApi("feedback", json, object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    Logger.e(msg)
                    return
                }
                Handler(Looper.getMainLooper()).post {
                    Toast.makeText(MyApplication.context, "Successful", Toast.LENGTH_SHORT).show()
                    EventBus.getDefault().post(FeedbackDismissEvent())
                }

                SPUtils.put(context, "last_feedback_time",System.currentTimeMillis())
            }

            override fun onError(err: String) {
                Logger.e(err)
            }
        })
    }
}