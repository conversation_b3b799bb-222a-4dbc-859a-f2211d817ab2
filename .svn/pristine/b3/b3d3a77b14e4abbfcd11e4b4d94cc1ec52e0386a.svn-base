<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:descendantFocusability="afterDescendants">

    <View
        android:id="@+id/view_dialog_background"
        android:layout_width="850dp"
        android:layout_height="450dp"
        android:background="@drawable/shape_dialog_epg"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_top"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@+id/view_dialog_background"
        app:layout_constraintStart_toStartOf="@+id/view_dialog_background"
        app:layout_constraintTop_toTopOf="@+id/view_dialog_background">

        <ImageView
            android:id="@+id/iv_channel_logo"
            android:layout_width="110dp"
            android:layout_height="96dp"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:contentDescription="@null"
            android:focusable="false"
            android:background="@drawable/shape_icon_bg"
            tools:src="@mipmap/dif_ic_logo_default" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="11dp"
            android:layout_weight="2"
            android:focusable="false"
            android:gravity="start"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_channel_name"
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="21dp"
                android:ellipsize="end"
                android:focusable="false"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:text="239TV ASHOOL" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:focusable="false"
                android:gravity="start"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:text="@string/dialog_epg_duration"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_epg_duration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:textColor="@color/white_ccc"
                    android:textSize="12sp"
                    tools:text="13:50-17:40" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48.5dp"
                    android:focusable="false"
                    android:text="@string/dialog_epg_program"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_epg_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:focusable="false"
                    android:maxLines="1"
                    android:textColor="@color/white_ccc"
                    android:textSize="12sp"
                    tools:text="A Critica Noticia -News" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_epg_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:focusable="false"
                android:gravity="start"
                android:maxLines="2"
                android:textColor="@color/white_ccc"
                android:textSize="11sp"
                tools:text="Acompanhe as principais noticias do dia para o esado do Amazon. " />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="30dp"
            android:layout_weight="1"
            android:focusable="false"
            android:gravity="center_vertical|end"
            android:orientation="vertical">

            <TextClock
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:format12Hour="hh:mm aa"
                android:format24Hour="HH:mm"
                android:textColor="@color/white"
                android:textSize="25sp"
                tools:text="16:55" />

            <TextClock
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:format12Hour="EEEE M/dd"
                android:format24Hour="EEEE M/dd"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:ignore="SmallSp"
                tools:text="Thursday 2/5" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_date"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="@+id/view_dialog_background"
        app:layout_constraintStart_toStartOf="@+id/view_dialog_background"
        app:layout_constraintTop_toBottomOf="@+id/ll_top"
        tools:layout_height="40dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:focusable="false"
            android:gravity="start"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:text="@string/dialog_epg_tip1"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:contentDescription="@null"
                    android:focusable="false"
                    android:src="@mipmap/ic_epg_up" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:focusable="false"
                    android:text="@string/dialog_epg_tip2"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:focusable="false"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:text="@string/dialog_epg_tip3"
                    android:textColor="@color/white"
                    android:textSize="12sp" />

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:contentDescription="@null"
                    android:focusable="false"
                    android:src="@mipmap/ic_epg_left" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:focusable="false"
                    android:text="@string/dialog_epg_tip4"
                    android:textColor="@color/white"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:focusable="false"
            android:gravity="end"
            android:text="@string/dialog_epg_date"
            android:textColor="@color/white"
            android:textSize="15sp" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="11dp"
            android:contentDescription="@null"
            android:focusable="false"
            android:src="@mipmap/ic_epg_date_left" />

        <androidx.leanback.widget.HorizontalGridView
            android:id="@+id/hg_date"
            android:layout_width="392dp"
            android:layout_height="40dp"
            android:descendantFocusability="blocksDescendants" />

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="30dp"
            android:contentDescription="@null"
            android:focusable="false"
            android:src="@mipmap/ic_epg_date_right" />
    </LinearLayout>

    <View
        android:id="@+id/view_line_time_top"
        android:layout_width="0dp"
        android:layout_height="0.3dp"
        android:layout_marginTop="5.5dp"
        android:background="@color/white_ccc"
        android:focusable="false"
        app:layout_constraintEnd_toEndOf="@+id/view_dialog_background"
        app:layout_constraintStart_toStartOf="@+id/view_dialog_background"
        app:layout_constraintTop_toBottomOf="@+id/ll_date" />

    <TextView
        android:id="@+id/tv_tag"
        android:layout_width="250dp"
        android:layout_height="25dp"
        android:focusable="false"
        android:gravity="center"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintStart_toStartOf="@id/view_dialog_background"
        app:layout_constraintTop_toBottomOf="@+id/view_line_time_top"
        tools:text="TOPNOVO(6/32)" />

    <View
        android:layout_width="0dp"
        android:layout_height="25dp"
        android:background="@color/white_20per"
        app:layout_constraintEnd_toEndOf="@+id/view_dialog_background"
        app:layout_constraintStart_toStartOf="@+id/view_dialog_background"
        app:layout_constraintTop_toBottomOf="@+id/view_line_time_top" />

    <View
        android:id="@+id/view_line_t1"
        android:layout_width="1dp"
        android:layout_height="5dp"
        android:layout_marginStart="249dp"
        android:background="@color/white"
        android:focusable="false"
        app:layout_constraintBottom_toTopOf="@+id/view_line_time_bottom"
        app:layout_constraintStart_toStartOf="@id/view_dialog_background" />

    <TextView
        android:id="@+id/tv_time_t1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/view_line_t1"
        app:layout_constraintEnd_toEndOf="@id/view_line_t1"
        app:layout_constraintStart_toStartOf="@id/view_line_t1"
        tools:text="15:00PM" />

    <View
        android:id="@+id/view_line_t2"
        android:layout_width="1dp"
        android:layout_height="5dp"
        android:layout_marginStart="119dp"
        android:background="@color/white"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@+id/view_line_t1"
        app:layout_constraintStart_toEndOf="@id/view_line_t1" />

    <TextView
        android:id="@+id/tv_time_t2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/view_line_t2"
        app:layout_constraintEnd_toEndOf="@id/view_line_t2"
        app:layout_constraintStart_toStartOf="@id/view_line_t2"
        tools:text="15:30PM" />

    <View
        android:id="@+id/view_line_t3"
        android:layout_width="1dp"
        android:layout_height="5dp"
        android:layout_marginStart="119dp"
        android:background="@color/white"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@+id/view_line_t2"
        app:layout_constraintStart_toEndOf="@id/view_line_t2" />

    <TextView
        android:id="@+id/tv_time_t3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/view_line_t3"
        app:layout_constraintEnd_toEndOf="@id/view_line_t3"
        app:layout_constraintStart_toStartOf="@id/view_line_t3"
        tools:text="16:00PM" />

    <View
        android:id="@+id/view_line_t4"
        android:layout_width="1dp"
        android:layout_height="5dp"
        android:layout_marginStart="119dp"
        android:background="@color/white"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@+id/view_line_t3"
        app:layout_constraintStart_toEndOf="@id/view_line_t3" />

    <TextView
        android:id="@+id/tv_time_t4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/view_line_t4"
        app:layout_constraintEnd_toEndOf="@id/view_line_t4"
        app:layout_constraintStart_toStartOf="@id/view_line_t4"
        tools:text="16:30PM" />

    <View
        android:id="@+id/view_line_t5"
        android:layout_width="1dp"
        android:layout_height="5dp"
        android:layout_marginStart="119dp"
        android:background="@color/white"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="@+id/view_line_t4"
        app:layout_constraintStart_toEndOf="@id/view_line_t4" />

    <TextView
        android:id="@+id/tv_time_t5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:textColor="@color/white"
        android:textSize="12sp"
        app:layout_constraintBottom_toTopOf="@id/view_line_t5"
        app:layout_constraintEnd_toEndOf="@id/view_line_t5"
        app:layout_constraintStart_toStartOf="@id/view_line_t5"
        tools:text="17:00PM" />

    <View
        android:id="@+id/view_line_time_bottom"
        android:layout_width="0dp"
        android:layout_height="0.3dp"
        android:background="@color/white_ccc"
        android:focusable="false"
        app:layout_constraintEnd_toEndOf="@+id/view_dialog_background"
        app:layout_constraintStart_toStartOf="@+id/view_dialog_background"
        app:layout_constraintTop_toBottomOf="@+id/tv_tag" />

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/vg_tag"
        android:layout_width="125dp"
        android:layout_height="0dp"
        android:descendantFocusability="blocksDescendants"
        app:layout_constraintBottom_toBottomOf="@+id/view_dialog_background"
        app:layout_constraintStart_toStartOf="@+id/view_dialog_background"
        app:layout_constraintTop_toBottomOf="@+id/view_line_time_bottom" />

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/vg_epg"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@+id/view_dialog_background"
        app:layout_constraintEnd_toEndOf="@id/view_dialog_background"
        app:layout_constraintStart_toEndOf="@+id/vg_tag"
        app:layout_constraintTop_toBottomOf="@+id/tv_tag" />

    <ImageView
        android:id="@+id/iv_indicator"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:layout_marginStart="125dp"
        android:layout_marginBottom="-4dp"
        android:focusable="false"
        android:scaleType="fitXY"
        android:src="@drawable/programguide_img_epg_line"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/vg_epg"
        app:layout_constraintStart_toStartOf="@+id/vg_epg"
        app:layout_constraintTop_toTopOf="@id/view_line_t1"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>