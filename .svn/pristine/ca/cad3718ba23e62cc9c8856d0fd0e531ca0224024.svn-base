package com.google.chuangke.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import io.objectbox.annotation.Index
import io.objectbox.annotation.Transient
import java.io.Serializable

@Entity
class ChannelBean : Serializable {

    @Id(assignable = true)
    var id: Long? = null // unid

    @Index
    var channelId: Long? = null // sql id
    var name: String? = null

    @Index
    var channelNumber: Int? = null
    var tags: String? = null
    var intOrder: Int? = null
    var playback: Int? = null
    var countryCode: String? = null
    var uid: String? = null
    var wid: String? = null
    var image: String? = null
    var content: String? = null

    @Transient
    var collection: Int? = null

    @Transient
    var locked: Int? = null

    override fun toString(): String {
        return "ChannelBean{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", channelNumber=" + channelNumber +
                ", tags=" + tags +
                '}'
    }
}