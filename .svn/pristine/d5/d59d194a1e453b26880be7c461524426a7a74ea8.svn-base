<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_channel_tag"
        android:layout_width="190dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:nextFocusRight="@id/rv_fragment_channel_channel"
        android:paddingTop="26dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_fragment_channels_channel"
        android:layout_width="375dp"
        android:layout_height="46dp"
        android:background="@color/bg_parent"
        android:focusable="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="36dp"
        android:paddingTop="18dp"
        android:paddingEnd="20dp"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_tag"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_fragment_channels_channel_tag"
            style="@style/font_bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:focusable="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="ALL" />

        <TextView
            android:id="@+id/tv_fragment_channels_channel_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:focusable="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="1/365" />

    </LinearLayout>

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_channel_channel"
        android:layout_width="375dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:nextFocusLeft="@id/rv_fragment_channel_tag"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_tag"
        app:layout_constraintTop_toBottomOf="@+id/ll_fragment_channels_channel" />

</androidx.constraintlayout.widget.ConstraintLayout>