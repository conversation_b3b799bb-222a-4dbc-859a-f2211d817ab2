<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!--未加载的进度区域-->
    <item android:id="@android:id/background">
        <shape>
            <!--进度条的圆角-->
            <corners android:radius="3.5dp" />
            <!--未加载的进度区域颜色-->
            <solid android:color="#30FFFFFF" />
        </shape>
    </item>
    <!--缓冲的进度的颜色，一般视频播放的缓冲区域-->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape>
                <!--进度条的圆角-->
                <corners android:radius="3.5dp" />
                <!--缓冲的进度的颜色，一般视频播放的缓冲进度-->
                <solid android:color="#30FFFFFF" />
            </shape>
        </clip>
    </item>
    <!--已经加载完的进度的区域-->
    <item android:id="@android:id/progress">
        <clip>
            <shape>
                <!--进度条的圆角-->
                <corners android:radius="3.5dp" />
                <!--已经加载完的进度的颜色-->
                <solid android:color="@color/purpler_7B0B82" />
            </shape>
        </clip>
    </item>
</layer-list>