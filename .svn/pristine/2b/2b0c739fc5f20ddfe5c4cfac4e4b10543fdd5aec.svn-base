package com.google.chuangke.player.tvbus;

import com.alibaba.fastjson.JSONObject;
import com.google.chuangke.MyApplication;
import com.google.chuangke.common.Constants;
import com.google.chuangke.util.SPUtils;
import com.orhanobut.logger.Logger;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 用枚举实现单例
 */
public enum LivePlayRestartTask {
    INSTANCE;

    private final Timer timer = new Timer();
    private TimerTask timerTask;

    private final static int DEFAULT_RESTART_DELAY_MAX = 120_000; // 最大重播间隔,2分钟
    private final static int DEFAULT_RESTART_DELAY_INCREASE = 10_000; // 重播间隔增加量，10秒
    private final static int DEFAULT_RESTART_DELAY_MIN = 20_000; // 最小（第一次）重播间隔, 20秒

    private int restart_delay_max;
    private int restart_delay_increase;
    private int restart_delay_min;
    private int restartDelayTime;

    // 初始化配置，如果服务器上有配置则使用服务器上的
    LivePlayRestartTask() {
        restart_delay_min = DEFAULT_RESTART_DELAY_MIN;
        restart_delay_increase = DEFAULT_RESTART_DELAY_INCREASE;
        restart_delay_max = DEFAULT_RESTART_DELAY_MAX;
        try {
            String userInfo = (String) SPUtils.Companion.getObject(MyApplication.context, Constants.SP_KEY_USER_INFO);
            String live_play_retry_time = JSONObject.parseObject(userInfo).getString("live_play_retry_time");
            if (live_play_retry_time != null) {
                String[] timeArr = live_play_retry_time.split(",");
                if (timeArr.length >= 1) {
                    restart_delay_min = Integer.parseInt(timeArr[0].trim());
                }
                if (timeArr.length >= 2) {
                    restart_delay_increase = Integer.parseInt(timeArr[1].trim());
                }
                if (timeArr.length >= 3) {
                    restart_delay_max = Integer.parseInt(timeArr[2].trim());
                }
            }
        } catch (Exception e) {
            Logger.e(e.getMessage());
        }
        restartDelayTime = restart_delay_min;
    }

    public void cancel() {
        restartDelayTime = restart_delay_min;
        if (timerTask != null) {
            timerTask.cancel();
        }
    }

    public void schedule(Runnable runnable) {
        if (timerTask != null) {
            timerTask.cancel();
        }
        timerTask = new TimerTask() {
            @Override
            public void run() {
                runnable.run();
            }
        };
        timer.schedule(timerTask, restartDelayTime);

        if (restartDelayTime >= restart_delay_max) {
            restartDelayTime = restart_delay_max;
        } else {
            restartDelayTime += restart_delay_increase;
        }
    }
}
