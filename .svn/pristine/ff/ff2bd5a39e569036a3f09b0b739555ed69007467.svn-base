package com.google.chuangke.common;


import com.orhanobut.logger.Logger;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 全局使用的线程池
 */
public class GlobalThreadPools {
    private static ExecutorService THREAD_POOL_EXECUTOR;//线程池
    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();//CPU数量
    private static final int CORE_POOL_SIZE = CPU_COUNT;//核心线程数
    private static final int MAXIMUM_POOL_SIZE = CPU_COUNT * 2;//最大线程数
    private static final int KEEP_ALIVE_SECONDS = 60;//线程闲置后的存活时间
    private static final BlockingQueue<Runnable> sPoolWorkQueue = new LinkedBlockingQueue<>(CPU_COUNT);//任务队列
    private static final ThreadFactory sThreadFactory = new ThreadFactory() {//线程工厂
        private final AtomicInteger mCount = new AtomicInteger(1);

        public Thread newThread(Runnable r) {
            return new Thread(r, "MangoTask #" + mCount.getAndIncrement());
        }
    };

    //初始化线程池
    private void initThreadPool() {
        THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(
                CORE_POOL_SIZE, MAXIMUM_POOL_SIZE, KEEP_ALIVE_SECONDS, TimeUnit.SECONDS,
                sPoolWorkQueue, sThreadFactory, new RejectedHandler()) {
            @Override
            public void execute(Runnable command) {
                super.execute(command);
            }
        };
    }

    private static class RejectedHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            Logger.e("线程池初始化成功");
        }
    }

    private static GlobalThreadPools instance;

    private GlobalThreadPools() {
        initThreadPool();
    }

    private static final class InstanceHolder {
        static final GlobalThreadPools instance = new GlobalThreadPools();
    }

    public static GlobalThreadPools getInstance() {
        return InstanceHolder.instance;
    }


    public void execute(Runnable command) {
        THREAD_POOL_EXECUTOR.execute(command);
    }


}