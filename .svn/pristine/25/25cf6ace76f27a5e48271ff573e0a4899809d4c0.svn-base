package com.google.chuangke.player.multiple;

import androidx.media3.common.MediaItem;
import androidx.media3.common.MimeTypes;
import androidx.media3.common.PlaybackException;
import androidx.media3.common.Player;
import androidx.media3.datasource.DefaultHttpDataSource;
import androidx.media3.datasource.HttpDataSource;
import androidx.media3.exoplayer.DefaultLoadControl;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.exoplayer.dash.DashMediaSource;
import androidx.media3.exoplayer.source.BehindLiveWindowException;
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory;
import androidx.media3.exoplayer.source.MediaSource;
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector;
import androidx.media3.ui.PlayerView;

import android.annotation.SuppressLint;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;

import com.google.chuangke.MyApplication;
import com.google.chuangke.player.FfmpegRenderersFactory;
import com.orhanobut.logger.Logger;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayDeque;
import java.util.HashMap;
import java.util.Queue;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SuppressLint("UnsafeOptInUsageError")
public class ExoPlayerPool {

    private final Queue<ExoPlayer> playerQueue;
    private final int poolSize;
    MediaSource mediaSource;

    private static final class InstanceHolder {
        public static final ExoPlayerPool instance = new ExoPlayerPool(10);
    }

    public static ExoPlayerPool getInstance() {
        return InstanceHolder.instance;
    }


    private ExoPlayerPool( int poolSize) {
        this.poolSize = poolSize;
        this.playerQueue = new ArrayDeque<>(poolSize);
    }

    private ExoPlayer createPlayer() {
        DefaultTrackSelector trackSelector = new DefaultTrackSelector(MyApplication.context);
        trackSelector.setParameters(
                trackSelector.getParameters().buildUpon()
                        .setPreferredAudioMimeType("audio/mp4a-latm") // 默认使用aac音轨
                        .setPreferredAudioLanguage("en")
                        .setForceLowestBitrate(true)
        );
        ExoPlayer exoPlayer = new ExoPlayer.Builder(MyApplication.context, new FfmpegRenderersFactory(MyApplication.context))
                .setTrackSelector(trackSelector)
                .setLoadControl(new DefaultLoadControl.Builder()
                        .setBufferDurationsMs(
                                1000,  // 最小播放缓冲
                                2000,  // 最小暂停缓冲
                                500,   // 重新缓冲
                                500    // 目标缓冲
                        )
                        .build())
                .build();
        exoPlayer.setVolume(1f);
        return exoPlayer;
    }


    // 从池中获取一个播放器
    public ExoPlayer acquirePlayer(String provider, String playUrl, Long timestampMs, String mimeTypes, PlayerView playerView) {
        if (TextUtils.isEmpty(playUrl)){
            return null;
        }

        // addDataSourceHeader
        HttpDataSource.Factory dataSourceFactory = new DefaultHttpDataSource.Factory().setReadTimeoutMs(60000).setConnectTimeoutMs(60000);
        HashMap<String, String> header = new HashMap<>();
        Pattern pattern = Pattern.compile("/\\d+/\\w+/", Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(playUrl);
        if (matcher.find()) {
            String session = matcher.group(0);
            if (session != null) {
                MessageDigest md;
                try {
                    md = MessageDigest.getInstance("MD5");
                } catch (NoSuchAlgorithmException e) {
                    throw new RuntimeException(e);
                }
                byte[] digest = md.digest(session.getBytes());
                String se = String.format("%032x", new BigInteger(1, digest));
                header.put("session", se);
                dataSourceFactory.setDefaultRequestProperties(header);
            }
        }

        // create MediaSource
        if ("000".equals(provider)){
            mediaSource = new DashMediaSource.Factory(dataSourceFactory, playUrl).createMediaSource(MediaItem.EMPTY);
        }else{
            MediaItem.Builder mediaBuilder = new MediaItem.Builder().setUri(Uri.parse(playUrl));
            if (mimeTypes != null) {
                mediaBuilder.setMimeType(mimeTypes);
            }
            MediaItem mediaItem = mediaBuilder.build();
            mediaSource = new DefaultMediaSourceFactory(dataSourceFactory).createMediaSource(mediaItem);
        }

        playerView.setControllerAutoShow(false);
        playerView.setUseController(false);
        playerView.setKeepScreenOn(true);
        playerView.setKeepContentOnPlayerReset(false);
        playerView.getSubtitleView().setVisibility(PlayerView.GONE);

        ExoPlayer exoPlayer;

//        Log.e("======== playerQueue", ""+playerQueue.size());
        if (playerQueue.isEmpty()) { // 池子里面有就从池子拿，没有就创建一个，释放的时候归还到池子中
            exoPlayer = createPlayer();
        }else{
            exoPlayer = playerQueue.poll();
        }
        playerView.setPlayer(exoPlayer);
        exoPlayer.setMediaSource(mediaSource);
//        if (timestampMs != null){
//            exoPlayer.seekTo(timestampMs);
//        }
        exoPlayer.prepare();
        exoPlayer.setPlayWhenReady(true);

        return exoPlayer;
    }

    // 将播放器归还到池中
    public void releasePlayer(ExoPlayer player) {
        if (player != null) {
            player.stop();
            player.clearMediaItems();
            player.setVideoSurface(null); // 解绑 Surface
            player.setPlayWhenReady(false);
//            player.seekTo(0); // 重置播放位置
            if (playerQueue.size() < poolSize) { // 避免池子过大
                playerQueue.offer(player);
            } else {
                player.release(); // 如果池已满，直接释放
            }
        }
    }

    // 释放池中所有播放器
    public void releaseAllPlayers() {
        while (!playerQueue.isEmpty()) {
            ExoPlayer player = playerQueue.poll();
            if (player != null) {
                player.release();
            }
        }
    }
}