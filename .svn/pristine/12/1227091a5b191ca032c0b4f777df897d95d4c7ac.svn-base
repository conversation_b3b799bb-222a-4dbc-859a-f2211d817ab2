<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <uses-feature
        android:name="android.hardware.touchscreen"
        android:required="false" />
    <uses-feature
        android:name="android.software.leanback"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.microphone"
        android:required="false" />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:extractNativeLibs="true"
        android:banner="@drawable/banner"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:defaultToDeviceProtectedStorage="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        tools:targetApi="31">

        <activity
            android:name=".page.MainActivity"
            android:launchMode="singleTask"
            android:screenOrientation="landscape" />

        <activity
            android:name=".page.SplashActivity"
            android:exported="true"
            android:screenOrientation="landscape">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".page.LoginActivity"
            android:screenOrientation="landscape" />

        <activity
            android:name=".page.center.ChannelSearchActivity"
            android:theme="@style/Theme.AppCompat.Translucent" />

        <meta-data
            android:name="design_width_in_dp"
            android:value="960" />
        <meta-data
            android:name="design_height_in_dp"
            android:value="540" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <service android:name=".common.LiveService"/>

        <service android:name="com.i18n.langs.BrSi" />
    </application>

</manifest>