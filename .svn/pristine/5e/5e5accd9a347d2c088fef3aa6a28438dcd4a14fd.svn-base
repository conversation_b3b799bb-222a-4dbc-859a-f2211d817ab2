<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <SurfaceView
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/fl1"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:focusable="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.media3.ui.PlayerView
            android:id="@+id/playerView1"
            android:layout_width="480dp"
            android:layout_height="270dp"
            android:layout_gravity="center"
            android:focusable="false"
            app:surface_type="surface_view" />

        <ImageView
            android:id="@+id/add1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:focusable="false"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_add" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selector_multiple_screen"
            android:duplicateParentState="true" />

    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl2"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:focusable="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.media3.ui.PlayerView
            android:id="@+id/playerView2"
            android:layout_width="480dp"
            android:layout_height="270dp"
            android:layout_gravity="center"
            android:focusable="false"
            app:surface_type="surface_view" />

        <ImageView
            android:id="@+id/add2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:focusable="false"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_add" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selector_multiple_screen"
            android:duplicateParentState="true" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl3"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.media3.ui.PlayerView
            android:id="@+id/playerView3"
            android:layout_width="480dp"
            android:layout_height="270dp"
            android:layout_gravity="center"
            android:focusable="false"
            app:surface_type="surface_view" />

        <ImageView
            android:id="@+id/add3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:focusable="false"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_add" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selector_multiple_screen"
            android:duplicateParentState="true" />
    </FrameLayout>

    <FrameLayout
        android:id="@+id/fl4"
        android:layout_width="480dp"
        android:layout_height="270dp"
        android:background="@drawable/selector_multiple_screen"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.media3.ui.PlayerView
            android:id="@+id/playerView4"
            android:layout_width="480dp"
            android:layout_height="270dp"
            android:layout_gravity="center"
            android:focusable="false"
            app:surface_type="surface_view" />

        <ImageView
            android:id="@+id/add4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:focusable="false"
            android:importantForAccessibility="no"
            android:src="@drawable/ic_add" />

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selector_multiple_screen"
            android:duplicateParentState="true" />
    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>