package com.google.chuangke.page.menu.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.common.Config
import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import java.util.*

class ChannelSearchPresenter : BasePresenter<ChannelBean>() {

    override fun layoutId(): Int {
        return R.layout.item_search_channel
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {
        return false
    }

    override fun bindViewHolder(view: View, item: ChannelBean) {

        val tempTvNo = view.findViewById<TextView>(R.id.tv_item_channel_no)
        tempTvNo.text = item.channelNumber.toString()

        val tempTvName = view.findViewById<TextView>(R.id.tv_item_channel_title)
        tempTvName.text = item.name

        // 当前节目
        val currentEpg = DBApi.getInstance().getCurrentEpgList(item, Date()).let { epgList ->
            if (epgList.isNotEmpty()) {
                epgList[0].name
            } else {
                context.getString(R.string.empty_no_data)
            }
        }
        view.findViewById<TextView>(R.id.tv_item_channel_subtitle).text = currentEpg

        // 正在播放
        val currentPlayGifView = view.findViewById<ImageView>(R.id.iv_item_channel_live)
        Glide.with(context).asGif().load(R.drawable.ic_tv_playing).into(currentPlayGifView)

        // 显示正在播放
        currentPlayGifView.visibility =
            if (item.id == Config.getInstance().currentPlayChannel.id) View.VISIBLE
            else View.INVISIBLE

        // 台标
        Glide.with(context).load("${Config.getInstance().logoUrl}${item.channelId}.png")
            .error(R.mipmap.dif_ic_logo_default).diskCacheStrategy(DiskCacheStrategy.ALL)//缓存所有台标
            .into(view.findViewById(R.id.iv_item_channel_logo))

        // Lock
        if (item.locked == null) {
            val locked = DBApi.getInstance().isChannelLocked(item.id!!)
            item.locked = if (locked) 1 else 0
        }
        val ivLock = view.findViewById<ImageView>(R.id.iv_item_channel_lock)
        ivLock.visibility = if (item.locked == 1) View.VISIBLE
        else View.GONE

    }


}