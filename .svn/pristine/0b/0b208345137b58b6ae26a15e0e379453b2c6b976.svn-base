package com.google.chuangke.page.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import java.util.Date
import com.google.chuangke.page.ClassicEpgFragment
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.util.DateUtil

class EpgDayPresenter(
    private val classicEpgFragment: ClassicEpgFragment,
    var hasFocus: Boolean = false
) : BasePresenter<Date>() {

    override fun layoutId(): Int {
        return R.layout.item_dialog_epg_date
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {
        return false
    }

    override fun bindViewHolder(view: View, item: Date) {

        val day = view.findViewById<TextView>(R.id.tv_item_dialog_epg_date_no)
        day.text = DateUtil.getFormatString(item, "dd")

        val week = view.findViewById<TextView>(
            R.id.tv_item_dialog_epg_date_week
        )
        week.text = DateUtil.getFormatString(item, "E").uppercase()

        // 当天的做标注
        val date = DateUtil.getFormatString(item, "yyyy-MM-dd")
        val current = DateUtil.getFormatString(Date(), "yyyy-MM-dd")
        val bg = view.findViewById<View>(R.id.iv_ll_item_dialog_epg_date)
        bg.visibility = if (date == current) View.VISIBLE else View.INVISIBLE

        // 外部控制日期颜色
        if (hasFocus) {
            day.setTextColor(context.getColorStateList(R.color.programguide_selector_item_dialog_epg_date_text))
            week.setTextColor(context.getColorStateList(R.color.selector_item_dialog_epg_date_subtext))
        } else {
            val targetTime =
                DateUtil.getDMTime(
                    classicEpgFragment.currentDate.toInstant().toEpochMilli(),
                    "yyyy-MM-dd"
                )

            day.setTextColor(context.getColor(if (date == targetTime) R.color.purpler_7B0B82 else R.color.white))
            week.setTextColor(context.getColor(if (date == targetTime) R.color.purpler_7B0B82 else R.color.white))
        }
    }

}