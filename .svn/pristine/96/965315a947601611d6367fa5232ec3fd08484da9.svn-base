package com.google.chuangke.page.menu.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.entity.CustomTagBean

class CustomTagPresenter : BasePresenter<CustomTagBean>() {

    override fun layoutId(): Int {
        return R.layout.item_custom_tag
    }

    override fun addFocusTextStyle(): List<Int> {
        return emptyList()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean {
        return false
    }

    override fun bindViewHolder(view: View, item: CustomTagBean) {
        (view as TextView).text = item.name
        view.let {
            it.text = item.name
            it.setCompoundDrawablesWithIntrinsicBounds(
                if (item.isSelect) R.drawable.selector_checkbox_checked else R.drawable.selector_checkbox,
                0,
                0,
                0
            )
        }
    }
}