package com.google.chuangke.data

import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelLockBean
import com.google.chuangke.entity.TagBean
import com.orhanobut.logger.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

class ChannelRepository(private val mDBApi: DBApi) {

    suspend fun getTagList(): List<TagBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.allTag
        }
    }

    suspend fun getTagListWithoutLock(): List<TagBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.getTagWithoutLock()
        }
    }

    suspend fun getChannelList(tagBean: TagBean): MutableList<ChannelBean> {

        // 自定义
        if ((tagBean.custom ?: 0) > 0) {
            return getChannelListByCustomTag(tagBean.id)
        }

        return withContext(Dispatchers.IO) {
            when (tagBean.id) {
                TAG_ALL -> {
                    return@withContext mDBApi.allChannel().toMutableList()
                }

                TAG_HISTORY -> {
                    val tempList = mutableListOf<ChannelBean>()
                    mDBApi.getChannelHistory().forEach {
                        tempList.addAll(
                            mDBApi.getChannelByChannelId(it.unid ?: -1)
                        )
                    }
                    return@withContext tempList
                }

                else -> {
                    return@withContext mDBApi.getChannelByTag(tagBean.id).toMutableList()
                }
            }
        }
    }

//    suspend fun getChannelList(tagId: Long): MutableList<ChannelBean> {
//        return withContext(Dispatchers.IO) {
//            when (tagId) {
//                TAG_ALL -> {
//                    return@withContext mDBApi.allChannel().toMutableList()
//                }
//
//                TAG_HISTORY -> {
//                    val tempList = mutableListOf<ChannelBean>()
//                    mDBApi.getChannelHistory().forEach {
//                        tempList.addAll(
//                            mDBApi.getChannelByChannelId(it.unid ?: -1)
//                        )
//                    }
//                    return@withContext tempList
//                }
////                TAG_FAVORITE -> {
////                    val tempList = mutableListOf<ChannelBean>()
////                    val ids = LongArray(mDBApi.allCollectionChannel.size)
////                    for (i in ids.indices){
////                        ids[i] = mDBApi.allCollectionChannel[i].unid?:-1
////                    }
////                    tempList.addAll(mDBApi.getChannelByChannelIds(ids))
////                    return@withContext tempList
////                }
//                else -> {
//                    return@withContext mDBApi.getChannelByTag(tagId).toMutableList()
//                }
//            }
//        }
//    }

    fun getSevenDate(): List<Date> {
        val days = mutableListOf<Date>()
        val calendar = Calendar.getInstance()
        days.add(calendar.time)

        val afterDays = 3
        // 前三天
        for (i in 1..afterDays) {
            calendar.add(Calendar.DAY_OF_YEAR, 1)
            days.add(calendar.time)
        }

        // 后7天
        calendar.add(Calendar.DAY_OF_YEAR, -afterDays)
        for (i in 1..7) {
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            days.add(0, calendar.time)
        }

        return days
    }

    suspend fun getChannelByChannelNumber(channelNumber: Int): List<ChannelBean> {
        return withContext(Dispatchers.IO) {
            return@withContext DBApi.getInstance().getChannelByChannelNumber(channelNumber)
        }
    }

    suspend fun getChannelLisByKeywords(keywords: String): List<ChannelBean> {
        return withContext(Dispatchers.IO) {
            return@withContext DBApi.getInstance().getChannelListByKeyword(keywords)
        }
    }

    suspend fun getChannelListByCustomTag(customId: Long): MutableList<ChannelBean> {
        return withContext(Dispatchers.IO) {
            val tempList = mutableListOf<ChannelBean>()
            val channelCollectionBeans = mDBApi.customTagChannelsId(customId)
            val ids = LongArray(channelCollectionBeans.size)
            for (i in ids.indices) {
                Logger.e("XXXXXX:${channelCollectionBeans[i].ctId}")
                ids[i] = channelCollectionBeans[i].unid ?: -1
            }
            tempList.addAll(mDBApi.getChannelByChannelIds(ids))
            tempList
        }
    }

    suspend fun getHistoryChannelList(): MutableList<ChannelBean> {
        return withContext(Dispatchers.IO) {
            val tempList = mutableListOf<ChannelBean>()
            mDBApi.getChannelHistory().forEach {
                tempList.addAll(
                    mDBApi.getChannelByChannelId(it.unid ?: -1)
                )
            }
            return@withContext tempList
        }
    }

    suspend fun saveLockedChannel(channelLockBean: ChannelLockBean) {
        return withContext(Dispatchers.IO) {
            mDBApi.saveLockedChannel(channelLockBean)
        }
    }
}