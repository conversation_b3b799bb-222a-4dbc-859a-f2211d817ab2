<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/v_fragment_center_top"
        android:layout_width="0dp"
        android:layout_height="35dp"
        android:background="@drawable/shape_bg_gradient"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.leanback.widget.HorizontalGridView
        android:id="@+id/rv_fragment_center_option"
        android:layout_width="220dp"
        android:layout_height="35dp"
        app:layout_constraintBottom_toTopOf="@+id/fl_fragment_center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <FrameLayout
        android:id="@+id/fl_fragment_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_fragment_center_option">

    </FrameLayout>

    <TextView
        android:id="@+id/tv_fragment_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginEnd="24dp"
        android:ellipsize="marquee"
        android:gravity="center_vertical"
        android:marqueeRepeatLimit="marquee_forever"
        android:singleLine="true"
        android:textColor="@color/white"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="@+id/v_fragment_center_top"
        app:layout_constraintEnd_toEndOf="@+id/v_fragment_center_top"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_center_option"
        app:layout_constraintTop_toTopOf="@+id/v_fragment_center_top" />

</androidx.constraintlayout.widget.ConstraintLayout>