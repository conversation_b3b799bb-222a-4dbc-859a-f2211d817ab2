package com.google.chuangke.util

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.OvershootInterpolator
import androidx.core.animation.addListener


object FocusAnimationUtils {
    /**
     * 当 Item 获得焦点时的动画（翻转一圈）
     */
    fun gainFocusAnimation(view: View) {
        // 沿 Y 轴翻转一圈
        val rotationY = ObjectAnimator.ofFloat(view, "rotationX", 0f, 360f)
        rotationY.setDuration(1000) // 动画持续时间
        rotationY.interpolator = OvershootInterpolator() // 弹性插值器
        rotationY.start()
    }

    /**
     * 当 Item 失去焦点时的动画（弹力晃动 3 下）
     */
    fun loseFocusAnimation(view: View) {
        // 第一次向左晃动
        val rotateLeft = ObjectAnimator.ofFloat(view, "rotationX", 0f, -180f)
        rotateLeft.setDuration(200)

        // 第二次向右晃动
        val rotateRight = ObjectAnimator.ofFloat(view, "rotationX", -180f, 180f)
        rotateRight.setDuration(200)

        // 第一次向左晃动
        val rotateLeft2 = ObjectAnimator.ofFloat(view, "rotationX", 0f, -100f)
        rotateLeft.setDuration(200)

        // 第二次向右晃动
        val rotateRight2 = ObjectAnimator.ofFloat(view, "rotationX", -100f, 100f)
        rotateRight.setDuration(200)

        // 回到中间
        val settle = ObjectAnimator.ofFloat(view, "rotationX", 100f, 0f)
        settle.setDuration(200)

        // 组合动画
        val animatorSet = AnimatorSet()
        animatorSet.playSequentially(
            rotateLeft,
            rotateRight,
            rotateLeft2,
            rotateRight2,
            settle
        ) // 顺序播放动画
        animatorSet.start()
    }

    /**
     * 同时调整 View 的宽高和缩放效果
     *
     * @param view       要调整的 View
     * @param startWidth 初始宽度
     * @param endWidth   目标宽度
     * @param startHeight 初始高度
     * @param endHeight   目标高度
     * @param fromScale   起始缩放比例
     * @param toScale     目标缩放比例
     * @param duration   动画持续时间（毫秒）
     */
    fun animateViewSizeAndScale(
        view: View,
        startWidth: Int,
        endWidth: Int,
        startHeight: Int,
        endHeight: Int,
//        fromScale: Float,
//        toScale: Float,
        duration: Long,
        onStart: () -> Unit,
        onEnd: () -> Unit,
    ) {
        // 宽高动画
        val sizeAnimator = ValueAnimator.ofFloat(0f, 1f)
        sizeAnimator.setDuration(duration)
        sizeAnimator.addUpdateListener { animation: ValueAnimator ->
            val fraction = animation.animatedValue as Float
            val newWidth = (startWidth + fraction * (endWidth - startWidth)).toInt()
            val newHeight = (startHeight + fraction * (endHeight - startHeight)).toInt()
            val params = view.layoutParams
            params.width = newWidth
            params.height = newHeight
            view.layoutParams = params
        }

        sizeAnimator.addListener(onEnd = { onEnd.invoke() }, onStart = { onStart.invoke() })

//        // 缩放动画
//        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", fromScale, toScale)
//        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", fromScale, toScale)

//        // 同时播放宽高和缩放动画
//        val animatorSet = AnimatorSet()
//        animatorSet.playTogether(sizeAnimator, scaleX, scaleY)
//        animatorSet.start()

        sizeAnimator.start()
    }
}