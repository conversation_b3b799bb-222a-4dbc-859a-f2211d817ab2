<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_fragment_channel"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_content"
    android:descendantFocusability="afterDescendants">

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_channel_tag"
        android:layout_width="190dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:nextFocusRight="@id/rv_fragment_channel_channel"
        android:paddingTop="26dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_fragment_channels_channel"
        android:layout_width="375dp"
        android:layout_height="46dp"
        android:background="@color/bg_parent"
        android:focusable="false"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="36dp"
        android:paddingTop="18dp"
        android:paddingEnd="20dp"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_tag"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_fragment_channels_channel_tag"
            style="@style/font_bold"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:focusable="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="ALL" />

        <TextView
            android:id="@+id/tv_fragment_channels_channel_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:focusable="false"
            android:textColor="@color/white"
            android:textSize="12sp"
            tools:text="1/365" />

    </LinearLayout>

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_channel_channel"
        android:layout_width="375dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:nextFocusLeft="@id/rv_fragment_channel_tag"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_tag"
        app:layout_constraintTop_toBottomOf="@+id/ll_fragment_channels_channel" />

    <LinearLayout
        android:id="@+id/ll_fragment_channels_channel_arrow"
        android:layout_width="30dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:focusable="false"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_channel"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="8dp"
            android:layout_height="18dp"
            android:contentDescription="@null"
            android:focusable="false"
            android:scaleType="fitXY"
            android:src="@mipmap/ic_channel_arrow_right" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_fragment_channels_channel_lock"
        android:layout_width="375dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:descendantFocusability="afterDescendants"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_tag"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_fragment_channels_channel_password"
            android:layout_width="315dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:background="@drawable/selector_input"
            android:drawablePadding="14dp"
            android:focusable="true"
            android:gravity="center_vertical"
            android:hint="@string/menu_lock_please_enter_password"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:textColor="@color/selector_color_hint"
            android:textColorHint="@color/selector_color_hint"
            android:textSize="14sp"
            app:drawableStartCompat="@mipmap/ic_menu_input_lock" />

        <TextView
            android:id="@+id/tv_fragment_menu_lock_tips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:focusable="false"
            android:paddingStart="25dp"
            android:paddingEnd="25dp"
            android:text="@string/menu_unlock_tips"
            android:textColor="@color/white"
            android:textSize="12sp" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_fragment_channel_program_detail"
        android:layout_width="375dp"
        android:layout_height="wrap_content"
        android:background="@color/bg_parent"
        android:descendantFocusability="afterDescendants"
        android:focusable="false"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_channel"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="25dp"
            android:focusable="false"
            android:orientation="horizontal">

            <TextView
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:text="@string/channel_duration"
                android:textColor="@color/white"
                android:textSize="17sp" />

            <TextView
                android:id="@+id/tv_fragment_channels_duration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:ellipsize="marquee"
                android:focusable="false"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:text="16:00-17:00" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="5dp"
            android:focusable="false"
            android:orientation="horizontal">

            <TextView
                style="@style/font_bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:text="@string/channel_program"
                android:textColor="@color/white"
                android:textSize="17sp" />

            <TextView
                android:id="@+id/tv_fragment_channels_program"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:ellipsize="marquee"
                android:focusable="false"
                android:maxLines="1"
                android:textColor="@color/white"
                android:textSize="17sp"
                tools:text="SBT Brasil - News" />
        </LinearLayout>

        <TextView
            android:id="@+id/tv_fragment_channels_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="5dp"
            android:ellipsize="end"
            android:focusable="false"
            android:maxLines="2"
            android:minLines="2"
            android:textColor="@color/white_50per"
            android:textSize="12sp"
            tools:text="Description:AcompanhDescription:AcompanhDescription:AcompanhDescription:AcompanhDescription:AcompanhDescription:AcompanhDescription:Acompanh" />

    </LinearLayout>

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_channel_program"
        android:layout_width="375dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:paddingStart="3.5dp"
        android:paddingTop="10dp"
        android:paddingEnd="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_channel"
        app:layout_constraintTop_toBottomOf="@+id/ll_fragment_channel_program_detail" />

    <TextView
        android:id="@+id/tv_fragment_channel_date"
        style="@style/font_bold"
        android:layout_width="90dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:background="@color/bg_parent"
        android:focusable="false"
        android:gravity="center_horizontal"
        android:paddingTop="15dp"
        android:text="@string/channel_date"
        android:textColor="#CCCCCC"
        android:textSize="13sp"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_program"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.leanback.widget.VerticalGridView
        android:id="@+id/rv_fragment_channel_date"
        android:layout_width="90dp"
        android:layout_height="0dp"
        android:background="@color/bg_parent"
        android:paddingTop="25dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rv_fragment_channel_program"
        app:layout_constraintTop_toBottomOf="@+id/tv_fragment_channel_date" />

</androidx.constraintlayout.widget.ConstraintLayout>