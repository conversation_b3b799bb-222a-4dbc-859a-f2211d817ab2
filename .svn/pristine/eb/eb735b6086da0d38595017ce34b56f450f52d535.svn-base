package com.google.chuangke.data.menu

import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelCollectionBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class MenuFavoriteRepository(private val mDBApi: DBApi) {

    suspend fun getAllChannel(): List<ChannelBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.allChannel() ?: mutableListOf()
        }
    }

    suspend fun getAllChannelCollection(): List<ChannelCollectionBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.allCollectionChannel ?: mutableListOf()
        }
    }

    suspend fun saveChannelCollection(collectionBean: ChannelCollectionBean) {
        withContext(Dispatchers.IO) {
            mDBApi.saveChannelCollection(collectionBean)
        }
    }

}