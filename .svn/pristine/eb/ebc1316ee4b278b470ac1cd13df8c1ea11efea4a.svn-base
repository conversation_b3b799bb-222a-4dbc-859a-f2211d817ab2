package com.google.chuangke.player;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.orhanobut.logger.Logger;

import java.util.Timer;
import java.util.TimerTask;

import gojson.gojson.Gojson;

/**
 * 每隔30秒检查播放器状态是否在播放（为了防止刚换台就进入检查，在每次播放源时重新开始计时）
 * 如果没有播放，则尝试播放当前频道的下一个源，同时进入30秒后检查
 * 如果尝试10次还没有播放，则重新获取频道的源
 * 换台后重置尝试次数，检查到播放成功重置尝试次数
 */
public enum SourceHelper {
    INSTANCE;

    private final Timer timer = new Timer();
    private TimerTask timerTask;

    public static long exoCheckInterval = 20000; // 检查间隔
    public static int maxRetriesPerSource = 3; //单个源的最大尝试次数
    public static int maxRetries = 20; // 最大重试次数,超过这个次数重新获取源

    public JSONArray sourceArray;
    public int currentIndex;
    public int retryCount;
    public int retryCountPerSource;

    SourceHelper() {

    }

    public void initParam() {
        new Thread(() -> {
            try {
                String playParam = Gojson.getPlayParam("");
                JSONObject jo = JSONObject.parseObject(playParam);
                long exoCheckInterval = jo.getLongValue("exoCheckInterval");
                int maxRetriesPerSource = jo.getIntValue("maxRetriesPerSource");
                int maxRetries = jo.getIntValue("maxRetries");
                SourceHelper.exoCheckInterval = exoCheckInterval == 0 ? 20000 : exoCheckInterval;
                SourceHelper.maxRetriesPerSource = maxRetriesPerSource;
                SourceHelper.maxRetries = maxRetries == 0 ? 20 : maxRetries;
            } catch (Exception e) {
                Logger.e(e.getMessage());
            }
        }).start();
    }

    public void cancel() {
        if (timerTask != null) {
            timerTask.cancel();
        }
    }

    public void schedule(Runnable runnable) {
//        Log.e("==========", "currentIndex:"+currentIndex+", retryCount:"+retryCount);
        if (timerTask != null) {
            timerTask.cancel();
        }

        // 先尝试播放其他源，超过了10次，重新获取源
        if (retryCount++ >= maxRetries) {
            PlayerHelper.getInstance().replay();
            return;
        }
        timerTask = new TimerTask() {
            @Override
            public void run() {
                runnable.run();
            }
        };
        timer.schedule(timerTask, exoCheckInterval);
    }

}
