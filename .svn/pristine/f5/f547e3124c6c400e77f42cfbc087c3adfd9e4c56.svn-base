package com.google.chuangke.util

object TimeUtil {

    /**
     * @param second 秒
     * @description: 秒转换为时分秒 HH:mm:ss 格式 仅当小时数大于0时 展示HH
     * @return: [String]
     */
    fun second2Time(second: Long?): String {
        if (second == null || second < 0) {
            return "00:00"
        }
        val h = second / 3600
        val m = second % 3600 / 60
        var str: String = if (h > 0) {
            (if (h < 10) "0$h" else h).toString() + ":"
        } else {
            "0:"
        }
        str += (if (m < 10) "0$m" else m).toString()
        return str
    }

    /**
     * @param second 秒
     * @description: 秒转换为时分秒 HH:mm:ss 格式 仅当小时数大于0时 展示HH
     * @return: [String]
     */
    fun second2Time2(second: Long?): String {
        if (second == null || second < 0) {
            return "00:00"
        }
        val h = second / 3600
        val m = second % 3600 / 60
        val s = second % 60
        var str: String = if (h > 0) {
            (if (h < 10) "0$h" else h).toString() + ":"
        } else {
            "0:"
        }
        str += (if (m < 10) "0$m" else m).toString() + ":"
        str += if (s < 10) "0$s" else s
        return str
    }

}