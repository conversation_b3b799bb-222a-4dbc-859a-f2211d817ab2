package com.google.chuangke.page.menu

import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import com.google.chuangke.R
import com.google.chuangke.base.BaseFragment
import com.google.chuangke.common.Constants
import com.google.chuangke.ext.shows
import com.google.chuangke.util.SPUtils
import com.google.chuangke.util.ScreenUtils

class MenuLockFragment : BaseFragment() {

    private lateinit var mTvOld: TextView
    private lateinit var mTvInput: TextView
    private lateinit var mTvSubmit: TextView
    private lateinit var mTvTips: TextView

    private var mMenuLockKeyboardDialog: MenuLockKeyboardDialog? = null

    private var oldPassword: String? = null

    override fun layoutId(): Int {
        return R.layout.fragment_menu_lock
    }

    override fun initView(view: View) {
        mTvOld = view.findViewById(R.id.tv_fragment_menu_lock_old)
        mTvInput = view.findViewById(R.id.tv_fragment_menu_lock_input)
        mTvSubmit = view.findViewById(R.id.tv_fragment_menu_lock_submit)
        mTvTips = view.findViewById(R.id.tv_fragment_menu_lock_tips)

    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            onResume()
        } else {
            sbPassword.clear()
            sbOldPassword.clear()
            mTvOld.text = ""
            mTvInput.text = ""
        }
    }

    override fun onResume() {
        super.onResume()
        val marginTop: Int
        oldPassword = SPUtils.getString(requireActivity(), Constants.SP_KEY_LOCK, "0000")
        if (!TextUtils.isEmpty(oldPassword)) {
            mTvOld.visibility = View.VISIBLE
            marginTop = 25
        } else {
            marginTop = 70
        }

        val lp = mTvInput.layoutParams as LinearLayout.LayoutParams
        lp.setMargins(0, ScreenUtils.dp2px(requireActivity(), marginTop), 0, 0)
        mTvInput.layoutParams = lp
    }

    var currentIndex = 1

    override fun initListener() {
        mTvOld.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                currentIndex = 0
                showKeyBoardDialog()
            } else {
                mMenuLockKeyboardDialog?.dismiss()
            }
        }

        mTvInput.setOnFocusChangeListener { v, hasFocus ->
            if (hasFocus) {
                currentIndex = 1
                showKeyBoardDialog()
            } else {
                mMenuLockKeyboardDialog?.dismiss()
            }
        }

        mTvSubmit.setOnClickListener {
            if (sbPassword.length == 4) {
                if (!TextUtils.isEmpty(oldPassword) && oldPassword != sbOldPassword.toString()) {
                    Toast.makeText(
                        context, getString(R.string.menu_lock_alert3), Toast.LENGTH_SHORT
                    ).show()
                    return@setOnClickListener
                }
                SPUtils.putString(requireActivity(), Constants.SP_KEY_LOCK, sbPassword.toString())
                Toast.makeText(
                    context, getString(R.string.menu_lock_alert2), Toast.LENGTH_SHORT
                ).show()
            } else Toast.makeText(
                context, getString(R.string.menu_lock_alert), Toast.LENGTH_SHORT
            ).show()
        }

        mTvSubmit.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyListener false
            }

            when (keyCode) {
                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    return@setOnKeyListener true
                }
            }
            false
        }
    }

    var sbPassword: StringBuilder = StringBuilder("")
    var sbOldPassword: StringBuilder = StringBuilder("")

    private fun showKeyBoardDialog() {
        if (mMenuLockKeyboardDialog == null) {
            mMenuLockKeyboardDialog = MenuLockKeyboardDialog(requireActivity(), R.style.Dialog)
            mMenuLockKeyboardDialog!!.mOnMenuLockKeyboardClickListener =
                object : OnMenuLockKeyboardClickListener {
                    override fun onClick(word: String) {
                        if (currentIndex == 1) {
                            if (sbPassword.length <= 4) {
                                when (word) {
                                    getString(R.string.key_board_del) -> {
                                        if (sbPassword.isNotEmpty()) {
                                            sbPassword.deleteCharAt(sbPassword.lastIndex)
                                            mTvInput.text = sbPassword
                                        }
                                    }
                                    getString(R.string.key_board_done) -> {
                                        if (sbPassword.length < 4) {
                                            Toast.makeText(
                                                context,
                                                getString(R.string.menu_lock_alert),
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        } else {
                                            mMenuLockKeyboardDialog?.dismiss()
                                        }
                                    }
                                    else -> {
                                        if (sbPassword.length < 4) {
                                            sbPassword.append(word)
                                            mTvInput.text = sbPassword
                                        }
                                    }
                                }
                            }
                        } else {
                            if (sbOldPassword.length <= 4) {
                                when (word) {
                                    getString(R.string.key_board_del) -> {
                                        if (sbOldPassword.isNotEmpty()) {
                                            sbOldPassword.deleteCharAt(sbOldPassword.lastIndex)
                                            mTvOld.text = sbOldPassword
                                        }
                                    }
                                    getString(R.string.key_board_done) -> {
                                        if (sbOldPassword.length < 4) {
                                            Toast.makeText(
                                                context,
                                                getString(R.string.menu_lock_alert),
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        } else {
                                            mMenuLockKeyboardDialog?.dismiss()
                                        }
                                    }
                                    else -> {
                                        if (sbOldPassword.length < 4) {
                                            sbOldPassword.append(word)
                                            mTvOld.text = sbOldPassword
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
        }
        mMenuLockKeyboardDialog?.shows()
    }

    fun requestFocus() {
        mTvOld.requestFocus()
    }

    override fun onDestroy() {
        super.onDestroy()
        mMenuLockKeyboardDialog?.dismiss()
    }
}