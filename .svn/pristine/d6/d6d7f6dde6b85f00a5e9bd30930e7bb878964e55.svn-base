<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_channel_favorite_root"
    android:layout_width="match_parent"
    android:layout_height="55dp"
    android:layout_marginBottom="7dp"
    android:background="@drawable/selector_white"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    tools:background="@color/black">

    <TextView
        android:id="@+id/tv_item_channel_favorite_no"
        android:layout_width="28dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:duplicateParentState="true"
        android:focusable="false"
        android:gravity="center"
        android:textColor="@color/selector_color_main"
        android:textSize="12sp"
        tools:text="001" />

    <ImageView
        android:id="@+id/iv_item_channel_favorite_logo"
        android:layout_width="56dp"
        android:layout_height="50dp"
        android:background="@drawable/shape_icon_bg"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:contentDescription="@null"
        android:focusable="false"
        android:scaleType="fitCenter" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="13dp"
        android:layout_weight="1"
        android:duplicateParentState="true"
        android:focusable="false"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_item_channel_favorite_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:focusable="false"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            android:textColor="@color/selector_color_main"
            android:textSize="15sp"
            tools:text="BABY TV" />

        <TextView
            android:id="@+id/tv_item_channel_favorite_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:duplicateParentState="true"
            android:focusable="false"
            android:textColor="@color/selector_color_action_bar_subtitle"
            android:textSize="9sp"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"
            tools:ignore="SmallSp"
            tools:text="Da Noite Para o Dia" />
    </LinearLayout>

    <ImageView
        android:id="@+id/iv_item_channel_favorite_collection"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="33.5dp"
        android:contentDescription="@null"
        android:duplicateParentState="true"
        android:src="@drawable/selector_menu_favorite_love"
        android:visibility="invisible" />

</LinearLayout>