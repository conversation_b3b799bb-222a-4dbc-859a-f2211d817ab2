<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="update_progress_horizontal" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:indeterminateOnly">false</item>
        <!--进度条的进度颜色drawable文件-->
        <item name="android:progressDrawable">@drawable/progress_indeterminate_horizontal</item>
        <!--进度条的最小高度-->
        <item name="android:minHeight">6dp</item>
        <!--进度条的最大高度-->
        <item name="android:maxHeight">6dp</item>

        <item name="android:thumb">@drawable/shape_seekbar_button</item>
    </style>

    <style name="Dialog" parent="@android:style/Theme.Dialog">
        <!-- 是否有边框 -->
        <item name="android:windowFrame">@null</item>
        <!--是否在悬浮Activity之上  -->
        <item name="android:windowIsFloating">true</item>
        <!--标题  -->
        <item name="android:windowNoTitle">true</item>
        <!--阴影  -->
        <item name="android:windowIsTranslucent">true</item><!--半透明-->
        <!-- 点外边可以消失  -->
        <item name="android:windowCloseOnTouchOutside">false</item>

        <item name="android:windowBackground">@android:color/transparent</item>
    </style>

    <style name="XUpdate_DialogTheme" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowIsFloating">true</item>
        <item name="android:background">@color/white_ccc</item>
        <item name="android:textColor">@color/black</item>
        <!--        <item name="android:buttonBarButtonStyle">@style/XUpdate_DialogTheme_Button</item>-->
    </style>

    <style name="XUpdate_DialogTheme_Button">
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/selector_menu_sub_btn</item>
        <item name="android:layout_height">16dp</item>
        <item name="android:textColor">@color/purple_200</item>
    </style>

    <style name="font_regular">
        <item name="android:fontFamily">@font/open_sans_regular</item>
    </style>

    <style name="font_bold">
        <item name="android:fontFamily">@font/open_sans_bold</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- loading弹出框透明背景颜色 -->
    <style name="MyLoadingDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:gravity">center</item>
    </style>


    <style name="TabLayoutTextStyle" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">18sp</item>
        <item name="android:typeface">monospace</item>
        <item name="textAllCaps">false</item>
    </style>

    <!-- Program Guide Start -->
    <style name="ProgramGuide.Button.JumpToLive" parent="">
        <item name="android:textSize">13sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:background">@drawable/programguide_button_background</item>
        <item name="android:minHeight">18dp</item>
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
    </style>

    <style name="ProgramGuide.Text.Detail.Title" parent="">
        <item name="android:fontFamily">@font/open_sans_bold</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textDirection">locale</item>
        <item name="android:textAlignment">gravity</item>
        <item name="android:textColor">@color/programguide_detail_text_color</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>

    <style name="ProgramGuide.Text.Detail.Metadata" parent="">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:textColor">@color/programguide_detail_text_color</item>
        <item name="android:textDirection">locale</item>
        <item name="android:textAlignment">gravity</item>
        <item name="android:layout_marginTop">6dp</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="ProgramGuide.Text.Detail.Description" parent="">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textColor">@color/programguide_detail_text_color</item>
        <item name="android:textDirection">locale</item>
        <item name="android:textAlignment">gravity</item>
        <item name="android:maxLines">5</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginEnd">96dp</item>
        <item name="android:layout_marginTop">6dp</item>
    </style>

    <style name="ProgramGuide.Text.Channel" parent="">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/programguide_channel_text_color</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:ellipsize">middle</item>
        <item name="android:layout_marginLeft">40dp</item>
        <item name="android:layout_marginRight">4dp</item>
    </style>

    <style name="ProgramGuide.Image.Detail" parent="">
        <item name="android:layout_width">200dp</item>
        <item name="android:layout_height">110dp</item>
        <item name="android:scaleType">centerInside</item>
        <item name="android:textSize">14sp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:elevation">4dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:minHeight">0dp</item>
    </style>

    <style name="ProgramGuide.Image.Channel" parent="">
        <item name="android:layout_width">36dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">7dp</item>
        <item name="android:layout_marginBottom">7dp</item>
        <item name="android:layout_gravity">left|center_vertical</item>
        <item name="android:adjustViewBounds">true</item>
        <item name="android:scaleType">fitStart</item>
    </style>

    <style name="ProgramGuide.Text.Program.Title" parent="">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:ellipsize">end</item>
        <item name="android:gravity">start|center_vertical</item>
        <item name="android:textDirection">locale</item>
        <item name="android:textAlignment">viewStart</item>
        <item name="android:maxLines">1</item>
        <item name="android:padding">@dimen/programguide_item_padding</item>
        <item name="android:duplicateParentState">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/programguide_item_text_color</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="ProgramGuide.Text.Time" parent="">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/programguide_time_row_height</item>
        <item name="android:gravity">center</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textColor">@color/programguide_time_text_color</item>
    </style>

    <style name="ProgramGuide.Text.Filter" parent="Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:textSize">13sp</item>
    </style>

    <style name="ProgramGuide.ErrorMessage" parent="">
        <item name="android:fontFamily">@font/open_sans_regular</item>
        <item name="android:textSize">16sp</item>
    </style>
    <!-- Program Guide End -->

    <style name="big">
        <item name="android:fontFamily">@font/open_sans_bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">32sp</item>
    </style>
</resources>