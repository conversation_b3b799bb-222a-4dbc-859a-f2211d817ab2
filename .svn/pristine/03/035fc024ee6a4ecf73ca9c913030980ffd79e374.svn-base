package com.google.chuangke.base

import android.content.Context
import android.graphics.Typeface
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.leanback.widget.Presenter
import com.google.chuangke.util.FocusAnimationUtils

@Suppress("UNCHECKED_CAST")
abstract class BasePresenter<T> : Presenter() {

    lateinit var context: Context

    val map = mutableMapOf<Boolean, T>()

    var lastSelectedView: View? = null

    var needKeep:Boolean = false

    abstract fun layoutId(): Int

    /**
     * 要改变字体状态的TextView id列表
     */
    abstract fun addFocusTextStyle(): List<Int>

    abstract fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): Boolean

    abstract fun bindViewHolder(view: View, item: T)

    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        context = parent.context

        val rootView = LayoutInflater.from(context).inflate(layoutId(), parent, false)

        rootView.setOnFocusChangeListener { view, hasFocus ->
            map[hasFocus] = view.tag as T
            if (map[true] == map[false] && needKeep) {
                view.isActivated = !hasFocus
                lastSelectedView = view
            } else {
                lastSelectedView?.isActivated = false
            }

            view.isSelected = hasFocus

            addFocusTextStyle().forEach {
                setFocusTextStyle(view, it, hasFocus)
            }

//            if (hasFocus) {
//                // 获得焦点时动画
//                FocusAnimationUtils.gainFocusAnimation(view)
//            } else {
//                // 失去焦点时动画
//                FocusAnimationUtils.loseFocusAnimation(view)
//            }
        }

        rootView.setOnKeyListener { v, keyCode, event ->
            return@setOnKeyListener onKeyListener(v, keyCode, event)
        }

        return ViewHolder(rootView)
    }

    override fun onBindViewHolder(viewHolder: ViewHolder, item: Any) {
        viewHolder.view.tag = item
        bindViewHolder(viewHolder.view, item as T)
    }

    override fun onUnbindViewHolder(viewHolder: ViewHolder?) {
    }

    fun needKeep() {
        needKeep = true
    }
}


private fun setFocusTextStyle(rootView: View, resId: Int, hasFocus: Boolean) {
    rootView.findViewById<TextView>(resId).typeface =
        if (hasFocus) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
}