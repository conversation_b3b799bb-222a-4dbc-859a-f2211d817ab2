package com.google.chuangke.page

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.OnChildViewHolderSelectedListener
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.chuangke.R
import com.google.chuangke.base.BaseDataBindingFragment
import com.google.chuangke.common.Config
import com.google.chuangke.common.UnitCallback
import com.google.chuangke.data.ClassicEpgViewModel
import com.google.chuangke.database.DBApi
import com.google.chuangke.databinding.FragmentClassicEpgBinding
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.ext.dp
import com.google.chuangke.page.adapter.RowAdapter
import com.google.chuangke.page.dialog.UnlockDialog
import com.google.chuangke.page.listener.OnProgramSelectListener
import com.google.chuangke.page.presenter.EpgDayPresenter
import com.google.chuangke.page.presenter.EpgTagPresenter
import com.google.chuangke.util.DateUtil
import com.google.chuangke.view.OnEpgViewControlListener
import com.google.chuangke.view.OnEpgViewFocusedListener
import com.google.chuangke.view.RowView
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.launch
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.text.SimpleDateFormat
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

class ClassicEpgFragment(private var mOnProgramSelectListener: OnProgramSelectListener) :
    BaseDataBindingFragment<FragmentClassicEpgBinding>(FragmentClassicEpgBinding::inflate),
    OnEpgViewFocusedListener, OnEpgViewControlListener {
    // 数据
    private val mClassicEpgViewModel: ClassicEpgViewModel by viewModel()

    // 日期
    var currentDate: ZonedDateTime = ZonedDateTime.now()

    /// Adapter
    private lateinit var mEpgAdapter: RowAdapter
    private lateinit var mEpgDayAdapter: ArrayObjectAdapter
    private lateinit var mGroupAdapter: ArrayObjectAdapter
    private lateinit var mEpgDayPresenter: EpgDayPresenter
    private var firstLoad = true
    private var mUnlockDialog: UnlockDialog? = null
    private var mEpgBean: EpgBean? = null
    private var mTagBean: TagBean? = null

    private val rightClickEvents = MutableSharedFlow<() -> Unit>()
    private val dayClickEvents = MutableSharedFlow<() -> Unit>()
    private val categoryClickEvents = MutableSharedFlow<() -> Unit>()
    private var shouldHandleEvent = false

    private fun showUnlockDialog(callback: UnitCallback) {
        if (mUnlockDialog == null) {
            mUnlockDialog = UnlockDialog(requireActivity(), R.style.Dialog, callback = callback)
        } else {
            mUnlockDialog!!.callback = callback
        }

        if (!mUnlockDialog!!.isShowing) {
            mUnlockDialog!!.showDialog()
        }
    }

    override fun initView() {
        binding.vgTag.clearFocus()
        mTagBean = Config.getInstance().currentTag
        mClassicEpgViewModel.initTag()
        binding.vgEpg.adapter =
            RowAdapter(this, this, this).also { mEpgAdapter = it }

        binding.hgDate.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(EpgDayPresenter(this@ClassicEpgFragment).also {
                it.needKeep = true
                mEpgDayPresenter = it
            }).also { mEpgDayAdapter = it })

        binding.vgTag.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(EpgTagPresenter().also {
                it.needKeep = true
            }).also { mGroupAdapter = it })

        initData()
    }

    @SuppressLint("SetTextI18n")
    private fun initData() {
        val hour = currentDate.hour
        // even time range
        if (hour % 2 != 0) {
            currentDate = currentDate.minusHours(1)
        }

        currentDate = ZonedDateTime.of(
            currentDate.year,
            currentDate.monthValue,
            currentDate.dayOfMonth,
            currentDate.hour,
            0,
            0,
            0,
            ZoneId.systemDefault()
        )

        // name
        binding.tvTag.text = "${mTagBean!!.name}"
        updateTimeRange()
        mClassicEpgViewModel.fetchEpgList(mTagBean!!)
    }

    private fun updateTimeRange() {
        val time = (currentDate.toInstant().toEpochMilli()
                / 1_000).toInt()
        binding.tvTimeT1.text = DateUtil.getScheduleTimeHour(time)
        binding.tvTimeT2.text = DateUtil.getScheduleTimeHour(time + 1_800)
        binding.tvTimeT3.text = DateUtil.getScheduleTimeHour(time + 3_600)
        binding.tvTimeT4.text = DateUtil.getScheduleTimeHour(time + 5_400)
        binding.tvTimeT5.text = DateUtil.getScheduleTimeHour(time + 7_200)
        updateCurrentTimeIndicator()
    }

    private fun updateCurrentTimeIndicator() {
        val currentCalendar = ZonedDateTime.now()
        val result = (currentCalendar.toInstant().toEpochMilli() - currentDate.toInstant()
            .toEpochMilli()) / 60_000
        // 2.5 * 60, [result > 0 && result < 150]
        if (result in 1..149) {
            binding.ivIndicator.translationX =
                (600.dp - binding.ivIndicator.width / 2) / 150f * result
            binding.ivIndicator.visibility = View.VISIBLE
        } else {
            binding.ivIndicator.visibility = View.GONE
        }
    }


    override fun initListener() {
        binding.hgDate.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                if (binding.hgDate.hasFocus()) {
                    val targetDate =
                        mEpgDayAdapter.get(binding.hgDate.selectedPosition) as Date

                    lifecycleScope.launch {
                        dayClickEvents.emit {
                            // 设置为为日期当天0点开始
                            currentDate = targetDate.toInstant().atZone(ZoneId.systemDefault())
                            initData()
                        }
                    }
                }
            }
        })

        binding.vgTag.setOnChildViewHolderSelectedListener(object :
            OnChildViewHolderSelectedListener() {
            override fun onChildViewHolderSelected(
                parent: RecyclerView?,
                child: RecyclerView.ViewHolder?,
                position: Int,
                subposition: Int
            ) {
                super.onChildViewHolderSelected(parent, child, position, subposition)
                if (binding.vgTag.hasFocus()) {
                    val tagBean =
                        mGroupAdapter.get(binding.vgTag.selectedPosition) as TagBean

                    // 标签没有变化不查询
                    if (mTagBean != null && mTagBean!!.id == tagBean.id && mTagBean!!.custom == tagBean.custom) return

                    lifecycleScope.launch {
                        currentDate = ZonedDateTime.now()
                        val hour = currentDate.hour
                        // even time range
                        if (hour % 2 != 0) {
                            currentDate = currentDate.minusHours(1)
                        }

                        // 焦点切回当前
                        binding.hgDate.selectedPosition = 7
                        mEpgDayAdapter.notifyArrayItemRangeChanged(0, mEpgDayAdapter.size())

                        currentDate = ZonedDateTime.of(
                            currentDate.year,
                            currentDate.monthValue,
                            currentDate.dayOfMonth,
                            currentDate.hour,
                            0,
                            0,
                            0,
                            ZoneId.systemDefault()
                        )
                        updateCurrentTimeIndicator()
                        categoryClickEvents.emit {
                            mTagBean = tagBean
                            mClassicEpgViewModel.fetchEpgList(tagBean)
                            binding.tvTag.text = tagBean.name!!
                        }
                    }
                }
            }
        })


        binding.vgTag.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    (requireActivity() as MainActivity).hideFragment()
                }

                KeyEvent.KEYCODE_DPAD_LEFT -> return@setOnKeyInterceptListener true

                KeyEvent.KEYCODE_DPAD_RIGHT -> {
                    if (mEpgAdapter.data.isEmpty()) return@setOnKeyInterceptListener true
                }
            }

            return@setOnKeyInterceptListener false
        }

        binding.hgDate.setOnKeyInterceptListener { event ->
            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_BACK -> {
                    (requireActivity() as MainActivity).hideFragment()
                }

                KeyEvent.KEYCODE_DPAD_DOWN -> {
                    mEpgDayPresenter.hasFocus = false
                    mEpgDayAdapter.notifyArrayItemRangeChanged(0, mEpgDayAdapter.size())
                    binding.hgDate.clearFocus()
                    binding.vgEpg.requestFocus()
                    return@setOnKeyInterceptListener true
                }
            }

            return@setOnKeyInterceptListener false
        }
    }

    @OptIn(FlowPreview::class)
    override fun initObserve() {

        lifecycleScope.launch {
            rightClickEvents
                .debounce(100)
                .collect {
                    it.invoke()
                }
        }

        lifecycleScope.launch {
            dayClickEvents
                .debounce(100)
                .collectLatest {
                    it.invoke()
                }
        }

        lifecycleScope.launch {
            categoryClickEvents
                .debounce(100)
                .collectLatest {
                    it.invoke()
                }
        }

        mClassicEpgViewModel.tagLiveData.observe(this) {
            mGroupAdapter.setItems(it, null)
            for (index in it.indices) {
                if (it[index].id == Config.getInstance().currentTag.id && it[index].custom == Config.getInstance().currentTag.custom) {
                    binding.vgTag.selectedPosition = index
                    binding.vgTag.requestFocus()
                    break
                }
            }
        }

        mClassicEpgViewModel.classicEpgLiveData.observe(this) {
            updateChannelsTimeRange(it)
        }

        // 日期
        mClassicEpgViewModel.dayListLiveData.observe(this) {
            mEpgDayAdapter.setItems(it, null)


            val sdf = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
            val targetDay = sdf.format(currentDate.toInstant().toEpochMilli())

            it.let { days ->
                for (i in days.indices) {
                    if (sdf.format(days[i]) == targetDay) {
                        binding.hgDate.selectedPosition = i
                        break
                    }
                }
            }
        }
    }


    private var currentSelectChannelId = -1L

    /**
     * Note: this is a simple data check
     */
    private fun updateChannelsTimeRange(list: MutableList<ChannelBean>) {
        mEpgAdapter.setNewInstance(list)
        var currentChannelIndex = 0
        for ((position, channelBean) in list.withIndex()) {
            if (currentSelectChannelId == -1L) {
                currentSelectChannelId = Config.getInstance().currentPlayChannel!!.channelId!!
            }
            if (channelBean.channelId!! == currentSelectChannelId) {
                currentChannelIndex = position
                break
            }
        }

        if (firstLoad) {
            firstLoad = false
            binding.vgEpg.let {
                it.postDelayed({
                    binding.vgEpg.selectedPosition = currentChannelIndex
                    binding.vgEpg.requestFocus()
                    it.postDelayed({
                        binding.vgEpg.layoutManager!!.findViewByPosition(
                            currentChannelIndex
                        )?.let { p1 ->
                            (p1 as ViewGroup).getChildAt(1)?.let { p2 ->
                                (p2 as RowView).focusTargetItem((System.currentTimeMillis() / 1000).toInt())
                            }
                        }
                    }, 100L)
                }, 100L)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onFocused(index: Int, epgBean: EpgBean) {
        binding.tvChannelName.text = "${epgBean.channelName}"
        binding.tvEpgDuration.text =
            "${DateUtil.getScheduleTimeHour(epgBean.beginTime!!)} - ${
                DateUtil.getScheduleTimeHour(
                    epgBean.endTime!!
                )
            }"
        binding.tvEpgName.text = "${epgBean.name}"
        binding.tvEpgDescription.text = epgBean.description

        // 台标
        Glide.with(requireActivity())
            .load(DBApi.getInstance().getChannelIcon(null, epgBean, null))
            .error(R.mipmap.dif_icon_default).diskCacheStrategy(DiskCacheStrategy.ALL)//缓存所有台标
            .into(binding.ivChannelLogo)
    }


    private var targetTime = (System.currentTimeMillis() / 1_000).toInt()

    /**
     * Epg Item key event
     *
     * @param event key event
     * @param position channel position in channel list
     * @param index epg index in epg list
     * @param total total epg count of the channel
     * @param epgBean current epg on touch
     *
     * @return [Boolean] intercept or not
     */
    override fun onKey(
        event: KeyEvent, position: Int, index: Int, total: Int, epgBean: EpgBean
    ): Boolean {
        when (event.keyCode) {
            KeyEvent.KEYCODE_DPAD_UP -> {
                val duration = event.eventTime - event.downTime
                if (duration < 1000 && event.repeatCount > 0) {
                    // 长按
                    mEpgDayPresenter.hasFocus = true
                    mEpgDayAdapter.notifyArrayItemRangeChanged(0, mEpgDayAdapter.size())
                    binding.hgDate.requestFocus()
                    return true
                } else {
                    if (position > 0) {
                        binding.vgEpg.layoutManager!!.findViewByPosition(position - 1)
                            ?.let { p1 ->
                                currentSelectChannelId = epgBean.channelId!!.toLong()
//                            p1.isActivated = true
                                (p1 as ViewGroup).getChildAt(1)?.let { p2 ->
                                    (p2 as RowView).focusTargetItem((targetTime))
                                }
                            }
                    } else {
                        mEpgDayPresenter.hasFocus = true
                        mEpgDayAdapter.notifyArrayItemRangeChanged(0, mEpgDayAdapter.size())
                        binding.hgDate.requestFocus()
                    }
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_DOWN -> {
                if (position < mEpgAdapter.data.size - 1) {
                    binding.vgEpg.layoutManager!!.findViewByPosition(position + 1)
                        ?.let { p1 ->
                            currentSelectChannelId = epgBean.channelId!!.toLong()
//                            p1.isActivated = true
                            (p1 as ViewGroup).getChildAt(1)?.let { p2 ->
                                (p2 as RowView).focusTargetItem((targetTime))
                            }
                        }
                }
                return true
            }

            KeyEvent.KEYCODE_PAGE_UP -> {
                if (position > 0) {
                    var tempIndex = position - PAGE_COUNT
                    if (tempIndex < 0) {
                        tempIndex = 0
                    }
                    binding.vgEpg.layoutManager!!.findViewByPosition(tempIndex)
                        ?.let { p1 ->
                            currentSelectChannelId = epgBean.channelId!!.toLong()
//                            p1.isActivated = true
                            (p1 as ViewGroup).getChildAt(1)?.let { p2 ->
                                (p2 as RowView).focusTargetItem((targetTime))
                            }
                        }
                }
                return true
            }

            KeyEvent.KEYCODE_PAGE_DOWN -> {
                if (position < mEpgAdapter.data.size - 1) {
                    var tempIndex = position + PAGE_COUNT
                    if (tempIndex >= mEpgAdapter.data.size) {
                        tempIndex = mEpgAdapter.data.size - 1
                    }
                    binding.vgEpg.layoutManager!!.findViewByPosition(tempIndex)
                        ?.let { p1 ->
                            currentSelectChannelId = epgBean.channelId!!.toLong()
//                            p1.isActivated = true
                            (p1 as ViewGroup).getChildAt(1)?.let { p2 ->
                                (p2 as RowView).focusTargetItem((targetTime))
                            }
                        }
                }
                return true
            }

            KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_ENTER, KeyEvent.KEYCODE_NUMPAD_ENTER -> {
                (requireActivity() as MainActivity).hideFragment()
                val channelBean = mEpgAdapter.data[position]
                mEpgBean = epgBean

                val block = {
//                    // 没有回播，EPG为空，还没到播放时间，跳转到直播
//                    if (channelBean.playback == 0 || epgBean.endTime!! > System.currentTimeMillis() / 1000 || epgBean.id == null) {
                    mOnProgramSelectListener.onProgramSelect(mTagBean!!, channelBean)
//                    } else {
//                        // 回播
//                        mOnProgramSelectListener.onProgramSelect(
//                            mTagBean!!, channelBean, epgBean
//                        )
//                    }
                }

                if (channelBean.locked == 1 && channelBean.id != Config.getInstance().currentPlayChannel!!.id) {
                    showUnlockDialog { block.invoke() }
                } else {
                    block.invoke()
                }
                return true
            }

            KeyEvent.KEYCODE_BACK -> {
                (requireActivity() as MainActivity).hideFragment()
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                val duration = event.eventTime - event.downTime
                if (duration < 1000 && event.repeatCount > 0) {
                    // 长按
                    binding.vgTag.requestFocus()
                    return true
                } else {
                    val minDay = mClassicEpgViewModel.dayListLiveData.value!!.first()
                    if (currentDate.toInstant()
                            .toEpochMilli() > minDay.time + 7_199_000
                    ) {
                        if (index == 0) {
                            currentDate = currentDate.minusHours(2)
                            initData()
                            // 日期选中
                            updateDay()
                            shouldHandleEvent = true
                        } else {
                            shouldHandleEvent = false
                        }
                    } else {
                        shouldHandleEvent = true
                    }

                    return shouldHandleEvent
                }
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                targetTime = epgBean.startTime!!
                lifecycleScope.launch {
                    rightClickEvents.emit {
                        val maxDay = mClassicEpgViewModel.dayListLiveData.value!!.last()
                        if (currentDate.toInstant()
                                .toEpochMilli() < maxDay.time + 79_199_000
                        ) {
                            if (index == total - 1) {
                                currentDate = currentDate.plusHours(2)
                                initData()
                                // 日期选中
                                updateDay()
                                shouldHandleEvent = true
                            } else {
                                shouldHandleEvent = false
                            }
                        } else {
                            shouldHandleEvent = true
                        }
                    }
                }

                return shouldHandleEvent
            }
        }

        return false
    }

    private fun updateDay() {
        // 日期选中
        for (dayIndex in 0 until mEpgDayAdapter.size()) {
            val targetTime =
                DateUtil.getDMTime(
                    currentDate.toInstant().toEpochMilli(),
                    "yyyy-MM-dd"
                )
            val date = DateUtil.getFormatString(
                mEpgDayAdapter[dayIndex] as Date,
                "yyyy-MM-dd"
            )
            if (date == targetTime) {
                binding.hgDate.selectedPosition = dayIndex
                break
            }
        }
        mEpgDayAdapter.notifyArrayItemRangeChanged(0, mEpgDayAdapter.size())
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (hidden) {
            currentSelectChannelId = -1L
            currentDate = ZonedDateTime.now()
            binding.hgDate.selectedPosition = 7
            mEpgDayAdapter.notifyArrayItemRangeChanged(0, mEpgDayAdapter.size())
            firstLoad = true
        } else {
            mTagBean = Config.getInstance().currentTag
            mClassicEpgViewModel.initTag()
            initData()
        }
    }

    private val progressUpdateHandler: Handler = Handler(Looper.getMainLooper())
    private val progressUpdateRunnable: Runnable = object : Runnable {
        override fun run() {
            updateCurrentTimeIndicator()
            progressUpdateHandler.postDelayed(this, TIME_INDICATOR_UPDATE_INTERVAL)
        }
    }

    override fun onResume() {
        super.onResume()
        progressUpdateHandler.removeCallbacks(progressUpdateRunnable)
        progressUpdateHandler.post(progressUpdateRunnable)
    }

    /**
     * Called when the fragment will be paused. We stop the progress updates in this case.
     */
    override fun onPause() {
        super.onPause()
        progressUpdateHandler.removeCallbacks(progressUpdateRunnable)
    }

    companion object {
        private val TIME_INDICATOR_UPDATE_INTERVAL = TimeUnit.SECONDS.toMillis(5)
        private const val PAGE_COUNT = 3
    }

}