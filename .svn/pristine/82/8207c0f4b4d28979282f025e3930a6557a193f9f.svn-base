package com.google.chuangke.page.dialog

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.*
import androidx.leanback.widget.ArrayObjectAdapter
import androidx.leanback.widget.ItemBridgeAdapter
import androidx.leanback.widget.VerticalGridView
import com.google.chuangke.R
import com.google.chuangke.base.BaseSpeechDialog
import com.google.chuangke.common.Constants
import com.google.chuangke.page.center.SettingType
import com.google.chuangke.page.center.presenter.SettingDialogPresenter
import com.google.chuangke.util.SPUtils

/**
 * 设置界面
 */
class SettingDialog(context: Context, val callback: (SettingType) -> Unit) :
    BaseSpeechDialog(context) {

    private lateinit var mRecyclerView: VerticalGridView
    private lateinit var mAdapter: ArrayObjectAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        window!!.requestFeature(Window.FEATURE_NO_TITLE)
        val view: View = LayoutInflater.from(context).inflate(R.layout.dialog_setting, null)
        setContentView(view)
        window!!.setBackgroundDrawable(ColorDrawable(0x00000000))
        window!!.setLayout(
            WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
        )

        initView(view)
        initData()
    }

    private fun initData() {
        val list = mutableListOf(SettingType.SPEED_VISIBLE, SettingType.TEXT_APPEARANCE)
        mAdapter.setItems(list, null)
    }

    private fun initView(view: View) {
        mRecyclerView = view.findViewById(R.id.rv_dialog_setting)
        mRecyclerView.adapter =
            ItemBridgeAdapter(ArrayObjectAdapter(SettingDialogPresenter()).also {
                mAdapter = it
            })

        mRecyclerView.setOnKeyInterceptListener { event ->

            if (event.action == KeyEvent.ACTION_UP) {
                return@setOnKeyInterceptListener false
            }

            when (event.keyCode) {
                KeyEvent.KEYCODE_DPAD_CENTER -> {
                    when (val type = mAdapter[mRecyclerView.selectedPosition] as SettingType) {
                        SettingType.SPEED_VISIBLE -> {
                            val currentSpeedSP: String? =
                                SPUtils.getString(context, Constants.SETTING_SPEED_VISIBLE, "1")

                            val show = currentSpeedSP != null && currentSpeedSP == "1"

                            SPUtils.putString(
                                context,
                                Constants.SETTING_SPEED_VISIBLE,
                                if (show) "0" else "1"
                            )

                            mAdapter.notifyItemRangeChanged(0, 1)
                        }

                        SettingType.TEXT_APPEARANCE -> {
                            callback.invoke(type)
                            dismiss()
                        }
                    }

                    return@setOnKeyInterceptListener true
                }
            }

            return@setOnKeyInterceptListener false

        }
    }
}



