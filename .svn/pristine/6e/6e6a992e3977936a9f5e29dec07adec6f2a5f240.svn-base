package com.google.chuangke.util

import java.text.SimpleDateFormat
import java.util.*

object DateUtil {

    const val MM_DD_YYYY_HH_MM_SS = "MM-dd-yyyy HH:mm:ss"
    const val MM_DD_YYYY_HH_MM = "MM-dd-yyyy HH:mm"
    private const val YYYY_MM_DD = "yyyy-MM-dd"

    fun getHourTime(timeStamp: Int?): String {
        timeStamp ?: return ""
        val sdf = SimpleDateFormat("h:mm aa", Locale.getDefault())
        return sdf.format(Date(timeStamp * 1000L))
    }

    fun getYMDTime(timeStamp: Int?): String {
        timeStamp ?: return ""
        val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        return sdf.format(Date(timeStamp * 1000L))
    }

    fun getYMDHMTime(timeStamp: Int?): String {
        timeStamp ?: return ""
        val sdf = SimpleDateFormat(MM_DD_YYYY_HH_MM_SS, Locale.getDefault())
        return sdf.format(Date(timeStamp * 1000L))
    }

    fun getDMTime(timeStamp: Int?): String {
        timeStamp ?: return ""
        val sdf = SimpleDateFormat("MM-dd-yyyy", Locale.getDefault())
        return sdf.format(Date(timeStamp * 1000L))
    }

    fun getDMTime(timeStamp: Long, formatter: String): String {
        val sdf = SimpleDateFormat(formatter, Locale.getDefault())
        return sdf.format(Date(timeStamp))
    }

    fun getFormatString(date: Date, formatter: String): String {
        val sdf = SimpleDateFormat(formatter, Locale.getDefault())
        return sdf.format(date)
    }


    fun getScheduleTimeHour2(timeStamp: Int): String {
        val sdf = SimpleDateFormat(MM_DD_YYYY_HH_MM, Locale.getDefault())
        return sdf.format(Date(timeStamp * 1000L))
    }

    /**
     * 当前时间戳(秒)
     */
    fun currentTimeSeconds(): Int {
        return (System.currentTimeMillis() / 1000).toInt()
    }

    fun isToday(date: Date?): Boolean {
        date ?: return false
        val sdf = SimpleDateFormat(YYYY_MM_DD, Locale.getDefault())
        val param = sdf.format(date) //参数时间
        val now = sdf.format(Date()) //当前时间
        return param == now
    }

    fun getScheduleTimeHour(timeStamp: Int): String {
        val sdf = SimpleDateFormat("hh:mm aa", Locale.getDefault())
        return sdf.format(Date(timeStamp * 1000L))
    }
}