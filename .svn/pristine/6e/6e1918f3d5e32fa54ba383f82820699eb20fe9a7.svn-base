<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bg_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llSample"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/setting"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Animal Planet East"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/tv2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Monday 2/24"
            android:textColor="@color/white"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="Duration: 09:00 AM - 10:00 AM"
            android:textColor="@color/white"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="The Zoo - Andre the Baby Goat"
            android:textColor="@color/white"
            android:textSize="10sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/setting"
        android:layout_width="480dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/llSample"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            style="@style/TextAppearance.AppCompat.Headline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="false"
            android:text="Font Size Settings"
            android:textColor="@color/white" />

        <TextView
            style="@style/TextAppearance.AppCompat.Caption"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:focusable="false"
            android:text="Adjust the font size to suit your viewing distance and comfort."
            android:textColor="@color/white" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:focusable="false"
            android:text="Options"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <RadioGroup
            android:id="@+id/group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <RadioButton
                android:id="@+id/tvSmall"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:buttonTint="@color/white"
                android:focusable="true"
                android:paddingStart="4dp"
                android:paddingEnd="24dp"
                android:text="Small (Best for close viewing)"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/tvMedium"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:buttonTint="@color/white"
                android:focusable="true"
                android:gravity="center_vertical"
                android:paddingStart="4dp"
                android:paddingEnd="24dp"
                android:text="Medium (Default size)"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/tvLarge"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:buttonTint="@color/white"
                android:focusable="true"
                android:gravity="center_vertical"
                android:paddingStart="4dp"
                android:paddingEnd="24dp"
                android:text="Large (Better readability)"
                android:textColor="@color/white"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/tvExtra"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginTop="4dp"
                android:background="@drawable/selector_menu_sub_btn"
                android:buttonTint="@color/white"
                android:focusable="true"
                android:gravity="center_vertical"
                android:paddingStart="4dp"
                android:paddingEnd="24dp"
                android:text="Extra Large (Ideal for distant viewing)"
                android:textColor="@color/white"
                android:textSize="14sp" />
        </RadioGroup>


        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:focusable="false"
            android:text="Confirm Your Choice"
            android:textColor="@color/white"
            android:textSize="16sp" />

        <Button
            android:id="@+id/apply"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/selector_menu_red"
            android:drawableStart="@drawable/ic_shutdown"
            android:drawablePadding="12dp"
            android:focusable="true"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="Restart App Now"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp" />

        <Button
            android:id="@+id/nextLaunch"
            android:layout_width="wrap_content"
            android:layout_height="40dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/selector_menu_blue"
            android:drawableStart="@drawable/ic_apply"
            android:drawablePadding="12dp"
            android:focusable="true"
            android:gravity="center"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="Apply on Next Launch"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
