package com.google.chuangke.view

import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.KeyEvent
import android.view.ViewGroup
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatTextView
import com.google.chuangke.R
import com.google.chuangke.entity.EpgBean

interface OnEpgViewFocusedListener {
    fun onFocused(index: Int, epgBean: EpgBean)
}

interface OnEpgViewControlListener {
    fun onKey(event: KeyEvent, position: Int, index: Int, total: Int, epgBean: EpgBean): Boolean
}

class EpgView(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    private var position: Int? = null
    private var index: Int? = null
    private var total: Int? = null
    private var epgBean: EpgBean? = null
    var mOnEpgViewFocusedListener: OnEpgViewFocusedListener? = null
    var mOnEpgViewControlListener: OnEpgViewControlListener? = null

    init {
        background =
            AppCompatResources.getDrawable(context, R.drawable.programguide_item_program_background)
        focusable = FOCUSABLE
        compoundDrawablePadding = 8
        setTextSize(TypedValue.COMPLEX_UNIT_SP, 12f)
        maxLines = 1
        ellipsize = TextUtils.TruncateAt.END
        setTextColor(Color.parseColor("#FFFFFF"))
    }

    fun setData(position: Int, index: Int, total: Int, epgBean: EpgBean) {
        this.position = position
        this.index = index
        this.total = total
        this.epgBean = epgBean
        text = epgBean.name
    }

    override fun onFocusChanged(focused: Boolean, direction: Int, previouslyFocusedRect: Rect?) {
        super.onFocusChanged(focused, direction, previouslyFocusedRect)
        // Handle focus change here
        if (focused) {
            // The view has gained focus
            // Do something when the view gains focus
            mOnEpgViewFocusedListener?.onFocused(index!!, epgBean!!)
        } else {
            // The view has lost focus
            // Do something when the view loses focus
        }

        // NOTE: 让整个列都获取焦点状态
        (this.parent.parent as ViewGroup).isActivated = hasFocus()
    }

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {
        if (event.action == KeyEvent.ACTION_DOWN) {
            return mOnEpgViewControlListener?.onKey(event, position!!, index!!, total!!, epgBean!!)
                ?: false
        }
        return super.dispatchKeyEvent(event)
    }

}