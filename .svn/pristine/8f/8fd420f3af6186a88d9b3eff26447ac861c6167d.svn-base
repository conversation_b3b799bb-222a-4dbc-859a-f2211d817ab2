package com.google.chuangke.player.tvbus;


import android.content.Intent;
import android.os.Handler;
import android.os.Looper;

import androidx.media3.common.MimeTypes;
import androidx.media3.common.Player;

import com.alibaba.fastjson.JSONObject;
import com.google.chuangke.MyApplication;
import com.google.chuangke.common.Config;
import com.google.chuangke.common.Constants;
import com.google.chuangke.common.event.PlayerStatusEvent;
import com.google.chuangke.player.PlayerHelper;
import com.google.chuangke.util.SPUtils;
import com.i18n.langs.BrSi;
import com.orhanobut.logger.Logger;
import com.tvbus.engine.TVCore;
import com.tvbus.engine.TVListener;

import org.greenrobot.eventbus.EventBus;

import gojson.gojson.Gojson;

//    public static final int BUFFER_LACK_ERROR2 = 319;
//    public static final int BUFFER_LACK_ERROR = 334;
//    /******************************************防盗模块错误码******************************************/
//    private static final int CERTIFICATE_CHECK_ERROR = -120;
//    /******************************************认证模块错误码******************************************/
//    public static final int NETWORK_ERROR = -201;
//    public static final int NOT_ADDRESS_ERROR = -202;
//    public static final int AUTH_SERVER_ERROR = -203;
//    public static final int MESSAGE_ERROR = -204;
//    public static final int WRONG_USER_ERROR = -205;
//    public static final int VERSION_NOT_COMPATIBLE_ERROR = -206;
//    public static final int REPEATEDLY_LOGIN_ERROR = -210;
//    public static final int FETCHER_CODE_ERROR = -211;
//    public static final int User_BANNED_ERROR = -212;

public class LivePlaySingleton {
    public static LivePlaySingleton instance;

    private final static long MP_START_CHECK_INTERVAL = 10 * 1000 * 1000 * 1000L; // 10 second
    private TVCore mTVCore = null;
    private final LivePlayBean livePlayBean = new LivePlayBean();
    private String tvBusUrl;

    private LivePlaySingleton() {
        startTVBusService();
    }

    public static LivePlaySingleton getInstance() {
        if (instance == null) {
            synchronized (LivePlaySingleton.class) {
                if (instance == null) {
                    instance = new LivePlaySingleton();
                }
            }
        }
        return instance;
    }

    public void start(String url) {
        LivePlayRestartTask.INSTANCE.cancel();
        if (!Config.getInstance().isAuth() || url == null) {
            return;
        }
        this.tvBusUrl = url;
        reStart();
    }

    public void stop() {
        LivePlayRestartTask.INSTANCE.cancel();
        mTVCore.stop();
    }

    public void release() {
        LivePlayRestartTask.INSTANCE.cancel();
        MyApplication.context.stopService(new Intent(MyApplication.context, BrSi.class));
        instance = null;
    }

    private void reStart() {
        mTVCore.stop();

        // 路径-1，是uid解析错误，可能是uid为空
        // 这种情况不播放，清除上一帧内容，显示错误码304
        if ("-1".equals(this.tvBusUrl)) {
//            runOnUIThread(() -> PlayerHelper.getInstance().clearContent());
            new Handler().postDelayed(() -> EventBus.getDefault().post(new PlayerStatusEvent(PlayerStatusEvent.Companion.tvBusCode(99))), 2000);
        } else {
            if (this.tvBusUrl != null) {
                mTVCore.start(this.tvBusUrl);
            }
        }
    }

    private void startTVBusService() {
        mTVCore = TVCore.getInstance();

        mTVCore.setTVListener(new TVListener() {
            @Override
            public void onInited(String result) {
                Logger.d(result);

                JSONObject reJson = JSONObject.parseObject(result);
                Integer tvcore = reJson.getInteger("tvcore");
                if (tvcore != null && tvcore == 0) {
                    runOnUIThread(() -> {
                        PlayerHelper.getInstance().stopPlay();
                        livePlayBean.init();
                        if (tvBusUrl != null) {
                            mTVCore.start(tvBusUrl);
                        }
                    });
                }
            }

            @Override
            public void onStart(String result) {
                Gojson.startPlaySL(Config.getInstance().getCurrentPlayChannel().getChannelId() + "");
                Logger.d(result);
            }

            @Override
            public void onPrepared(String result) {
                Logger.d(result);

                JSONObject reJson = JSONObject.parseObject(result);
                String playbackUrl = reJson.getString("http");
                if (playbackUrl != null) {
                    livePlayBean.setPlayUrl(playbackUrl);
                    livePlayBean.setCheckTime(System.nanoTime() + MP_START_CHECK_INTERVAL);
                    runOnUIThread(() -> PlayerHelper.getInstance().startPlay("0050", livePlayBean.getPlayUrl(), 0L, MimeTypes.APPLICATION_M3U8, false));
                }
            }

            @Override
            public void onInfo(String result) {
                JSONObject reJson = JSONObject.parseObject(result);
                livePlayBean.setReplayCount(reJson.getIntValue("hls_last_conn"));
                livePlayBean.setBuffer(reJson.getIntValue("buffer"));

                EventBus.getDefault().post(new PlayerStatusEvent(PlayerStatusEvent.NORMAL));

                checkPlayer();
            }

            @Override
            public void onStop(String result) {
                Gojson.startPlaySL("0");

                JSONObject reJson = JSONObject.parseObject(result);
                int errno = reJson.getInteger("errno");

                if (errno < 0) {
                    EventBus.getDefault().post(new PlayerStatusEvent(PlayerStatusEvent.Companion.tvBusCode(errno))); //异常结束
//                    runOnUIThread(() -> PlayerHelper.getInstance().clearContent());
                } else {
                    EventBus.getDefault().post(new PlayerStatusEvent(PlayerStatusEvent.NORMAL)); //正常结束
                }

                if (errno == -104 || errno == -113) {
                    LivePlayRestartTask.INSTANCE.schedule(() -> reStart());
                }

            }

            @Override
            public void onQuit(String result) {
                Logger.d(result);
            }
        });

        String url = Config.getInstance().getBaseUrl();
        if (url == null) {
            return;
        }
        StringBuffer authUrl = new StringBuffer(url);
//        authUrl.insert(authUrl.indexOf("."), "_auth").append("v1/auth");
        authUrl.insert(authUrl.indexOf("."), "_authn2").append("v1/auth");
        String token = SPUtils.Companion.getString(MyApplication.context, Constants.SP_KEY_TOKEN, "");

        String ip = SPUtils.Companion.getString(MyApplication.context, Constants.SP_KEY_IP, "{}");
        if (ip != null && ip.contains("Singapore") && ip.contains("SG")) {
            mTVCore.setOption("doh_url", "https://dns.google/dns-query");
        }

        mTVCore.setAuthUrl(authUrl.toString());
        mTVCore.setUsername(token);
        mTVCore.setPassword("469263");
        mTVCore.setRunningMode(1);

        MyApplication.context.startService(new Intent(MyApplication.context, BrSi.class));
    }

    private void checkPlayer() {
        runOnUIThread(() -> {
            //尝试次数大于20，并且buffer大于50，说明播放器异常了，需要先停止播放器，下面再重启播放器
            if (livePlayBean.getReplayCount() > 20 && livePlayBean.getBuffer() > 50) {
                PlayerHelper.getInstance().stopPlay();
            }

            // 重试50次，buffer小于10,说明流异常，重启tvCore
            if (livePlayBean.getReplayCount() > 40 && livePlayBean.getBuffer() < 10) {
                reStart();
            }

            //每次播放开始计时，超过了10S开始检查
            if (System.nanoTime() > livePlayBean.getCheckTime()) {
                int playbackState = PlayerHelper.getInstance().getPlaybackState();

                //[如果不是空闲（也就是说在播放中） && 如果不是播完（也就是没有播放完）] == 正在播放
                //![正在播放] == 一定不在播放中
                if (playbackState == Player.STATE_IDLE || playbackState == Player.STATE_ENDED) {
                    livePlayBean.setCheckTime(System.nanoTime() + MP_START_CHECK_INTERVAL);
                    PlayerHelper.getInstance().startPlay("0050", livePlayBean.getPlayUrl(), 0L, null, false);
                }
            }
        });
    }

    private void runOnUIThread(Runnable runnable) {
        new Handler(Looper.getMainLooper()).post(runnable);
    }

}

