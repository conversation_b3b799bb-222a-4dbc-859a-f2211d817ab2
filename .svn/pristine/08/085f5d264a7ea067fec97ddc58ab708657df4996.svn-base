package com.google.chuangke.page.adapter

import android.view.KeyEvent
import android.view.View

class RecyclerExtras {

    interface OnItemClickListener {
        fun onItemClick(view: View, position: Int)
    }

    interface OnItemKeyListener {
        fun onItemKey(view: View, position: Int, keyEvent: KeyEvent, keyCode: Int)
    }

    interface OnItemKeyListener2 {
        fun onItemKey(view: View, position: Int, keyEvent: KeyEvent, keyCode: Int): Boolean
    }

    interface OnItemFocusChangeListener {
        fun onItemFocusChange(view: View, position: Int, hasFocu: Boolean)
    }
}