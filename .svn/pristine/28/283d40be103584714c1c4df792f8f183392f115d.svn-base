package com.google.chuangke.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import io.objectbox.annotation.Index
import io.objectbox.annotation.Transient
import java.io.Serializable

@Entity
class EpgBean : Serializable {

    @Id(assignable = true)
    var id: Long? = null

    @Index
    var channelId: Int? = null
    var name: String? = null
    var description: String? = null
    var beginTime: Int? = null
    var endTime: Int? = null

    @Transient
    var startTime: Int? = null
        get() {
            return field ?: beginTime
        }

    @Transient
    var finishTime: Int? = null
        get() {
            return field ?: endTime
        }

    @Transient
    var channelName: String? = null

    @Transient
    var channelNumber: Int? = null
}