package com.google.chuangke.page.dialog

import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.text.method.ScrollingMovementMethod
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BaseSpeechDialog
import com.orhanobut.logger.Logger
import java.io.BufferedReader
import java.io.InputStreamReader


class DebugDialog(
    activity: Activity, themeId: Int
) : BaseSpeechDialog(activity, themeId) {
    private lateinit var tvFlow: TextView
    private lateinit var tvFixed: TextView


    override fun onCreate(savedInstanceState: Bundle?) {
        setContentView(R.layout.dialog_debug)
        initView()

        val window: Window? = this.window
        if (window != null) {
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            window.setLayout(
                WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT
            )
            window.setDimAmount(0f)
            window.setFlags(
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
            )
        }
    }

    private fun initView() {
        tvFlow = findViewById(R.id.tv_dialog_debug_flow)
        tvFixed = findViewById(R.id.tv_dialog_debug_fixed)
        tvFlow.movementMethod = ScrollingMovementMethod()
        tvFixed.movementMethod = ScrollingMovementMethod()
    }

    fun appendFlow(info: String) {
        tvFlow.append(Html.fromHtml(info, Html.FROM_HTML_MODE_COMPACT))
    }

    fun appendFixed(info: String) {
        tvFixed.append(Html.fromHtml(info, Html.FROM_HTML_MODE_COMPACT))
    }


}

class DebugDialogHelper {
    private var debugDialog: DebugDialog? = null

    companion object {
        fun getInstance() = InstanceHelper.instance
    }

    object InstanceHelper {
        val instance = DebugDialogHelper()
    }

    init {
        logcat()
    }

    fun show(activity: Activity) {
        if (debugDialog != null && debugDialog!!.isShowing) {
            return
        }
        debugDialog = DebugDialog(activity, R.style.Dialog)
        debugDialog!!.setOwnerActivity(activity)
        debugDialog!!.show()
    }

    fun dismiss() {
        debugDialog?.let {
            if (it.isShowing)
                it.dismiss()
        }
    }

    fun isShow(): Boolean? {
        return debugDialog?.isShowing
    }

    fun appendFlow(info: String, hightlight: Boolean = false) {
        if (hightlight){
            debugDialog?.appendFlow("<p style='color:red'>$info</p>")
        }else{
            debugDialog?.appendFlow("<p>$info</p>")
        }
    }

    fun appendFixed(info: String, hightlight: Boolean = true) {
        if (hightlight){
            debugDialog?.appendFixed("<p style='color:red'>$info</p>")
        }else{
            debugDialog?.appendFixed("<p>$info</p>")
        }
    }

    private fun logcat(){
        Thread {
            try {
                val process = Runtime.getRuntime().exec(arrayOf("logcat", "-v tag *:I"))
                val reader = BufferedReader(InputStreamReader(process.inputStream))
                var line: String
                while (reader.readLine().also { line = it } != null) {
                    Handler(Looper.getMainLooper()).post {
                        if(line.startsWith("E/")) {
                            appendFlow(line, false)
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }.start()
    }


}



