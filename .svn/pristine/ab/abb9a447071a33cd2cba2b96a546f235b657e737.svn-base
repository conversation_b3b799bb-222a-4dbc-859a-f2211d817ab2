<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <layer-list>
            <item android:left="-0.5dp" android:top="-0.5dp">
                <shape>
                    <solid android:color="@color/purpler_7B0B82" />
                    <stroke android:width="0.2dp" android:color="@color/white_ccc" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item android:state_selected="true">
        <layer-list>
            <item android:left="-0.5dp" android:top="-0.5dp">
                <shape>
                    <solid android:color="@color/purpler_7B0B82" />
                    <stroke android:width="0.2dp" android:color="@color/white_ccc" />
                </shape>
            </item>
        </layer-list>
    </item>
    <item>
        <layer-list>
            <item android:left="-0.5dp" android:top="-0.5dp">
                <shape>
                    <solid android:color="@color/transparent" />
                    <stroke android:width="0.2dp" android:color="@color/white_ccc" />
                </shape>
            </item>
        </layer-list>
    </item>
</selector>