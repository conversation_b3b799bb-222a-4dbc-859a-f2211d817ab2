package com.google.chuangke.base

import android.app.Dialog
import android.content.Context
import android.view.KeyEvent
import com.google.chuangke.common.event.SearchEvent
import org.greenrobot.eventbus.EventBus

open class BaseSpeechDialog(context: Context, themeId: Int = 0) : Dialog(context, themeId) {

    override fun dispatchKeyEvent(event: KeyEvent): Bo<PERSON>an {

        if (event.action != KeyEvent.ACTION_UP && event.keyCode == KeyEvent.KEYCODE_SEARCH) {
            dismiss()
            EventBus.getDefault().post(SearchEvent())
            return true
        }

        return super.dispatchKeyEvent(event)
    }
}

open class DisableSpeechDialog(context: Context, themeId: Int = 0) : <PERSON>alog(context, themeId) {

    override fun dispatchKeyEvent(event: KeyEvent): Boolean {

        if (event.action != KeyEvent.ACTION_UP && event.keyCode == KeyEvent.KEYCODE_SEARCH) {
            return true
        }

        return super.dispatchKeyEvent(event)
    }
}