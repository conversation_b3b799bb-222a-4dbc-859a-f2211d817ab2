package com.google.chuangke.common

import android.content.Intent
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.database.DBApi
import com.google.chuangke.page.LoadInfo
import com.google.chuangke.page.SplashLoadingEvent
import com.google.chuangke.util.FileUtil
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import gojson.gojson.Gojson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache1.commons.codec.digest.DigestUtils
import org.greenrobot.eventbus.EventBus
import java.io.FileInputStream

class LiveService : LifecycleService() {

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)

        lifecycleScope.launch  {
            withContext(Dispatchers.IO) {
                // 检查token
                val token = SPUtils.getString(MyApplication.context, Constants.SP_KEY_TOKEN, null)
                if (token == null) {
                    Logger.e("token null")
                    return@withContext
                }

                // 下载otherDB
                if (!getData(Constants.DB_NAME_OTHER, token)) {
                    return@withContext
                }

                // 进入首页
                EventBus.getDefault().post(SplashLoadingEvent(LoadInfo.COMPLETE))

                // 下载otherDB
                getData(Constants.DB_NAME_EPG, token)
            }
        }

        return START_NOT_STICKY
    }

    private fun getData(dbName: String, token: String): Boolean {
        try {
            val spKey = dbName.hashCode().toString()
            val basePath = MyApplication.context.cacheDir.path
            val lastMd5 = SPUtils.getString(MyApplication.context, spKey, "")
            val data = Gojson.getData(basePath, "$dbName.db.zip", lastMd5, token)
            Logger.e(data)

            val dataJson = JSONObject.parseObject(data)
            val code = dataJson.getIntValue("code")
            val dbPath = dataJson.getString("dbPath")
            when (code) {
                0 -> return true // 无新数据
                1 -> { // 有新数据，重新加载db
                    val isInit = if (Constants.DB_NAME_EPG == dbName) {
                        DBApi.getInstance().initEpgDao(dbPath)
                    } else {
                        DBApi.getInstance().initOtherDB(dbPath)
                    }
                    if (isInit) {
                        val md5: String = DigestUtils.md5Hex(FileInputStream(dbPath))
                        SPUtils.putString(MyApplication.context, spKey , md5)
                    }
                    FileUtil.delFile(dbPath)
                    return true
                }

                else -> { // 异常
                    Logger.e("" + code)
                    return false
                }
            }
        }catch (e :Exception){
            Logger.e(e.message.toString())
        }

        return false
    }

}