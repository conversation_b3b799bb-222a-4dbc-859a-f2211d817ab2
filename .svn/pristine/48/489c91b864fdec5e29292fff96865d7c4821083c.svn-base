package com.google.chuangke.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

@Entity
class ChannelCollectionBean : Serializable {
    @Id
    var id: Long? = null
    var unid: Long? = null
    var ctId: Long? = null
    var createTime: Int? = null

    override fun toString(): String {
        return "ChannelCollectionBean(id=$id, unid=$unid, ctId=$ctId, createTime=$createTime)"
    }
}