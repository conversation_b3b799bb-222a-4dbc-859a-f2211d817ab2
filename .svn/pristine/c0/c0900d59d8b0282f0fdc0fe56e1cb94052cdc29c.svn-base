package com.google.chuangke.data.menu

import androidx.lifecycle.MutableLiveData
import com.google.chuangke.base.BaseViewModel
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.ChannelCollectionBean

class MenuFavoriteViewModel(
    private val mMenuFavoriteRepository: MenuFavoriteRepository
) : BaseViewModel() {

    val channelLiveData: MutableLiveData<List<ChannelBean>> = MutableLiveData()
    val collectionCountLiveData: MutableLiveData<Int> = MutableLiveData()

    init {
        getAllChannel()
    }

    fun getAllChannel() {
        launch {
            mMenuFavoriteRepository.getAllChannel().let {

                channelLiveData.value = it

                val collection = mMenuFavoriteRepository.getAllChannelCollection()
                collectionCountLiveData.value = collection.size

            }
        }
    }

    fun saveChannelCollection(channelId: Long, collection: Boolean) {
        launch {
            collectionCountLiveData.value?.let {
                var count = it
                if (collection) count++ else count--
                collectionCountLiveData.postValue(count)
            }
            val collectionBean = ChannelCollectionBean()
            collectionBean.unid = channelId
            collectionBean.createTime = (System.currentTimeMillis() / 1000).toInt()
            mMenuFavoriteRepository.saveChannelCollection(collectionBean)
        }
    }
}