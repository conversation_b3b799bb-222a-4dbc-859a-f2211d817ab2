package com.google.chuangke.util;

import com.google.chuangke.MyApplication;
import com.orhanobut.logger.Logger;

import java.lang.reflect.Method;

import dalvik.system.PathClassLoader;

public class DexUtil {

    public static void initDexFile(String dexPath,String className,String methodName){
        try {
            // APK 文件路径
            //String dexPath = getApplication().getFilesDir().getPath()+"/Hello.png";

            // 创建 PathClassLoader
            PathClassLoader classLoader = new PathClassLoader(dexPath, MyApplication.context.getClassLoader());

            // 加载目标类
            //Class<?> myClass = classLoader.loadClass("Hello");
            Class<?> myClass = classLoader.loadClass(className);

            // 创建实例
            Object instance = myClass.getDeclaredConstructor().newInstance();

            // 调用方法
            //Method method = myClass.getMethod("main");
            Method method = myClass.getMethod(methodName);
            method.invoke(instance);

        } catch (Exception e) {
            Logger.e(""+e.getMessage());
        }
    }
}
