package com.google.chuangke.player.multiple

import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.media3.common.MimeTypes
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.MyApplication.Companion.context
import com.google.chuangke.common.Constants
import com.google.chuangke.common.event.PlayerStatusEvent
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.player.PlayerHelper
import com.google.chuangke.util.SPUtils
import com.orhanobut.logger.Logger
import gojson.gojson.Gojson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import java.util.Timer
import java.util.TimerTask

/**
 * 加载资源类
 * 不直接创建，通过SourceLoaderPool管理
 */
class SourceLoader private constructor() {

    private var sourceArray: JSONArray? = null
    private var currentIndex = 0
    private var retryCount = 0
    private var exoPlayer: ExoPlayer? = null
    private var channelBean: ChannelBean? = null
    private var playerView: PlayerView? = null


    companion object {
        fun create(): SourceLoader {
            return SourceLoader()
        }
    }

    fun start(channelBean: ChannelBean, playerView: PlayerView) {
        playerView.visibility = View.VISIBLE
        this.channelBean = channelBean
        this.playerView = playerView
        val token = SPUtils.getString(context, Constants.SP_KEY_TOKEN, null)

        CoroutineScope(Dispatchers.IO).launch {
            val channelId = channelBean.id.toString()
            val data = Gojson.getSource(channelId, token)
            Logger.e(data)

            val jsonObject = JSONObject.parseObject(data)
            val code = jsonObject.getIntValue("code")
            if (code != 1) {
                EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.getSourceCode(code)))
                return@launch
            }

            val jsonArray = jsonObject.getJSONArray("reData")
            if (jsonArray.size == 0) {
                EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.ERROR_GET_SOURCE_NONE))
                return@launch
            }
            sourceArray = jsonArray
            retryCount = 0

            // 按顺序播放源，直到有可以播的为止
            var started = false
            for (i in jsonArray.indices) {
                started = startPlay(i, playerView)
                if (started){
                    break
                }
            }

            if (!started) {
                EventBus.getDefault().post(PlayerStatusEvent(PlayerStatusEvent.ERROR_VALID_SOURCE_NONE))
            }
            PlayerHelper.getInstance().setChannelChange(true)
        }

    }

    private fun startPlay(i:Int, playerView: PlayerView) :Boolean{
        var index = i
        if (index == sourceArray?.size){
            index = 0
        }
        currentIndex = index

        ticker()

        try {
            val jsonArray = sourceArray
            val platformName = jsonArray?.getString(index)

            val dir = context.filesDir.path
            val platform = platformName!!.split("_")[0]
            val jsonResult = when (platform) {
                "1350" -> Gojson.start1350(dir, this.channelBean!!.channelId.toString(), 0) // directv
                "1150" -> Gojson.start1150(this.channelBean!!.channelId.toString(), platformName) // fubo
//                "0650" -> Gojson.start0650(dir, this.channelBean!!.channelId.toString(), platformName) // hulu
                "0550" -> Gojson.start0550(this.channelBean!!.channelId.toString(), platformName) // dash with key
                "0250" -> Gojson.start0250(this.channelBean!!.channelId.toString(), platformName) // clear stream
//                "0050" -> Gojson.start0050(this.channelBean!!.channelId.toString(), platformName) // tvbus
                else -> return false
            }
            Logger.e(""+channelBean?.channelNumber+ jsonResult)


            val json = JSONObject.parseObject(jsonResult) ?: return false
            val code = json.getIntValue("code")
            val url = json.getString("url")
            val provider = json.getString("provider")
            val mimeType = json.getString("mimeType")
            if (code != 1) {
                return false
            }

            val mimeTypes = when (mimeType) {
                "m3u8" -> MimeTypes.APPLICATION_M3U8
                "mpd" -> MimeTypes.APPLICATION_MPD
                "mp4" -> MimeTypes.APPLICATION_MP4
                else -> MimeTypes.APPLICATION_M3U8
            }

            Handler(Looper.getMainLooper()).post {
                if (exoPlayer != null){
                    ExoPlayerPool.getInstance().releasePlayer(exoPlayer) // 如果不是第一次播放，先释放上一次的实例
                    this.playerView?.player = null
                }
                exoPlayer = ExoPlayerPool.getInstance().acquirePlayer(provider, url, null, mimeTypes, playerView)
            }

            return true
        } catch (e: Exception) {
            Logger.e(e.message.toString())
        }

        return false
    }

    private fun ticker(){
        execute {
            Handler(Looper.getMainLooper()).post {
                // 每30秒检查一遍状态，如果没有播放则选择下一个源
                if (exoPlayer == null || exoPlayer?.playbackState == Player.STATE_IDLE || exoPlayer?.playbackState == Player.STATE_ENDED) {
                    ExoPlayerPool.getInstance().releasePlayer(exoPlayer)
                    exoPlayer = null
                    playerView?.player = null
                    startPlay(currentIndex + 1, playerView!!)
                }else{
                    retryCount = 0
                    ticker()
                }
            }
        }
    }

    private var timer = Timer()
    private var timerTask: TimerTask? = null
    private fun execute(runnable: () -> Unit) {
        timerTask?.cancel()

        // 先尝试播放其他源，超过了10次停止
        if (retryCount++ >= 10) {
            return
        }

        timerTask = object : TimerTask() {
            override fun run() {
                runnable.invoke()
            }
        }
        timer.schedule(timerTask, 30000)
    }

    fun reset(){
        retryCount = 0
        timerTask?.cancel()
        ExoPlayerPool.getInstance().releasePlayer(exoPlayer)
        exoPlayer = null
        playerView?.player = null
        playerView = null
    }

    fun release(){
        reset()
        timer.cancel()
        timer.purge()
    }

}
