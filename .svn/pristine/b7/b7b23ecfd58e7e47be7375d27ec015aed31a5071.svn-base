package com.google.chuangke.ext

import android.app.Dialog
import android.view.WindowManager
import android.content.Context
import android.content.res.Resources
import android.util.TypedValue
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StyleRes
import androidx.appcompat.view.ContextThemeWrapper

/**
 * Delay request focus
 */
fun View.delayRequestFocus() {
    this.let {
        it.postDelayed({
            it.requestFocus()
        }, 300L)
    }
}


fun Dialog.shows() {
    window?.let {
        it.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE
        )
        show()
        it.clearFlags(WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE);
        val layoutParams = it.attributes
        layoutParams?.height = WindowManager.LayoutParams.MATCH_PARENT
        layoutParams?.width = WindowManager.LayoutParams.MATCH_PARENT
        it.setDimAmount(0f) // activity去除阴影
        it.attributes = layoutParams
    }
}

// 测距
fun Int.toExactlyMeasureSpec() = View.MeasureSpec.makeMeasureSpec(this, View.MeasureSpec.EXACTLY)
fun Int.toAtMostMeasureSpec() = View.MeasureSpec.makeMeasureSpec(this, View.MeasureSpec.AT_MOST)

fun View.defaultWidthMeasureSpec(parent: ViewGroup): Int {
    return when (layoutParams.width) {
        ViewGroup.LayoutParams.MATCH_PARENT -> parent.measuredWidth.toExactlyMeasureSpec()
        ViewGroup.LayoutParams.WRAP_CONTENT -> parent.measuredWidth.toAtMostMeasureSpec()
        else -> layoutParams.width.toExactlyMeasureSpec()
    }
}

fun View.defaultHeightMeasureSpec(parent: ViewGroup): Int {
    return when (layoutParams.height) {
        ViewGroup.LayoutParams.MATCH_PARENT -> parent.measuredHeight.toExactlyMeasureSpec()
        ViewGroup.LayoutParams.WRAP_CONTENT -> parent.measuredHeight.toAtMostMeasureSpec()
        else -> layoutParams.height.toExactlyMeasureSpec()
    }
}

fun View.autoMeasure(parent: ViewGroup) {
    measure(this.defaultWidthMeasureSpec(parent), this.defaultHeightMeasureSpec(parent))
}

fun View.autoLayout(parent: ViewGroup, x: Int = 0, y: Int = 0, fromRight: Boolean = false) {
    if (!fromRight) {
        layout(x, y, x + measuredWidth, y + measuredHeight)
    } else {
        autoLayout(parent, parent.measuredWidth - x - measuredWidth, y)
    }
}

val Int.dp
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, this.toFloat(), Resources.getSystem().displayMetrics
    ).toInt()

val Float.sp
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP, this, Resources.getSystem().displayMetrics
    )

fun Context.toTheme(@StyleRes style: Int) = ContextThemeWrapper(this, style)