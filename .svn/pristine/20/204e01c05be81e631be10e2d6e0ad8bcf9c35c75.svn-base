<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/selector_feedback"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <TextView
        android:id="@+id/tv_item_fragment_feedback_faq_id"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="135dp"
        android:layout_marginTop="10dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_item_fragment_feedback_faq_question"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="164dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_item_fragment_feedback_faq_id"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_item_fragment_feedback_faq_answer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="@color/white_ccc"
        android:textSize="11sp"
        android:paddingBottom="10dp"
        app:layout_constraintEnd_toEndOf="@+id/tv_item_fragment_feedback_faq_question"
        app:layout_constraintStart_toStartOf="@+id/tv_item_fragment_feedback_faq_question"
        app:layout_constraintTop_toBottomOf="@+id/tv_item_fragment_feedback_faq_question" />
</androidx.constraintlayout.widget.ConstraintLayout>
