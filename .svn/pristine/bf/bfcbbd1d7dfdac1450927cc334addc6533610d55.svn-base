package com.google.chuangke.page

import android.content.Intent
import android.graphics.Paint
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import com.alibaba.fastjson.JSONObject
import com.google.chuangke.base.BaseActivity
import com.google.chuangke.common.Constants
import com.google.chuangke.common.EventFilter
import com.google.chuangke.common.UserHelper
import com.google.chuangke.databinding.ActivityLoginBinding
import com.google.chuangke.ext.toast
import com.google.chuangke.http.HttpCallback
import com.google.chuangke.http.HttpHelper
import com.google.chuangke.player.LivePlayHelper
import com.google.chuangke.util.SPUtils

class LoginActivity : BaseActivity<ActivityLoginBinding>(ActivityLoginBinding::inflate) {

    private lateinit var loginEventFilter: EventFilter

    override fun initView() {
        binding.tvActivityLoginTips.paint.flags = Paint.UNDERLINE_TEXT_FLAG
        binding.etActivityLoginAccountNumber.requestFocus()
        loginEventFilter =
            EventFilter.Builder().setType(EventFilter.TYPE_OUT_TIME).setTime(1000).build()
    }

    override fun initListener() {
        binding.ivActivityLoginBack.setOnClickListener {
            onBack()
        }

        binding.tvActivityLoginLogin.setOnClickListener {
            if (loginEventFilter.filter()) {
                login()
            }
        }
    }

    private fun login() {
//        LoadingDialogHelper.getInstance().show(this@LoginActivity)
        val loginCode = binding.etActivityLoginAccountNumber.text.toString()
        if (TextUtils.isEmpty(loginCode)) {
            binding.tvActivityLoginFailed.visibility = View.VISIBLE
            return
        }

        val params = JSONObject()
        params["loginCode"] = loginCode
        HttpHelper.getInstance().postApi("loginByCode", params, object : HttpCallback() {
            override fun onSuccess(jsonObject: JSONObject) {
                super.onSuccess(jsonObject)
                val code = jsonObject.getInteger("code")
                if (code != 1) {
                    val reData = jsonObject.getJSONObject("reData")
                    val msg = reData.getString("msg")
                    runOnUiThread {
                        toast(msg)
//                        LoadingDialogHelper.getInstance().dismiss()
                    }

                    return
                }
                //如果已登录测试账号，需要退出原来的账号
                var token = SPUtils.getString(this@LoginActivity, Constants.SP_KEY_TOKEN, null)
                if(!TextUtils.isEmpty(token)){
                    HttpHelper.getInstance().postApi("logout", JSONObject(), object : HttpCallback() {})
                }

                val reData = jsonObject.getString("reData")
                val respJson = JSONObject.parseObject(reData)
                token = respJson.getString("token")
                SPUtils.putString(this@LoginActivity, Constants.SP_KEY_TOKEN, token)

                startActivity(Intent(this@LoginActivity, SplashActivity::class.java))
                finish()
            }

            override fun onError(err: String?) {
//                mTvFailed.visibility = View.VISIBLE
            }
        })
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {

        return super.onKeyDown(keyCode, event)
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        onBack()
        super.onBackPressed()
    }

    private fun onBack(){
        if(LivePlayHelper.getInstance().initPlay){
            UserHelper.getInstance().exit()
        }else{
            finish()
        }
    }


    override fun isRegisterEventBus(): Boolean {
        return false
    }

}