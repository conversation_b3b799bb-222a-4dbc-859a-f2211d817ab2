<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/shape_dialog_epg"
        android:orientation="vertical"
        android:padding="36dp">

        <TextView
            style="@style/font_bold"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:focusable="false"
            android:text="Custom Group"
            android:textColor="@color/white_eee"
            android:textSize="19sp" />

        <EditText
            android:id="@+id/et_dialog_custom_group"
            style="@style/font_bold"
            android:layout_width="320dp"
            android:layout_height="45dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:background="@drawable/selector_input2"
            android:focusable="true"
            android:gravity="center_vertical"
            android:hint="Group Name"
            android:maxLength="20"
            android:paddingStart="26.5dp"
            android:paddingEnd="26.5dp"
            android:singleLine="true"
            android:textColor="@color/selector_color_hint"
            android:textColorHint="@color/selector_color_hint"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_dialog_custom_group_save"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="35dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="35dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="SAVE"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_dialog_custom_group_cancel"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="35dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="15dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="CANCEL"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tv_dialog_custom_group_delete"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="35dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="25dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="DELETE"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp"
            android:visibility="gone"
            tools:visibility="visible" />
    </LinearLayout>

</RelativeLayout>