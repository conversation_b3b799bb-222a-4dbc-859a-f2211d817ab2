<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <bitmap android:gravity="center" android:src="@mipmap/ic_menu_app_selected" android:tileMode="disabled" />
    </item>
    <item android:state_focused="true">
        <bitmap android:gravity="center" android:src="@mipmap/ic_menu_app_selected" android:tileMode="disabled" />
    </item>
    <item android:state_selected="true">
        <bitmap android:gravity="center" android:src="@mipmap/ic_menu_app_selected" android:tileMode="disabled" />
    </item>
    <item android:state_focused="false">
        <bitmap android:gravity="center" android:src="@mipmap/ic_menu_app" android:tileMode="disabled" />
    </item>
    <item android:state_selected="false">
        <bitmap android:gravity="center" android:src="@mipmap/ic_menu_app" android:tileMode="disabled" />
    </item>
</selector>


