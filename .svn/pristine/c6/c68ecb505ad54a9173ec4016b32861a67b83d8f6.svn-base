package com.google.chuangke.player;

import android.util.Log;

import com.alibaba.fastjson.JSONArray;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 每隔30秒检查播放器状态是否在播放（为了防止刚换台就进入检查，在每次播放源时重新开始计时）
 * 如果没有播放，则尝试播放当前频道的下一个源，同时进入30秒后检查
 * 如果尝试10次还没有播放，则重新获取频道的源
 * 换台后重置尝试次数，检查到播放成功重置尝试次数
 */
public enum SourceHelper {
    INSTANCE;

    private final Timer timer = new Timer();
    private TimerTask timerTask;

    private final static long checkTime = 30000; // 检查间隔
    private final static int maxRetryCount = 10; // 最大重试次数

    private JSONArray sourceArray;
    private int currentIndex;
    private int retryCount;

    SourceHelper() {

    }

    public void cancel() {
        if (timerTask != null) {
            timerTask.cancel();
        }
    }

    public void schedule(Runnable runnable) {
        Log.e("==========", "currentIndex:"+currentIndex+", retryCount:"+retryCount);
        if (timerTask != null) {
            timerTask.cancel();
        }

        // 先尝试播放其他源，超过了10次，重新获取源
        if (retryCount++ >= maxRetryCount){
            PlayerHelper.getInstance().replay();
            return;
        }
        timerTask = new TimerTask() {
            @Override
            public void run() {
                runnable.run();
            }
        };
        timer.schedule(timerTask, checkTime);
    }

    public int getCurrentIndex() {
        return currentIndex;
    }

    public void setCurrentIndex(int currentIndex) {
        this.currentIndex = currentIndex;
    }

    public JSONArray getSourceArray() {
        return sourceArray;
    }

    public void setSourceArray(JSONArray sourceArray) {
        this.sourceArray = sourceArray;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }
}
