<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_item_tag_root"
    android:layout_width="match_parent"
    android:layout_height="30dp"
    android:background="@drawable/selector_feedback"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/tv_item_fragment_feedback_id"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="18dp"
        android:textAlignment="center"
        android:focusable="false"
        android:layout_marginEnd="18dp"
        android:textSize="13sp"
        android:textColor="@color/white_ccc" />

    <TextView
        android:id="@+id/tv_item_fragment_feedback_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:focusable="false"
        android:textColor="@color/white_ccc"
        android:textSize="13sp" />

</LinearLayout>