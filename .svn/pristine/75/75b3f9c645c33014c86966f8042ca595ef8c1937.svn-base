package com.google.chuangke.page.presenter

import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import com.google.chuangke.R
import com.google.chuangke.base.BasePresenter
import com.google.chuangke.entity.TagBean

class EpgTagPresenter : BasePresenter<TagBean>() {

    override fun layoutId(): Int {
        return R.layout.programguide_item_dialog_epg_tag
    }

    override fun addFocusTextStyle(): List<Int> {
        return mutableListOf()
    }

    override fun onKeyListener(v: View, keyCode: Int, event: KeyEvent): <PERSON><PERSON>an {
        return false
    }

    override fun bindViewHolder(view: View, item: TagBean) {
        (view as TextView).text = item.name
    }

}