<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) 2020, Egeniq
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true">
        <shape>
            <solid android:color="@color/programguide_filter_background_focused"/>
            <stroke android:width="1dp" android:color="@color/programguide_filter_border_focused" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    <item>
        <shape>
            <solid android:color="@color/programguide_filter_background_default"/>
            <stroke android:width="1dp" android:color="@color/programguide_filter_border_default" />
            <corners android:radius="4dp" />
        </shape>
    </item>
</selector>