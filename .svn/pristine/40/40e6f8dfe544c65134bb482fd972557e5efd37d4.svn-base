<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.leanback.widget.HorizontalGridView
        android:id="@+id/fragment_sport_top"
        android:layout_width="0dp"
        android:layout_height="216dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.leanback.widget.HorizontalGridView
        android:id="@+id/fragment_sport_middle"
        android:layout_width="0dp"
        android:layout_height="108dp"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fragment_sport_top" />

    <androidx.leanback.widget.HorizontalGridView
        android:id="@+id/fragment_sport_bottom"
        android:layout_width="0dp"
        android:layout_height="108dp"
        android:layout_marginTop="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/fragment_sport_middle" />

</androidx.constraintlayout.widget.ConstraintLayout>