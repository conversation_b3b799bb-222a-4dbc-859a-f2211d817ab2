package com.google.chuangke.data

import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

class EpgRepository(private val mDBApi: DBApi) {

    suspend fun getEpgListByDay(channelBean: ChannelBean, date: Date): List<EpgBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.getEpgListByDay(channelBean, date)
        }
    }
    suspend fun getEspecialEpgListByDay(channelBean: ChannelBean, date: Date): List<EpgBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.getEspecialEpgListByDay(channelBean, date)
        }
    }

    suspend fun getCurrentEpgList(channelBean: ChannelBean, date: Date): List<EpgBean> {
        return withContext(Dispatchers.IO) {
            mDBApi.getCurrentEpgList(channelBean, date)
        }
    }

}