package com.google.chuangke.view

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import android.view.KeyEvent
import android.view.ViewGroup
import androidx.core.view.children
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.ext.dp
import com.google.chuangke.ext.toExactlyMeasureSpec

class RowView(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : ViewGroup(context, attrs, defStyleAttr), OnEpgViewFocusedListener, OnEpgViewControlListener {

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    var mXOnEpgViewFocusedListener: OnEpgViewFocusedListener? = null
    var mXOnEpgViewControlListener: OnEpgViewControlListener? = null

    // epg total width
    private val itemTotalWidth: Int = 600.dp
    private val itemHeight: Int = 44.dp
    private val itemSpace = 0.dp
    private val itemPadding = 10.dp

    // 2.5 hour
    private val totalTime = 150
    private val perWidth: Int = itemTotalWidth / totalTime

    private var epgBeans: List<EpgBean>? = null

    fun setData(position: Int, epgBeans: List<EpgBean>) {
        this.epgBeans = epgBeans
        removeAllViews()
        for ((index, epg) in epgBeans.withIndex()) {
            EpgView(context).apply {
                val epgBean = epgBeans[index]
                val itemWidth =
                    perWidth * ((epgBean.finishTime!! - epgBean.startTime!!) / 60) - itemSpace
                layoutParams = LayoutParams(
                    itemWidth, itemHeight
                )
                setPadding(itemPadding, 0, itemPadding, 0)
                gravity = Gravity.CENTER_VERTICAL
                tag = epgBean
                setData(position, index, epgBeans.size, epg)
                mOnEpgViewFocusedListener = this@RowView
                mOnEpgViewControlListener = this@RowView
                addView(this)
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        children.forEach {
            it.measure(
                it.layoutParams.width.toExactlyMeasureSpec(),
                it.layoutParams.height.toExactlyMeasureSpec()
            )
        }
        setMeasuredDimension(measuredWidth, measuredHeight)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        var currentWidth = 0
        children.forEach {
            it.layout(currentWidth, 0, currentWidth + it.measuredWidth, it.measuredHeight)
            currentWidth += (it.measuredWidth + itemSpace)
        }
    }

    override fun onFocused(index: Int, epgBean: EpgBean) {
        mXOnEpgViewFocusedListener?.onFocused(index, epgBean)
    }

    override fun onKey(
        event: KeyEvent, position: Int, index: Int, total: Int, epgBean: EpgBean
    ): Boolean {
        return mXOnEpgViewControlListener?.onKey(event, position, index, total, epgBean) ?: false
    }

    /**
     * Focus the item which close to the target time
     */
    fun focusTargetItem(targetTime: Int) {
        if (epgBeans.isNullOrEmpty()) {
            getChildAt(0).requestFocus()
            return
        }

        var position = 0
        for ((index, epg) in epgBeans!!.withIndex()) {
            if (targetTime > epg.startTime!! && targetTime < epg.finishTime!!) {
                position = index
                break
            }
        }
        getChildAt(position).requestFocus()
    }
}