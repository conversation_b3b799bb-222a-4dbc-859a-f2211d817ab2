<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="480dp"
        android:layout_height="240dp"
        android:background="@drawable/shape_round_primary2"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:text="Enter ZIP Code"
            android:textColor="@color/white"
            android:textSize="18sp" />

        <EditText
            android:id="@+id/et_zipcode"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="45dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="30dp"
            android:background="@drawable/selector_input"
            android:focusable="true"
            android:gravity="center_vertical"
            android:hint="@string/login_enter_code"
            android:inputType="number"
            android:maxLength="5"
            android:paddingStart="26.5dp"
            android:paddingEnd="26.5dp"
            android:singleLine="true"
            android:textColor="@color/selector_color_hint"
            android:textColorHint="@color/selector_color_hint"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/tv_apply"
            style="@style/font_bold"
            android:layout_width="220dp"
            android:layout_height="40dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="48dp"
            android:background="@drawable/selector_menu_sub_btn"
            android:focusable="true"
            android:gravity="center"
            android:text="Apply"
            android:textColor="@color/selector_color_menu_sub_btn"
            android:textSize="14sp" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>