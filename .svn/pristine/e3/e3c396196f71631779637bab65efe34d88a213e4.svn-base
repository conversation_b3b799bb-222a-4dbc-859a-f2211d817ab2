<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@drawable/img_channel_error_nba"
    android:descendantFocusability="afterDescendants"
    android:focusable="true"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:focusable="false" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="105dp"
        android:background="#CC000000"
        android:descendantFocusability="afterDescendants"
        android:focusable="true"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_dialog_action_bar_logo"
            android:layout_width="140dp"
            android:paddingStart="48dp"
            android:paddingTop="24dp"
            android:paddingBottom="24dp"
            android:layout_height="match_parent"
            android:contentDescription="@null"
            android:focusable="false"
            android:padding="8dp"
            android:scaleType="fitCenter" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginEnd="20dp"
            android:descendantFocusability="afterDescendants"
            android:focusable="true"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_dialog_action_bar_channel_no"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48dp"
                    android:focusable="false"
                    android:textColor="@color/white"
                    android:textSize="15sp"
                    tools:text="807" />

                <TextView
                    android:id="@+id/tv_dialog_action_bar_channel_name"
                    style="@style/font_bold"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:focusable="false"
                    android:textColor="@color/white"
                    android:textSize="17sp"
                    tools:text="RECORD TV HD" />

                <ImageView
                    android:id="@+id/iv_dialog_action_bar_replay"
                    android:layout_width="15dp"
                    android:layout_height="15dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="9dp"
                    android:layout_marginTop="1dp"
                    android:contentDescription="@null"
                    android:focusable="false"
                    android:src="@mipmap/ic_action_bar_replay"
                    android:visibility="invisible"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_fragment_live_info_current_resolution"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:background="@drawable/shape_black_blank"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:textColor="@color/white"
                    android:textSize="13sp"
                    android:visibility="gone"
                    tools:text="1920x1080"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_fragment_live_info_current_rate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:background="@drawable/shape_black_blank"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:textColor="@color/white"
                    android:textSize="13sp"
                    android:visibility="gone"
                    tools:text="65fps"
                    tools:visibility="visible" />

                <TextClock
                    android:id="@+id/tc_dialog_action_bar_clock"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="50dp"
                    android:layout_weight="1"
                    android:focusable="false"
                    android:format12Hour="M-dd-yyyy hh:mm:ss aa"
                    android:format24Hour="M-dd-yyyy HH:mm:ss"
                    android:gravity="end"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    tools:text="02-06-2022 16:51:54" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_dialog_action_bar_process"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:descendantFocusability="afterDescendants"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_dialog_action_bar_start_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48dp"
                    android:layout_marginEnd="-10dp"
                    android:focusable="false"
                    android:gravity="end"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    tools:ignore="SmallSp"
                    tools:text="22:22:22" />

                <SeekBar
                    android:id="@+id/sb_dialog_action_bar_process"
                    style="@style/update_progress_horizontal"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="1dp"
                    android:layout_weight="1"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:max="100"
                    android:thumbOffset="0dip"
                    tools:progress="90" />

                <TextView
                    android:id="@+id/tv_dialog_action_bar_end_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="-10dp"
                    android:layout_marginEnd="48dp"
                    android:focusable="false"
                    android:textColor="@color/white"
                    android:textSize="10sp"
                    tools:ignore="SmallSp"
                    tools:text="36:30:58" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_dialog_action_bar_current"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:descendantFocusability="afterDescendants"
                android:focusable="true"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48dp"
                    android:focusable="false"
                    android:text="@string/action_bar_current"
                    android:textColor="#CCCCCC"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_dialog_action_bar_current"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:maxLines="1"
                    android:textColor="#CCCCCC"
                    android:textSize="12sp"
                    tools:text="Congresso Pa" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:focusable="false"
                    android:text="@string/action_bar_next"
                    android:textColor="#CCCCCC"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_dialog_action_bar_next"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="50dp"
                    android:focusable="false"
                    android:textColor="#CCCCCC"
                    android:textSize="12sp"
                    tools:text="Congresso Pa" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_dialog_action_bar_buff"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48dp"
                    android:drawablePadding="3dp"
                    android:focusable="false"
                    android:text="@string/action_bar_channel"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_ok" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:focusable="false"
                    android:text="@string/action_bar_information"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_infor" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:focusable="false"
                    android:text="@string/action_bar_setting"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_setting" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:focusable="false"
                    android:text="@string/action_bar_change_channel"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_channel" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_dialog_action_bar_replay"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="9dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="48dp"
                    android:drawablePadding="3dp"
                    android:text="@string/action_bar_setting"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_setting" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:text="@string/action_bar_fast_rewind"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_rewind" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:text="@string/action_bar_play_pause"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_ok" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:text="@string/action_bar_fast_forward"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_fast_foward" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:text="@string/action_bar_info"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_info" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="50dp"
                    android:drawablePadding="3dp"
                    android:text="@string/action_bar_return_to_epg"
                    android:textColor="#CCCCCC"
                    android:textSize="11sp"
                    app:drawableStartCompat="@mipmap/ic_action_bar_return_epg" />
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</LinearLayout>