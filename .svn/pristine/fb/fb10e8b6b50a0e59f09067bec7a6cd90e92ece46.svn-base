package com.google.chuangke.data

import com.google.chuangke.database.DBApi
import com.google.chuangke.entity.CustomTagBean
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class ManageCustomTagRepository(private val dbApi: DBApi) {

    suspend fun getCustomTags(): List<CustomTagBean> {
        return withContext(Dispatchers.IO) {
            dbApi.getCustomTags()
        }
    }

}