package com.google.chuangke.page

import android.Manifest
import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.animation.BounceInterpolator
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.alibaba.fastjson.JSON
import com.google.chuangke.R
import com.google.chuangke.common.Config
import com.google.chuangke.common.Constants
import com.google.chuangke.common.EventFilter
import com.google.chuangke.common.UnitCallback
import com.google.chuangke.common.UserHelper
import com.google.chuangke.common.event.ActionBarSettingEpgEvent
import com.google.chuangke.common.event.ChannelPlayEvent
import com.google.chuangke.common.event.CloseChannelEvent
import com.google.chuangke.common.event.CustomGroupEvent
import com.google.chuangke.common.event.EditGroupEvent
import com.google.chuangke.common.event.FeedbackDialogEvent
import com.google.chuangke.common.event.LiveChangeEvent
import com.google.chuangke.common.event.SettingEvent
import com.google.chuangke.data.MainViewModel
import com.google.chuangke.database.DBApi
import com.google.chuangke.databinding.ActivityMainBinding
import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean
import com.google.chuangke.ext.shows
import com.google.chuangke.ext.toast
import com.google.chuangke.page.center.CenterFragment
import com.google.chuangke.page.dialog.ActionBarDialogHelper
import com.google.chuangke.page.dialog.ActionBarSettingDialog
import com.google.chuangke.page.dialog.CustomGroupDialog
import com.google.chuangke.page.dialog.FeedbackDialogFragment
import com.google.chuangke.page.dialog.LoadingDialogHelper
import com.google.chuangke.page.dialog.SettingDialog
import com.google.chuangke.page.dialog.TextAppearanceDialog
import com.google.chuangke.page.dialog.UnlockDialog
import com.google.chuangke.page.listener.OnProgramSelectListener
import com.google.chuangke.page.multiple.MultipleScreenActivity
import com.google.chuangke.player.LivePlayHelper
import com.google.chuangke.player.PlayerHelper
import com.google.chuangke.player.SourceHelper
import com.google.chuangke.util.DateUtil
import com.google.chuangke.util.FileHelper
import com.orhanobut.logger.Logger
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.koin.androidx.viewmodel.ext.android.viewModel
import kotlin.math.abs


class MainActivity : SpeechActivity<ActivityMainBinding>(ActivityMainBinding::inflate),
    OnProgramSelectListener {
    private val mMainViewModel: MainViewModel by viewModel()
    private lateinit var backEventFilter: EventFilter

    //    private lateinit var debugEventFilter: EventFilter
    private lateinit var recordEventFilter: EventFilter

    private var mCenterFragment: CenterFragment? = null

    private var mActionBarSettingDialog: ActionBarSettingDialog? = null
    private var mClassicEpgFragment: ClassicEpgFragment? = null
    private var mSettingDialog: SettingDialog? = null
    private var mUnlockDialog: UnlockDialog? = null
    private var mTextAppearanceDialog: TextAppearanceDialog? = null
    private var mFeedbackDialogFragment: FeedbackDialogFragment? = null

    // 数字换台
    private var numberInputBegin = false
    private var numberString = StringBuilder()
    private val numberChangeChannelHandler = Handler(Looper.myLooper()!!) {
        when (it.what) {
            // 数字换台
            MSG_NUMBER_CHANGE_CHANNEL -> changeChannelByNumber()
        }
        return@Handler false
    }

    // 1小时
    private val playBackMaxTime: Long = 3600L
    private var playBackTimeStep: Long = 60L
    private var playBackTime: Long = 0L

    private var pauseTime: Long = 0

    private val playbackHandler = Handler(Looper.myLooper()!!) {
        when (it.what) {
            MSG_PLAYBACK -> playback()
        }
        return@Handler false
    }

    private fun playback() {
        binding.tvActivityLivePlayback.visibility = View.GONE

        LivePlayHelper.getInstance().initPlay = true

        LivePlayHelper.getInstance()
            .channelPlay(
                Config.getInstance().currentPlayChannel!!,
                false,
                playback = true,
                playBackTime
            )
    }

    override fun initView() {
        PlayerHelper.getInstance().init(binding.spvActivityMain)
        initData()
        initObserve()
        initReadWritePermission()
//        printFocus()
    }

    private fun initReadWritePermission() {
        Config.getInstance().isRequestPermission = true
        if (ContextCompat.checkSelfPermission(
                this, Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED || ContextCompat.checkSelfPermission(
                this, Manifest.permission.READ_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            // 如果权限未被授予，请求权限
            ActivityCompat.requestPermissions(
                this, arrayOf(
                    Manifest.permission.WRITE_EXTERNAL_STORAGE,
                    Manifest.permission.READ_EXTERNAL_STORAGE
                ), REQUEST_READ_PERMISSION
            )
        } else {
            DBApi.getInstance().initCustomTag()
        }

    }

    private fun initObserve() {
        mMainViewModel.searchChannelList.observe(this) {
            if (dialog.isShowing) dialog.setResultList(it)
        }
        // 去解锁
        mMainViewModel.unlockLiveData.observe(this) {
            showUnlockDialog(it)
        }
    }

    private fun initData() {
        mMainViewModel.initData()

        // 其它资源下载
        UserHelper.getInstance().lazyLoad()

        // 初始化播放源的参数
        SourceHelper.INSTANCE.initParam()
    }

    override fun initListener() {
        backEventFilter =
            EventFilter.Builder().setType(EventFilter.TYPE_IN_TIME).setTime(2000).setNumOfTime(2)
                .build()
//        debugEventFilter =
//            EventFilter.Builder().setType(EventFilter.TYPE_IN_TIME).setTime(3000).setNumOfTime(8)
//                .build()
        recordEventFilter =
            EventFilter.Builder().setType(EventFilter.TYPE_OUT_TIME).setTime(1000).build()
    }


    @SuppressLint("SetTextI18n")
    private fun showPlayback(rewind: Boolean) {
        val time = System.currentTimeMillis() / 1000 + playBackTime
        binding.tvActivityLivePlayback.text = DateUtil.getScheduleTimeHour2(time.toInt())
        if (rewind) {
            binding.tvActivityLivePlayback.setCompoundDrawablesWithIntrinsicBounds(
                R.drawable.ic_movie_info_rewind,
                0,
                0,
                0
            )
        } else {
            binding.tvActivityLivePlayback.setCompoundDrawablesWithIntrinsicBounds(
                0,
                0,
                R.drawable.ic_movie_info_forward,
                0
            )
        }
        binding.tvActivityLivePlayback.visibility = View.VISIBLE

        showPlaybackIcon()

        playbackHandler.removeMessages(MSG_PLAYBACK)
        playbackHandler.sendEmptyMessageDelayed(
            MSG_PLAYBACK, 3000L
        )
    }

    private fun showPlaybackIcon() {
        if (playBackTime >= 0) {
            binding.ivActivityLivePlayback.visibility = View.GONE
        } else {
            binding.ivActivityLivePlayback.visibility = View.VISIBLE
        }
    }


    override fun onKeyDown(keyCode: Int, keyEvent: KeyEvent): Boolean {

        if (keyEvent.action == KeyEvent.ACTION_UP) {
            return false
        }

        if (mClassicEpgFragment != null && mClassicEpgFragment!!.isVisible) return false

        when (keyCode) {
//            KeyEvent.KEYCODE_8 -> {
//                if (debugEventFilter.filter()) {
//                    DebugDialogHelper.getInstance().show(this@MainActivity)
//                }
//            }

            KeyEvent.KEYCODE_SETTINGS, KeyEvent.KEYCODE_MENU -> {
                ActionBarDialogHelper.getInstance().dismiss()
                hiddeChannelDialog()
                showActionBarSetting()
                return true
            }

            2019, 172 -> { //epg键
                if (mCenterFragment != null && mCenterFragment!!.isVisible) {
                    // 关闭频道页面
                    hiddeChannelDialog()
                }
                ActionBarDialogHelper.getInstance().dismiss()
                showEpg()
                return true
            }

            KeyEvent.KEYCODE_DPAD_LEFT -> {
                if (abs(playBackTime) < (playBackMaxTime - playBackTimeStep)
                    && (mCenterFragment == null || mCenterFragment?.isVisible == false)
                    && (mClassicEpgFragment == null || mClassicEpgFragment?.isVisible == false)
                    && (mFeedbackDialogFragment == null || mFeedbackDialogFragment?.isVisible == false)
                ) {
                    playBackTime -= playBackTimeStep
                    showPlayback(true)
                    return true
                }
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> {
                if (playBackTime < 0
                    && (mCenterFragment == null || mCenterFragment?.isVisible == false)
                    && (mClassicEpgFragment == null || mClassicEpgFragment?.isVisible == false)
                    && (mFeedbackDialogFragment == null || mFeedbackDialogFragment?.isVisible == false)
                ) {
                    playBackTime += playBackTimeStep
                    showPlayback(false)
                    return true
                }
            }
        }

        // 如果channel页面开启的时候，确认按键递交给它们自己处理
        if (mCenterFragment?.isVisible == true) {
            if (keyCode == KeyEvent.KEYCODE_DPAD_CENTER || keyCode == KeyEvent.KEYCODE_ENTER) return false
        }

        return if (Config.getInstance().playType == 1) { //回播的时候的按键处理
            playBackKeyEvent(keyCode)
        } else { //直播
            playLiveKeyEvent(keyCode)
        }
    }

    private fun showActionBarSetting() {
        if (mActionBarSettingDialog == null) {
            mActionBarSettingDialog = ActionBarSettingDialog(this, R.style.Dialog)
        } else {
            mActionBarSettingDialog?.initData()
        }
        mActionBarSettingDialog?.shows()
    }

    /**
     * 双击退出
     */
    private fun backPressed() {
//        if (DebugDialogHelper.getInstance().isShow() == true) {
//            DebugDialogHelper.getInstance().dismiss()
//            return
//        }

        if (backEventFilter.filter()) {
            UserHelper.getInstance().exit()
        } else {
            toast(getString(R.string.main_exit_app))
        }
    }

    /**
     * 数字换台
     */
    private fun numberChange(keyCode: Int) {
        if (mCenterFragment != null && mCenterFragment!!.isVisible) {
            hiddeChannelDialog()
        }
        if (numberString.length < 4) numberString.append(keyCode - 7)
        binding.tvActivityMainNo.text = numberString
        if (!numberInputBegin) {
            binding.tvActivityMainNo.visibility = View.VISIBLE
            numberInputBegin = true
            numberChangeChannelHandler.sendEmptyMessageDelayed(
                MSG_NUMBER_CHANGE_CHANNEL, Constants.NUMBER_CHANGE_CHANNEL_TIME
            )
        }
    }

    /**
     * 数字换台
     */
    private fun changeChannelByNumber() {
        mMainViewModel.changeChannelByNumber(numberString.toString().toInt())
        numberInputBegin = false
        numberString.clear()
        binding.tvActivityMainNo.visibility = View.GONE
    }

    private fun showChannelDialog() {
        // 如果buffer还在，就隐藏
        ActionBarDialogHelper.getInstance().dismiss(this)
        LoadingDialogHelper.getInstance().dismiss(this)

        if (mCenterFragment != null && mCenterFragment!!.isVisible) {
            return
        }
        val ft = supportFragmentManager.beginTransaction()
        if (mCenterFragment == null) {
            mCenterFragment = CenterFragment(this)
            ft.add(android.R.id.content, mCenterFragment!!, "mCenterFragment")
        } else {
            ft.show(mCenterFragment!!)
        }
        ft.commit()
    }

    private fun showEpg() {
        showClassicEpgPage()
    }

    fun showClassicEpgPage() {
        val ft = supportFragmentManager.beginTransaction()
        if (mClassicEpgFragment == null) {
            mClassicEpgFragment = ClassicEpgFragment(this)
            ft.add(android.R.id.content, mClassicEpgFragment!!, "mClassicEpgFragment")
        } else {
            ft.show(mClassicEpgFragment!!)
        }
        ft.commit()
    }

    /**
     * 回播时的按键处理
     */
    private fun playBackKeyEvent(keyCode: Int): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_LEFT -> { //回退
                ActionBarDialogHelper.getInstance()
                    .show(this@MainActivity, Config.getInstance().currentPlayEpg)
                return true
            }

            KeyEvent.KEYCODE_DPAD_RIGHT -> { //快进
                ActionBarDialogHelper.getInstance()
                    .show(this@MainActivity, Config.getInstance().currentPlayEpg)
                return true
            }

            KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_ENTER -> { //暂停、播放
                ActionBarDialogHelper.getInstance()
                    .show(this@MainActivity, Config.getInstance().currentPlayEpg)
                PlayerHelper.getInstance().toggle()
                return true
            }

            2007, KeyEvent.KEYCODE_DPAD_DOWN -> { //INFO键,下键
                ActionBarDialogHelper.getInstance()
                    .show(this@MainActivity, Config.getInstance().currentPlayEpg)
                return true
            }

            KeyEvent.KEYCODE_BACK -> { //epg键，返回键
                ActionBarDialogHelper.getInstance().dismiss()
                showEpg()
                return true
            }

            KeyEvent.KEYCODE_SEARCH -> {
                // 回播处理语音键
                ActivityCompat.requestPermissions(
                    this@MainActivity,
                    arrayOf(Manifest.permission.RECORD_AUDIO),
                    REQUEST_RECORD_PERMISSION
                )
                return true
            }
        }
        return false
    }

    /**
     * 直播播时的按键处理
     */
    private fun playLiveKeyEvent(keyCode: Int): Boolean {
        when (keyCode) {
            KeyEvent.KEYCODE_DPAD_CENTER, KeyEvent.KEYCODE_ENTER -> {
                if (playBackTime != 0L) {
                    if (pauseTime != 0L) {
                        val difference = System.currentTimeMillis() / 1000L - pauseTime
                        if (difference > playBackMaxTime) {
                            Log.e(
                                "kissgoodbye",
                                "超过最大时移值，则在现在的时间时移到最大支持的值的时间:$difference"
                            )
                            // 超过最大时移值，则在现在的时间时移到最大支持的值的时间
                            pauseTime = 0L
                            playBackTime = -playBackMaxTime
                            playbackHandler.sendEmptyMessage(
                                MSG_PLAYBACK
                            )
                        } else {
                            // 最大时移值内继续回播
                            Log.e("kissgoodbye", "最大时移值内继续回播:$difference")
                            pauseTime = 0L
                            PlayerHelper.getInstance().toggle()
                        }
                    } else {
                        // 在回播状态下暂停
                        Log.e("kissgoodbye", "在回播状态下暂停")
                        pauseTime = System.currentTimeMillis() / 1000L + playBackTime
                        PlayerHelper.getInstance().toggle()
                    }
                } else {
                    showChannelDialog()
                }
                return true
            }

            KeyEvent.KEYCODE_BACK -> {
                if (mCenterFragment != null && mCenterFragment!!.isVisible) {
                    // 关闭频道页面
                    hiddeChannelDialog()
                } else {
                    // 关闭App
                    saveCustomGroupConfig()
                    backPressed()
                }
                return true
            }

            KeyEvent.KEYCODE_DPAD_UP -> { //换台
                if (mCenterFragment == null || mCenterFragment!!.isHidden) mMainViewModel.upChangeChannel()
            }

            KeyEvent.KEYCODE_DPAD_DOWN -> { //换台
                if (mCenterFragment == null || mCenterFragment!!.isHidden) mMainViewModel.downChangeChannel()
            }

            KeyEvent.KEYCODE_PAGE_UP, KeyEvent.KEYCODE_DEL -> { //查看历史
                if (mCenterFragment == null || mCenterFragment!!.isHidden) mMainViewModel.watchHistoryPrevious()
            }

            KeyEvent.KEYCODE_PAGE_DOWN -> { //查看历史
                if (mCenterFragment == null || mCenterFragment!!.isHidden) mMainViewModel.watchHistoryAfter()
            }
            // 数字键换台
            in KeyEvent.KEYCODE_0..KeyEvent.KEYCODE_9 -> {
                numberChange(keyCode)
                return true
            }

//            KeyEvent.KEYCODE_INFO -> {
//                if (multipleScreen){
//                    binding.spvActivityMain.visibility = View.VISIBLE
//                    PlayerHelper.getInstance().replay()
//
//                    for (sourceLoader in sourceLoaderList){
//                        SourceLoaderPool.getInstance().release(sourceLoader)
//                    }
//                    sourceLoaderList.clear()
//
//                    multipleScreen = false
//                }else {
//                    binding.spvActivityMain.visibility = View.INVISIBLE
//                    PlayerHelper.getInstance().pause()
//                    SourceHelper.INSTANCE.cancel()
//                    multipleScreen = true
//
//                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(10)[0], binding.spvActivityMain1))
//                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(101)[0], binding.spvActivityMain2))
//                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(102)[0], binding.spvActivityMain3))
//                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(103)[0], binding.spvActivityMain4))
////                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(106)[0], binding.spvActivityMain5))
////                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(407)[0], binding.spvActivityMain6))
////                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(408)[0], binding.spvActivityMain7))
////                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(410)[0], binding.spvActivityMain8))
////                    sourceLoaderList.add(SourceLoaderPool.getInstance().acquire(DBApi.getInstance().getChannelByChannelNumber(409)[0], binding.spvActivityMain9))
//
//
//                }
//                return true
//            }

            2007, 170, KeyEvent.KEYCODE_INFO -> { //INFO键
                hiddeChannelDialog()
                mSettingDialog?.dismiss()
                mFeedbackDialogFragment?.dismiss()
                mActionBarSettingDialog?.dismiss()
                ActionBarDialogHelper.getInstance().show(this@MainActivity)
                return true
            }

            KeyEvent.KEYCODE_SEARCH -> {
                if (!recordEventFilter.filter()) {
                    return true
                }
                if (mCenterFragment != null && mCenterFragment!!.isVisible) {
                    // 关闭频道页面
                    hiddeChannelDialog()
                }
                ActivityCompat.requestPermissions(
                    this@MainActivity,
                    arrayOf(Manifest.permission.RECORD_AUDIO),
                    REQUEST_RECORD_PERMISSION
                )
                return true
            }
        }
        return false
    }

    /**
     * 关闭Channel，归还焦点
     */
    private fun hiddeChannelDialog() {
        if (mCenterFragment == null || mCenterFragment!!.isHidden) {
            return
        }
        mCenterFragment?.let {
            it.close()
            Handler(Looper.myLooper()!!).postDelayed({
                val ft = supportFragmentManager.beginTransaction()
                ft.hide(it)
                ft.commit()
            }, 200L)
        }
        Handler(Looper.myLooper()!!).postDelayed({ binding.focusCatcher.requestFocus() }, 10L)
    }

//    /**
//     * 关闭Epg，归还焦点
//     */
//    private fun hiddeEpgDialog() {
//        if (mEpgDialogFragment?.isVisible == true) {
//            // 关闭Epg
//            mEpgDialogFragment?.dismiss()
//        }
//        Handler(Looper.myLooper()!!).postDelayed({ binding.focusCatcher.requestFocus() }, 100L)
//    }

    override fun searchContent(spokenList: ArrayList<String>?) {
        val keywords = mutableListOf<String>()
        spokenList?.let {
            for (i in spokenList.indices) {
                if (!TextUtils.isEmpty(spokenList[i])) {
                    keywords.add(spokenList[i])
                    val words = spokenList[i].split(" ")
                    if (words.isNotEmpty()) {
                        for (j in words.indices) {
                            keywords.add(words[j])
                        }
                    }
                }
            }
        }
        if (keywords.isNotEmpty()) {
            mMainViewModel.searchKeywords(keywords)
        }
    }

//    @Subscribe(threadMode = ThreadMode.MAIN)
//    fun onPlaybackPlayEvent(event: PlaybackPlayEvent) {
//        val epgBean = event.epgBean
//        // 关闭Epg
//        mEpgDialogFragment?.dismiss()
//        hiddeChannelDialog()
//
//        if (epgBean.endTime!! > System.currentTimeMillis() / 1000) {
//            toast { getString(R.string.player_playback_time_not_come) }
//            return
//        }
//
//        VodPlayHelper.onPlaybackPlay(epgBean, this@MainActivity)
//    }

    //注册EventBus
    override fun isRegisterEventBus(): Boolean {
        return true
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onFeedbackDialogEvent(event: FeedbackDialogEvent) {
        if (mFeedbackDialogFragment == null) {
            mFeedbackDialogFragment = FeedbackDialogFragment()
        }
        mFeedbackDialogFragment?.show(supportFragmentManager, "mFeedbackDialogFragment")
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onSettingEvent(event: SettingEvent) {
        if (mSettingDialog == null) {
            mSettingDialog = SettingDialog(this) {
                showTextAppearanceDialog()
            }
        }
        mSettingDialog?.show()
    }

    private fun showTextAppearanceDialog() {
        if (mTextAppearanceDialog == null) {
            mTextAppearanceDialog = TextAppearanceDialog(this)
        }
        mTextAppearanceDialog?.show()
    }

//
//    @Subscribe(threadMode = ThreadMode.MAIN)
//    fun onPlayLiveEvent(event: PlayLiveEvent) {
//        hiddeChannelDialog()
//        if (mEpgDialogFragment?.isVisible == true) {
//            // 关闭Epg
//            mEpgDialogFragment?.dismiss()
//        }
//        mMainViewModel.onChannelClick()
//    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onLiveChangeEvent(event: LiveChangeEvent) {
        playBackTime = 0
        showPlaybackIcon()
    }

    //显示epg
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onActionBarSettingEpgEvent(event: ActionBarSettingEpgEvent) {
        showEpg()
    }

    fun catchFocus() {
        binding.focusCatcher.requestFocus()
    }

    override fun onDestroy() {
        super.onDestroy()
        show = false
        // 如果buffer还在，就隐藏
        ActionBarDialogHelper.getInstance().dismiss(this)
        LoadingDialogHelper.getInstance().dismiss(this)
    }

    private var show = true
    fun printFocus() {
        val run2 = Runnable {
            while (show) {
                try {
                    Thread.sleep(3000)
                    val rooter: View = this.window.decorView
                    val currentView: View? = rooter.findFocus()
                    //TAG为当前Activity名称
                    Logger.i("TAG:当前焦点所在View：$currentView")
                } catch (e: InterruptedException) {
                    e.printStackTrace()
                }
            }
        }
        Thread(run2).start()
    }

    companion object {
        private const val MSG_NUMBER_CHANGE_CHANNEL = 0x110

        private const val MSG_PLAYBACK = 0x111
    }

    /**
     * 播放频道
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onChannelPlayEvent(event: ChannelPlayEvent) {
        onProgramSelect(event.tagBean, event.channelBean, event.epgBean)
    }

    /**
     * 关闭channel
     */
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCloseChannelEvent(event: CloseChannelEvent) {
        hiddeChannelDialog()
    }

    private var mCustomGroupDialog: CustomGroupDialog? = null

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onCustomGroupEvent(event: CustomGroupEvent) {
        hiddeChannelDialog()
        if (mCustomGroupDialog == null) {
            mCustomGroupDialog = CustomGroupDialog(this@MainActivity, R.style.Dialog)
        }
        mCustomGroupDialog?.showDialog()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEditGroupEvent(event: EditGroupEvent) {
        hiddeChannelDialog()
        if (mCustomGroupDialog == null) {
            mCustomGroupDialog = CustomGroupDialog(this@MainActivity, R.style.Dialog)
        }
        mCustomGroupDialog!!.showDialog(event.customId)
    }

    var currentCustomId: Long? = null

    /**
     * 调用此方法前需要判断解锁
     */
    override fun onProgramSelect(tagBean: TagBean, channelBean: ChannelBean, epgBean: EpgBean?) {
        hiddeChannelDialog()
        if (epgBean == null) {
            // 直播
            mMainViewModel.onChannelPlay(tagBean, channelBean)
        } else {
            // 回播
            mMainViewModel.onPlaybackPlay(tagBean, channelBean, epgBean, this@MainActivity)
        }
    }

    override fun onSpeechResultClick(channelBean: ChannelBean) {
        dialog.dismissDialog()
        mMainViewModel.searchPlay(channelBean)
    }

    private fun saveCustomGroupConfig() {
        if (ContextCompat.checkSelfPermission(
                this, Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED || ContextCompat.checkSelfPermission(
                this, Manifest.permission.READ_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            val tags = DBApi.getInstance().getCustomTags()
            val channels = DBApi.getInstance().allCollectionChannel
            val map = mutableMapOf<String, Any>()
            map["tags"] = tags
            map["channels"] = channels
            FileHelper.writeJsonToFile(this, JSON.toJSONString(map))
        }
    }

    private fun showUnlockDialog(callback: UnitCallback) {
        if (mUnlockDialog == null) {
            mUnlockDialog = UnlockDialog(this@MainActivity, R.style.Dialog, callback = callback)
        } else {
            mUnlockDialog!!.callback = callback
        }

        if (!mUnlockDialog!!.isShowing) {
            mUnlockDialog!!.showDialog()
        }
    }

    fun hideFragment() {
        if (mClassicEpgFragment == null || mClassicEpgFragment!!.isHidden) {
            return
        }
        mClassicEpgFragment?.let {
            Handler(Looper.myLooper()!!).postDelayed({
                val ft = supportFragmentManager.beginTransaction()
                ft.hide(it)
                ft.commit()
            }, 200L)
        }
    }

    fun onMultiScreenClick() {
        mActionBarSettingDialog?.dismiss()
        PlayerHelper.getInstance().pause()
        SourceHelper.INSTANCE.cancel()
        startActivity(Intent(this, MultipleScreenActivity::class.java))
        finish()
    }

    fun onMultiScreen() {
        startCircleAnimation(binding.spvActivityMain)
//        // 获取根布局
//        val rootView = binding.rlActivityMain
//
//        // 获取动画的起点（左下角）
//        val startX = 0
//        val startY = rootView.height
//
//        // 获取动画的结束半径
//        val startRadius = hypot(rootView.width.toDouble(), rootView.height.toDouble()).toFloat()
//        val endRadius = 0f
//
//        // 创建收缩动画
//        val animator: Animator = ViewAnimationUtils.createCircularReveal(
//            rootView,
//            startX,
//            startY,
//            startRadius,
//            endRadius
//        )
//
//        animator.setDuration(1500)
//        animator.interpolator = AccelerateInterpolator()
//
//        // 动画结束后退出 Activity
//        animator.addListener(object : AnimatorListenerAdapter() {
//            override fun onAnimationEnd(animation: Animator) {
//                super.onAnimationEnd(animation)
//                onMultiScreenClick()
//                overridePendingTransition(0, 0) // 禁用默认动画
//            }
//        })
//
//        animator.start()
    }

    private fun startCircleAnimation(view: View) {
        // 获取屏幕宽高
        val metrics = resources.displayMetrics
        val screenWidth = metrics.widthPixels
        val screenHeight = metrics.heightPixels

        // 计算目标位置和大小
        val targetSize = 100 // 最终缩小到的宽高 (100px)
        val targetX = (screenWidth - targetSize) / 32f // 屏幕中心的 X
        val targetY = (screenHeight - targetSize) / 32f // 屏幕中心的 Y

        Log.e("========targetX", "targetX:$screenWidth")
        Log.e("========targetX", "targetY:$screenHeight")
        Log.e("========targetX", "targetY:$targetX")
        Log.e("========targetX", "targetY:$targetY")
        Log.e("========targetX", "targetY:${view.x}")
        Log.e("========targetX", "targetY:${view.y}")

        // 计算掉落到底部的位置
        val bottomY = (screenHeight - targetSize).toFloat()

        // 1. 缩小动画
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, targetSize / view.width.toFloat())
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, targetSize / view.height.toFloat())
        val moveX = ObjectAnimator.ofFloat(view, "x", view.x, targetX)
        val moveY = ObjectAnimator.ofFloat(view, "y", view.y, targetY)

        // 组合缩小动画
        val shrinkSet = AnimatorSet()
        shrinkSet.playTogether(scaleX, scaleY, moveX, moveY)
//        val rotate = ObjectAnimator.ofFloat(view, "rotation", 0f, 360f)
//        rotate.setDuration(2000)
//        shrinkSet.playTogether(scaleX, scaleY, moveX, moveY, rotate)
        shrinkSet.setDuration(2000) // 缩小动画时长

        // 2. 掉落动画
        val drop = ObjectAnimator.ofFloat(view, "y", targetY, bottomY)
        drop.setDuration(2000) // 掉落动画时长
        drop.interpolator = BounceInterpolator() // 添加弹性效果

        // 3. 动画组合
        val finalSet = AnimatorSet()
        finalSet.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animation: Animator) {}

            override fun onAnimationEnd(animation: Animator) {
                show = false
                PlayerHelper.getInstance().pause()
                // 如果buffer还在，就隐藏
                ActionBarDialogHelper.getInstance().dismiss(this@MainActivity)
                LoadingDialogHelper.getInstance().dismiss(this@MainActivity)

                onMultiScreenClick()
                overridePendingTransition(0, 0) // 禁用默认动画
            }

            override fun onAnimationCancel(animation: Animator) {}

            override fun onAnimationRepeat(animation: Animator) {}

        })
        finalSet.playSequentially(shrinkSet, drop) // 先缩小后掉落
        finalSet.start()
    }

    override fun onRestart() {
        super.onRestart()
    }

}