package com.google.chuangke.entity

import io.objectbox.annotation.Entity
import io.objectbox.annotation.Id
import java.io.Serializable

@Entity
class TagBean(
    @Id(assignable = true)
    var id: Long,
    var name: String? = null,
    var intOrder: Int? = null,
    var passwordAccess: Int? = null,
    var naLiteral: String? = null,
    var isSpecial: Int? = null,
    var custom: Int? = 0
) : Serializable