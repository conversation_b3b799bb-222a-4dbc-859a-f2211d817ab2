package com.google.chuangke.common.event

import com.google.chuangke.entity.ChannelBean
import com.google.chuangke.entity.EpgBean
import com.google.chuangke.entity.TagBean

class UserInfoEvent
class ContactInfoEvent
class AttentionInfoEvent
class NetworkChangeEvent
class PlayerStatusEvent(val code: Int, val speed: Int)
class FeedbackDialogEvent
class SettingEvent
class EpgDownLoadCompleteEvent
class VodExitEvent
class ChannelPlayEvent(val tagBean: TagBean, val channelBean: ChannelBean, val epgBean: EpgBean?)
class CloseChannelEvent
class CustomGroupEvent
class EditGroupEvent(val customId: Long)
class SearchEvent
class TracksChangedEvent
class LiveChangeEvent
class ExitEvent()