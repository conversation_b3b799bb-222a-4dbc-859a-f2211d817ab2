package com.google.chuangke

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import androidx.lifecycle.ProcessLifecycleOwner
import com.google.chuangke.common.UserHelper
import com.google.chuangke.database.ObjectBox
import com.google.chuangke.inject.InjectionApplication
import com.google.chuangke.util.FileUtil
import com.jakewharton.threetenabp.AndroidThreeTen
import com.orhanobut.logger.AndroidLogAdapter
import com.orhanobut.logger.Logger


open class MyApplication : InjectionApplication(){

    override fun onCreate() {
        super.onCreate()
        context = applicationContext

        try {
            ObjectBox.init(this)
        } catch (e: Exception) {
            FileUtil.deleteDirWihtFile(filesDir)
            ObjectBox.init(this)
        }

        AndroidThreeTen.init(this)
        initLogger()
        go2.Seq.setContext(this)

        ProcessLifecycleOwner.get().lifecycle.addObserver(object : LifecycleObserver {
            @OnLifecycleEvent(Lifecycle.Event.ON_STOP)
            fun onAppBackgrounded() {
                UserHelper.getInstance().exit()
            }

            @OnLifecycleEvent(Lifecycle.Event.ON_START)
            fun onAppForegrounded() {

            }
        })
    }

    private fun initLogger() {
        Logger.addLogAdapter(object : AndroidLogAdapter() {
            override fun isLoggable(priority: Int, tag: String?): Boolean {
                return BuildConfig.DEBUG
            }
        })
    }

    companion object {
        @SuppressLint("StaticFieldLeak")
        lateinit var context: Context
        lateinit var currentActivity: Activity
    }

}