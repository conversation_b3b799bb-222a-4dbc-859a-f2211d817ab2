package com.google.chuangke.http;


import android.annotation.SuppressLint;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;

import com.alibaba.fastjson.JSONObject;
import com.google.chuangke.MyApplication;
import com.google.chuangke.common.Config;
import com.google.chuangke.common.Constants;
import com.google.chuangke.util.SPUtils;
import com.orhanobut.logger.Logger;
import com.wochuang.json.DeviceIdUtil;
import com.wochuang.json.NativeLib;

import java.net.Proxy;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;

public class HttpHelper {
    private static HttpHelper instance;
    private final static int TIMEOUT = 10000;
    private OkHttpClient client;

    private HttpHelper() {
        HttpsUtils.SSLParams sslParams = HttpsUtils.getSslSocketFactory();
        client = new OkHttpClient.Builder()
                .connectTimeout(TIMEOUT, TimeUnit.MILLISECONDS)
                .readTimeout(TIMEOUT, TimeUnit.MILLISECONDS)
                .writeTimeout(TIMEOUT, TimeUnit.MILLISECONDS)
                .dns(new HttpDoh())
                .proxy(Proxy.NO_PROXY)
                .retryOnConnectionFailure(false)
                .sslSocketFactory(sslParams.sSLSocketFactory, sslParams.trustManager)
                .hostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String hostname, SSLSession session) {
                        return true;
                    }
                }).build();
    }

    private static final class HttpHelperHolder {
        private static final HttpHelper sInstance = new HttpHelper();
    }

    public static HttpHelper getInstance() {
        if (instance == null) {
            synchronized (HttpHelper.class) {
                instance = new HttpHelper();
            }
        }
        return instance;
    }

    public OkHttpClient getClient() {
        return client;
    }

    public void get(String url, Callback callback) {
        String token = SPUtils.Companion.getString(MyApplication.context, Constants.SP_KEY_TOKEN, null);
        if (TextUtils.isEmpty(token)) {
            token = "000";
        }
        String requestData = NativeLib.getRequestData(MyApplication.context, token, "");
        Request request = new Request.Builder()
                .url(url)
                .header("data", requestData)
                .header("clientData", getClientData())
                .header("LANG", Locale.getDefault().getLanguage())
                .get()
                .build();
        client.newCall(request).enqueue(callback);
    }


    /**
     * FormBody formBody = new FormBody.Builder()
     * .add("data", requestData)
     * .build();
     */
    public void post(String url, FormBody formBody, Callback callback) {
        if (url == null) {
            return;
        }
        Request request = new Request.Builder()
                .url(url)
                .header("LANG", Locale.getDefault().getLanguage())
                .post(formBody)
                .build();

        client.newCall(request).enqueue(callback);
    }

    public void postApi(String api, JSONObject paramJson, Callback callback) {
        postApi(api, paramJson, callback, null);
    }

    public void postApi(String api, JSONObject paramJson, Callback callback, Map<String, String> unsigns) {
        String url = Config.getInstance().getApiUrl(api);
        if (url == null) {
            return;
        }

        String token = SPUtils.Companion.getString(MyApplication.context, Constants.SP_KEY_TOKEN, null);
        if (TextUtils.isEmpty(token)) {
            token = "000";
        }
        String requestData = NativeLib.getRequestData(MyApplication.context, token, paramJson.toJSONString());
        FormBody.Builder formBodyBuilder = new FormBody.Builder();

        formBodyBuilder.add("data", requestData);
        formBodyBuilder.add("clientData", getClientData());

        if (unsigns != null) {
            for (String key : unsigns.keySet()) {
                formBodyBuilder.add(key, unsigns.get(key));
            }
        }

        FormBody formBody = formBodyBuilder.build();

        Request request = new Request.Builder()
                .url(url)
                .header("LANG", Locale.getDefault().getLanguage())
                .post(formBody)
                .build();

        client.newCall(request).enqueue(callback);
    }

    public void postApiWithoutEncrypt(String api, Map<String, String> paras, Callback callback) {
        String url = Config.getInstance().getApiUrl(api);
        if (url == null) {
            return;
        }

        FormBody.Builder formBuilder = new FormBody.Builder();

        for (String key : paras.keySet()) {
            Object value = paras.get(key) != null ? paras.get(key) : "";
            formBuilder.add(key, value.toString());
        }

        formBuilder.add("clientData", getClientData());

        Request request = new Request.Builder().url(url).header("LANG", Locale.getDefault().getLanguage()).post(formBuilder.build()).build();

        client.newCall(request).enqueue(callback);
    }

    @SuppressLint("HardwareIds")
    private static String getClientData() {
        JSONObject json = new JSONObject();
        json.put("deviceSystem", Build.VERSION.RELEASE);
        json.put("deviceName", Build.BRAND);

        try {
            String ip = SPUtils.Companion.getString(MyApplication.context, Constants.SP_KEY_IP, null);
            if (TextUtils.isEmpty(ip)) {
                ip = "{}";
            }
            json.put("ip", ip);

            PackageManager pm = MyApplication.context.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(MyApplication.context.getPackageName(), 0);
            json.put("appPackage", pi.packageName);
            json.put("appVersion", pi.versionName);
            json.put("sn", DeviceIdUtil.getSN());
            json.put("mac", DeviceIdUtil.getMac());
            json.put("cpuId", DeviceIdUtil.getCPUId());
            json.put("ssaId", Settings.Secure.getString(MyApplication.context.getContentResolver(), Settings.Secure.ANDROID_ID));
        } catch (Exception e) {
            Logger.e(""+e.getMessage());
        }
        return NativeLib.getRequestData(MyApplication.context, "000", json.toJSONString());
    }

    public void release() {
        HttpHelper.instance = null;
    }

}
