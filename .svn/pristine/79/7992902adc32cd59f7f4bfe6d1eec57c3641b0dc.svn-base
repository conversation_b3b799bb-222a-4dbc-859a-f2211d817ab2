package com.google.chuangke.util

import android.content.Context

object ScreenUtils {
    /**
     * PX 转换为 DP
     *
     * @param context
     * @param px
     * @return
     */
    fun px2dp(context: Context, px: Int): Int {
        val scale: Float = context.resources.displayMetrics.density
        return (px / scale + 0.5f).toInt()
    }

    /**
     * DP 转换为 PX
     *
     * @param context
     * @param dp
     * @return
     */
    fun dp2px(context: Context, dp: Int): Int {
        val scale: Float = context.resources.displayMetrics.density
        return (dp * scale + 0.5f).toInt()
    }
}