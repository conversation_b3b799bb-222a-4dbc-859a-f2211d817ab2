{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:6241335490986731672", "lastPropertyId": "12:1943556879350015328", "name": "ChannelBean", "properties": [{"id": "1:4732573108279725636", "name": "id", "type": 6, "flags": 129}, {"id": "2:4633971980326774723", "name": "channelId", "indexId": "1:1753835657228930808", "type": 6, "flags": 8}, {"id": "3:386291751525296525", "name": "name", "type": 9}, {"id": "4:2478770049867382289", "name": "channelNumber", "indexId": "2:3583128404456829196", "type": 5, "flags": 8}, {"id": "5:3765945566191151106", "name": "tags", "type": 9}, {"id": "6:7946495567979702907", "name": "intOrder", "type": 5}, {"id": "7:2739722494315178637", "name": "playback", "type": 5}, {"id": "8:6858674760488104828", "name": "countryCode", "type": 9}, {"id": "9:5506184873987291927", "name": "uid", "type": 9}, {"id": "10:1231434953115661025", "name": "wid", "type": 9}, {"id": "11:2686021379541363884", "name": "image", "type": 9}, {"id": "12:1943556879350015328", "name": "content", "type": 9}], "relations": []}, {"id": "2:5115214211873119506", "lastPropertyId": "4:782816789036005523", "name": "ChannelCollectionBean", "properties": [{"id": "1:7626368641964413930", "name": "id", "type": 6, "flags": 1}, {"id": "2:2539572676082925065", "name": "unid", "type": 6}, {"id": "3:1765373788036477145", "name": "createTime", "type": 5}, {"id": "4:782816789036005523", "name": "ctId", "type": 6}], "relations": []}, {"id": "3:8125627504308358359", "lastPropertyId": "3:2236351478704054277", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>ean", "properties": [{"id": "1:2027846441523249247", "name": "id", "type": 6, "flags": 1}, {"id": "2:3627549494103173560", "name": "unid", "indexId": "3:2655545574400948150", "type": 6, "flags": 8}, {"id": "3:2236351478704054277", "name": "createTime", "type": 5}], "relations": []}, {"id": "4:8947480283607633230", "lastPropertyId": "6:568731981182901214", "name": "EpgBean", "properties": [{"id": "1:8936653167923849396", "name": "id", "type": 6, "flags": 129}, {"id": "2:3613502387124996688", "name": "channelId", "indexId": "4:8670434611729252487", "type": 5, "flags": 8}, {"id": "3:1289414522843830526", "name": "name", "type": 9}, {"id": "4:5904639104382110154", "name": "description", "type": 9}, {"id": "5:3860377714213336532", "name": "beginTime", "type": 5}, {"id": "6:568731981182901214", "name": "endTime", "type": 5}], "relations": []}, {"id": "5:1960286631156485590", "lastPropertyId": "7:2175477336115657467", "name": "TagBean", "properties": [{"id": "1:2247517623934104223", "name": "id", "type": 6, "flags": 129}, {"id": "2:4443476421464335196", "name": "name", "type": 9}, {"id": "3:7088000638583308732", "name": "intOrder", "type": 5}, {"id": "4:5775648030860568640", "name": "passwordAccess", "type": 5}, {"id": "5:3279628858842995179", "name": "naLiteral", "type": 9}, {"id": "6:4962853083835993161", "name": "isSpecial", "type": 5}, {"id": "7:2175477336115657467", "name": "custom", "type": 5}], "relations": []}, {"id": "6:3345492038620439138", "lastPropertyId": "4:4147510027463079974", "name": "CustomTagBean", "properties": [{"id": "1:929171758922682489", "name": "id", "type": 6, "flags": 129}, {"id": "2:5866635392573500803", "name": "name", "type": 9}, {"id": "3:1193969798703997241", "name": "edit", "type": 5}, {"id": "4:4147510027463079974", "name": "createTime", "type": 5}], "relations": []}], "lastEntityId": "6:3345492038620439138", "lastIndexId": "4:8670434611729252487", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [], "retiredPropertyUids": [], "retiredRelationUids": [], "version": 1}